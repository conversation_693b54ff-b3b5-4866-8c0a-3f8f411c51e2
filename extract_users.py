#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
提取用户ID脚本

从rawdata目录中选择JSON文件，提取所有用户ID，并将其保存到Markdown文件中。
输出格式为"原文件名-userlist.md"，内容仅包含从1到N的用户名列表。
输出文件保存在rawdata目录中。
"""

import json
import os
import glob
import readline
import platform


def get_json_files():
    """获取rawdata目录下的所有json文件"""
    data_dir = os.path.join(os.path.dirname(
        os.path.abspath(__file__)), 'rawdata')
    files = glob.glob(os.path.join(data_dir, '*.json'))
    return [os.path.basename(f) for f in files]


def completer(text, state):
    """用于自动补全的函数"""
    json_files = get_json_files()
    matches = [f for f in json_files if f.startswith(text)]
    if state < len(matches):
        return matches[state]
    else:
        return None


def select_json_file_smart():
    """智能文件选择 - 增强的自动补全和预设默认值，最新文件优先显示"""
    json_files = get_json_files()
    if not json_files:
        print("错误: rawdata目录中没有找到任何JSON文件")
        print("请检查以下内容:")
        print("1. 确保 'rawdata' 目录存在")
        print("2. 确保 'rawdata' 目录中包含至少一个 .json 文件")
        return None

    # 设置自动补全
    readline.set_completer(completer)

    # 根据操作系统设置不同的补全方式
    if platform.system() == 'Darwin':  # macOS
        readline.parse_and_bind("bind ^I rl_complete")
    else:
        readline.parse_and_bind("tab: complete")

    # 显示文件列表，用彩色高亮最新的文件
    print("\n可用的JSON文件:")

    # 按修改时间排序文件
    data_dir = os.path.join(os.path.dirname(
        os.path.abspath(__file__)), 'rawdata')
    sorted_files = sorted(
        [(f, os.path.getmtime(os.path.join(data_dir, f))) for f in json_files],
        key=lambda x: x[1],
        reverse=True  # 最新的文件排在前面
    )

    # 设置最新文件为默认选择
    default_file = sorted_files[0][0] if sorted_files else None

    # 显示文件列表，最新的文件带星号标记
    for i, (f, mtime) in enumerate(sorted_files, 1):
        if i == 1:  # 最新文件
            print(f"  {i}. \033[1m{f}\033[0m (最新，推荐)")  # 粗体显示
        else:
            print(f"  {i}. {f}")

    # 询问用户是否使用默认文件
    if default_file:
        choice = input(
            f"\n按回车键选择默认文件 [{default_file}] 或输入编号(1-{len(sorted_files)}): ")

        if choice.strip() == "":
            # 用户按回车，使用默认文件
            return os.path.join('rawdata', default_file)

        # 尝试解析数字选择
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(sorted_files):
                return os.path.join('rawdata', sorted_files[idx][0])
        except ValueError:
            # 不是数字，尝试作为文件名处理
            if choice in json_files:
                return os.path.join('rawdata', choice)

        # 如果以上都不匹配，回到手动输入
        print("无效选择，请手动输入文件名:")

    # 手动输入模式
    while True:
        prompt = "\n请输入要处理的文件名 (按Tab键自动补全"
        if default_file:
            prompt += f"，默认为 {default_file}"
        prompt += "): "

        file_name = input(prompt)

        # 如果输入为空且有默认值，使用默认值
        if file_name.strip() == "" and default_file:
            return os.path.join('rawdata', default_file)

        if file_name in json_files:
            return os.path.join('rawdata', file_name)
        else:
            print(f"文件 '{file_name}' 不存在，请重新输入")


def extract_user_ids(json_file):
    """从JSON文件中提取用户ID列表"""
    try:
        # 打开并读取JSON文件
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 提取所有用户ID
        users = []
        for user in data.get("users", []):
            user_id = user.get("user_id", "未知用户")
            users.append(user_id)

        return users
    except Exception as e:
        print(f"提取用户ID时发生错误: {e}")
        return []


def save_to_markdown(users, output_file):
    """将用户ID列表保存到Markdown文件，仅包含从1到N的用户名列表"""
    try:
        # 构建Markdown内容 - 只包含从1到N的用户名列表
        markdown_content = ""
        for i, user_id in enumerate(users, 1):
            markdown_content += f"{i}. {user_id}\n"

        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        return True
    except Exception as e:
        print(f"保存Markdown文件时发生错误: {e}")
        return False


def main():
    """主函数"""
    print("=== 用户列表提取工具 ===")

    # 让用户选择JSON文件
    json_file = select_json_file_smart()
    if not json_file:
        return

    # 提取基本文件名（不含扩展名）
    base_name = os.path.splitext(os.path.basename(json_file))[0]

    # 设置输出文件路径在rawdata目录中
    output_file = os.path.join('rawdata', f"{base_name}_userlist.md")

    # 提取用户ID
    print(f"正在从 {json_file} 提取用户ID...")
    users = extract_user_ids(json_file)

    if not users:
        print("未找到任何用户ID")
        return

    # 保存到Markdown文件
    print(f"正在将 {len(users)} 个用户ID保存到 {output_file}...")
    if save_to_markdown(users, output_file):
        print(f"用户列表已成功保存到: {output_file}")
        print(f"统计结果: 共有 {len(users)} 位用户参与")


if __name__ == "__main__":
    main()
