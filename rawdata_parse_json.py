import re
import json
import os
import glob
import readline
import platform
import unicodedata
from collections import defaultdict

# 尝试导入tkinter，如果不可用则跳过
try:
    import tkinter as tk
    from tkinter import filedialog
    TKINTER_AVAILABLE = True
except ImportError:
    TKINTER_AVAILABLE = False


def get_data_files():
    """获取rawdata目录下的所有txt文件"""
    data_dir = 'rawdata'
    if not os.path.exists(data_dir):
        os.makedirs(data_dir, exist_ok=True)
    files = glob.glob(os.path.join(data_dir, '*.txt'))
    return [os.path.basename(f) for f in files]


def completer(text, state):
    """用于自动补全的函数"""
    data_files = get_data_files()
    matches = [f for f in data_files if f.startswith(text)]
    if state < len(matches):
        return matches[state]
    else:
        return None


def select_file_manual():
    """手动输入文件名，带自动补全功能"""
    data_files = get_data_files()
    if not data_files:
        print("数据目录中没有找到任何txt文件")
        return None

    # 设置自动补全
    readline.set_completer(completer)
    readline.parse_and_bind("tab: complete")

    print("可用的数据文件:")
    for i, f in enumerate(data_files, 1):
        print(f"  {f}")

    while True:
        file_name = input("\n请输入要处理的文件名 (按Tab键自动补全): ")
        if file_name in data_files:
            return os.path.join('rawdata', file_name)
        else:
            print(f"文件 '{file_name}' 不存在，请重新输入")


def select_file_dropdown():
    """通过下拉框选择文件"""
    data_files = get_data_files()
    if not data_files:
        print("数据目录中没有找到任何txt文件")
        return None

    print("可用的数据文件:")
    for i, f in enumerate(data_files, 1):
        print(f"{i}. {f}")

    while True:
        try:
            choice = input("\n请输入文件对应的编号: ")
            idx = int(choice) - 1
            if 0 <= idx < len(data_files):
                return os.path.join('rawdata', data_files[idx])
            else:
                print(f"无效的编号，请输入1-{len(data_files)}之间的数字")
        except ValueError:
            print("请输入有效的数字")


def select_file_dialog():
    """使用GUI文件对话框选择文件"""
    if not TKINTER_AVAILABLE:
        print("错误: 图形界面组件(tkinter)不可用。请安装tkinter或选择其他文件选择方式。")
        return None

    # 创建一个隐藏的tkinter根窗口
    root = tk.Tk()
    root.withdraw()

    # 使用相对路径
    data_dir = os.path.abspath('rawdata')

    # 打开文件对话框，初始目录设为rawdata文件夹
    file_path = filedialog.askopenfilename(
        title="选择要处理的数据文件",
        initialdir=data_dir,
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
    )

    # 如果用户选择了文件，返回文件路径
    if file_path:
        return file_path
    return None


def select_file_smart():
    """智能文件选择 - 增强的自动补全和预设默认值"""
    data_files = get_data_files()
    if not data_files:
        print("数据目录中没有找到任何txt文件")
        return None

    # 设置自动补全
    readline.set_completer(completer)

    # 根据操作系统设置不同的补全方式
    if platform.system() == 'Darwin':  # macOS
        readline.parse_and_bind("bind ^I rl_complete")
    else:
        readline.parse_and_bind("tab: complete")

    # 显示文件列表，用彩色高亮最新的文件
    print("\n可用的数据文件:")

    # 按修改时间排序文件
    data_dir = 'rawdata'
    sorted_files = sorted(
        [(f, os.path.getmtime(os.path.join(data_dir, f))) for f in data_files],
        key=lambda x: x[1],
        reverse=True  # 最新的文件排在前面
    )

    # 设置最新文件为默认选择
    default_file = sorted_files[0][0] if sorted_files else None

    # 显示文件列表，最新的文件带星号标记
    for i, (f, mtime) in enumerate(sorted_files, 1):
        if i == 1:  # 最新文件
            print(f"  {i}. \033[1m{f}\033[0m (最新，推荐)")  # 粗体显示
        else:
            print(f"  {i}. {f}")

    # 询问用户是否使用默认文件
    if default_file:
        choice = input(
            f"\n按回车键选择默认文件 [{default_file}] 或输入编号(1-{len(sorted_files)}): ")

        if choice.strip() == "":
            # 用户按回车，使用默认文件
            return os.path.join('rawdata', default_file)

        # 尝试解析数字选择
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(sorted_files):
                return os.path.join('rawdata', sorted_files[idx][0])
        except ValueError:
            # 不是数字，尝试作为文件名处理
            if choice in data_files:
                return os.path.join('rawdata', choice)

        # 如果以上都不匹配，回到手动输入
        print("无效选择，请手动输入文件名:")

    # 手动输入模式
    while True:
        prompt = "\n请输入要处理的文件名 (按Tab键自动补全"
        if default_file:
            prompt += f"，默认为 {default_file}"
        prompt += "): "

        file_name = input(prompt)

        # 如果输入为空且有默认值，使用默认值
        if file_name.strip() == "" and default_file:
            return os.path.join('rawdata', default_file)

        if file_name in data_files:
            return os.path.join('rawdata', file_name)
        else:
            print(f"文件 '{file_name}' 不存在，请重新输入")


def process_reading_data(file_path):
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 移除第一行"#接龙"和月份读书分享
    month_pattern = r'^#接龙\s*\n\d+月读书分享\s*\n'
    content = re.sub(month_pattern, '', content)

    # 使用正则表达式提取条目
    # 每个条目以数字编号开头，后面跟着用户名和书籍信息
    entries = re.findall(
        r'(\d+)\.\s+(.*?)(?=\n\d+\.\s+|\Z)', content, re.DOTALL)

    # 用户数据存储
    user_books = defaultdict(list)

    # 定义需要标准化的用户名模式
    user_patterns = {
        r'小菜鸡\s*读完': '小菜鸡',
        r'小菜鸡\s*在读': '小菜鸡',
        r'小菜鸡': '小菜鸡'
    }

    for entry_num, entry_content in entries:
        # 提取用户ID
        user_match = re.match(r'([^，。：\n]+)', entry_content.strip())
        if not user_match:
            continue

        original_user_id = user_match.group(1).strip()

        # 处理特殊情况 - 先尝试解析用户ID和书名
        user_id, books = process_entry_with_space(
            original_user_id, entry_content)

        if user_id and books:
            # 成功解析了用户ID和书籍，直接添加到结果并继续处理下一条
            user_books[user_id].extend(books)
            continue

        # 如果没有通过特殊处理获得用户ID和书籍，则按照原来的方式处理
        user_id = extract_user_id(original_user_id)

        # 标准化用户ID
        for pattern, replacement in user_patterns.items():
            if re.match(pattern, user_id):
                user_id = replacement
                break

        # 记录读书状态信息
        reading_status = None
        if "读完" in original_user_id:
            reading_status = "读完"
        elif "在读" in original_user_id:
            reading_status = "在读"

        # 移除用户ID，只保留书籍信息
        books_content = entry_content[len(user_match.group(0)):].strip()

        # 处理不同的书籍格式
        if "读完：" in books_content or "读完:" in books_content or reading_status == "读完":
            # 使用正则表达式提取"读完："后的书籍
            if "读完：" in books_content or "读完:" in books_content:
                books_part = re.split(r'读完[：:]', books_content, 1)[1]
            else:
                books_part = books_content

            books = extract_books(books_part)
            for book in books:
                book['reading_status'] = "读完"
                book['progress'] = "100%"
                # 添加默认分类
                book['category'] = assign_default_category(book['title'])

        elif "在读：" in books_content or "在读:" in books_content or reading_status == "在读":
            # 使用正则表达式提取"在读："后的书籍
            if "在读：" in books_content or "在读:" in books_content:
                books_part = re.split(r'在读[：:]', books_content, 1)[1]
            else:
                books_part = books_content

            books = extract_books(books_part)
            for book in books:
                book['reading_status'] = "在读"
                # 进度需要从书名中提取
                extract_progress(book)
                # 添加默认分类
                book['category'] = assign_default_category(book['title'])
        else:
            # 直接提取书籍列表
            books = extract_books(books_content)
            for book in books:
                extract_progress(book)
                # 添加默认分类
                book['category'] = assign_default_category(book['title'])

        # 将书籍添加到用户的阅读列表
        user_books[user_id].extend(books)

    # 处理结果
    result = []
    total_books = 0
    for user_id, books in user_books.items():
        result.append({
            "user_id": user_id,
            "books": books,
            "total_books": len(books)
        })
        total_books += len(books)

    # 添加统计信息
    stats = {
        "total_participants": len(user_books),
        "total_books": total_books
    }

    return result, stats


def process_entry_with_space(original_user_id, entry_content):
    """处理包含空格的条目，尝试解析出用户ID和书籍"""
    # 检查是否有空格
    space_split = original_user_id.split(' ', 1)
    if len(space_split) <= 1:
        return None, None

    # 有空格，第一部分是用户ID
    user_id = space_split[0].strip()
    rest_content = space_split[1].strip()

    books = []
    reading_status = "未知"

    # 检查剩余内容中是否包含书名号
    if '《' in rest_content and '》' in rest_content:
        # 尝试提取书名号中的内容作为书名
        book_titles = re.findall(r'《([^》]+)》', rest_content)
        if book_titles:
            for title in book_titles:
                # 提取阅读状态
                if "已读完" in rest_content or "读完" in rest_content:
                    reading_status = "读完"
                elif "在读" in rest_content:
                    reading_status = "在读"

                books.append({
                    "title": title,
                    "reading_status": reading_status,
                    "progress": "100%" if reading_status == "读完" else None,
                    "category": assign_default_category(title)
                })
    else:
        # 没有书名号，尝试把空格后的内容作为书名
        # 检查是否有多个空格分隔的内容，如果有，第一个可能是书名
        parts = rest_content.split()
        if parts:
            book_title = parts[0]

            # 检查阅读状态
            if any(status in rest_content for status in ["已读完", "读完"]):
                reading_status = "读完"
                # 如果状态标记在书名中，移除它
                for status in ["已读完", "读完"]:
                    if status in book_title:
                        book_title = book_title.replace(status, "").strip()
            elif "在读" in rest_content:
                reading_status = "在读"
                if "在读" in book_title:
                    book_title = book_title.replace("在读", "").strip()

            books.append({
                "title": book_title,
                "reading_status": reading_status,
                "progress": "100%" if reading_status == "读完" else None,
                "category": assign_default_category(book_title)
            })

    # 检查是否有其他书籍信息（在原始条目中但不在用户ID部分）
    remaining_content = entry_content.replace(original_user_id, "").strip()
    if remaining_content:
        additional_books = extract_books(remaining_content)
        for book in additional_books:
            # 避免重复添加相同书籍
            if not any(b['title'] == book['title'] for b in books):
                book['reading_status'] = reading_status
                book['progress'] = "100%" if reading_status == "读完" else None
                book['category'] = assign_default_category(book['title'])
                books.append(book)

    return (user_id, books) if books else (None, None)


def extract_books(books_content):
    """从文本中提取书籍列表"""
    books = []

    # 多种可能的分隔模式
    # 1. 编号前缀 (📖1, 📖2)
    numbered_books = re.findall(r'📖\d+《([^》]+)》', books_content)
    for book_name in numbered_books:
        books.append({"title": book_name})

    # 2. 直接《书名》格式
    titled_books = re.findall(r'《([^》]+)》', books_content)
    for book_name in titled_books:
        # 避免重复添加已经处理过的编号书籍
        if not any(book['title'] == book_name for book in books):
            books.append({"title": book_name})

    # 3. 英文书名格式(不带书名号)
    # 这部分比较复杂，可能需要启发式方法
    english_books = re.findall(r'([A-Z][a-zA-Z\s\':#\d-]+)', books_content)
    for book_name in english_books:
        book_name = book_name.strip()
        # 过滤掉可能的非书名
        if len(book_name.split()) > 1 and book_name not in ["Magic Tree House"]:
            books.append({"title": book_name})

    return books


def extract_progress(book):
    """从书名中提取并分离阅读进度信息"""
    # 检查书名中是否包含进度信息
    progress_match = re.search(r'(\d+)/(\d+)$', book['title'])
    if progress_match:
        current, total = progress_match.groups()
        # 移除进度信息
        book['title'] = re.sub(r'\s*\d+/\d+$', '', book['title']).strip()
        # 计算百分比
        try:
            percentage = round(int(current) / int(total) * 100)
            book['progress'] = f"{percentage}%"
            book['reading_status'] = "在读"
        except:
            book['progress'] = None
            book['reading_status'] = "未知"
    else:
        # 检查百分比格式
        percent_match = re.search(r'(\d+)%$', book['title'])
        if percent_match:
            book['progress'] = f"{percent_match.group(1)}%"
            book['title'] = re.sub(r'\s*\d+%$', '', book['title']).strip()
            book['reading_status'] = "在读"
        else:
            # 没有明确的进度信息
            book['progress'] = None
            book['reading_status'] = "未知"


def assign_default_category(book_title):
    """为书籍分配默认分类"""
    # 根据书名中的关键词简单匹配分类
    # 这里返回的是一个列表，以支持多个分类标签

    # 一些简单的匹配规则
    if "哲学" in book_title or "沉思" in book_title or "伦理" in book_title:
        return ["哲学类", "西方哲学"]
    elif "诗" in book_title or "散文" in book_title:
        return ["文学类", "诗歌"]
    elif "红楼梦" in book_title:
        return ["文学类", "古典小说"]
    elif "物理" in book_title or "量子" in book_title:
        return ["科学技术类", "物理学"]
    elif "心理" in book_title:
        return ["心理学类"]
    elif "社会" in book_title or "政治" in book_title:
        return ["社会科学类"]
    elif "历史" in book_title:
        return ["历史类"]
    # 默认分类
    return ["未分类"]


def extract_user_id(original_user_id):
    """从原始用户ID中提取纯用户名，去除书名和阅读状态"""
    # 常见的分隔模式，用于识别用户名和书籍信息的分隔
    patterns = [
        r'《.*?》',            # 匹配《书名》
        r'在读.*?《.*?》',     # 匹配"在读《书名》"
        r'读完.*?《.*?》',     # 匹配"读完《书名》"
        r'\d+/\d+',           # 匹配进度如"123/456"
        r'\d+%',              # 匹配百分比进度
        r'已读完',             # 匹配"已读完"状态
        r'读完',               # 匹配"读完"状态
        r'在读',               # 匹配"在读"状态
    ]

    # 特殊情况处理 - 明确知道的特殊情况
    special_cases = {
        "Jerome勖 人类群星闪耀时": "Jerome勖",
        "岁月如梭小红红                                                   简爱": "岁月如梭小红红",
        "黄山大力士 一月": "黄山大力士",
        "𝗦𝗜𝗦𝗬𝗣𝗛𝗨𝗦": "SISYPHUS",  # 特殊编码的西西弗斯
    }

    # 检查是否为特殊情况
    for case, replacement in special_cases.items():
        if original_user_id == case:
            return replacement

    # 检查是否包含书名号，如果有，取第一个书名号前的内容作为用户ID
    if '《' in original_user_id:
        user_id = original_user_id.split('《')[0].strip()
    else:
        user_id = original_user_id.strip()

    # 移除用户名后面的作者名和其他信息
    for author in ['肖霍洛夫', '奥斯特洛夫斯基', '果戈里', '冯尔康', '张一兵', '台湾商务印书馆']:
        if author in user_id:
            user_id = user_id.split(author)[0].strip()

    # 按照第一原则：用户名里不包含空格
    # 如果用户名中有空格，取第一个空格前的内容作为用户ID
    if ' ' in user_id:
        user_id = user_id.split(' ')[0].strip()

    # 移除空格和特定标记
    user_id = user_id.rstrip(' 、')

    # 处理特殊情况
    if '英语第' in user_id:
        user_id = user_id.split('英语第')[0].strip()

    # 移除末尾可能出现的阅读状态
    reading_statuses = ['已读完', '读完', '在读']
    for status in reading_statuses:
        if user_id.endswith(status):
            user_id = user_id[:-len(status)].strip()

    # 处理特殊字符
    user_id = normalize_unicode_chars(user_id)

    return user_id.strip()


def normalize_unicode_chars(text):
    """将特殊Unicode字符转换为标准ASCII字符"""
    # 特殊字符映射
    special_char_map = {
        # 样式化字母映射 (如数学粗体符号等)
        '𝗦': 'S', '𝗜': 'I', '𝗬': 'Y', '𝗣': 'P', '𝗛': 'H', '𝗨': 'U',
        'ଲ': '',  # 移除奥里亚文字符
        # 可以添加更多映射...
    }

    # 首先应用特定字符映射
    for special, normal in special_char_map.items():
        text = text.replace(special, normal)

    # 然后进行通用Unicode标准化（将组合字符转换为标准形式）
    text = unicodedata.normalize('NFKD', text)

    # 过滤掉控制字符和不可见字符
    text = ''.join(
        c for c in text if not unicodedata.category(c).startswith('C'))

    return text


def main():
    print("欢迎使用读书分享数据处理工具！")
    print("=" * 50)

    # 获取文件列表
    data_files = get_data_files()
    if not data_files:
        print("错误: 数据目录中没有找到任何txt文件")
        return

    # 如果只有一个文件，自动选择该文件
    if len(data_files) == 1:
        file_path = os.path.join('rawdata', data_files[0])
        print(f"数据目录中只有一个文件，自动选择: {data_files[0]}")
    else:
        # 选择输入方式
        print("请选择文件选择方式：")
        print("1. 智能选择 (推荐，带默认值和自动补全)")
        print("2. 下拉菜单选择")
        if TKINTER_AVAILABLE:
            print("3. 图形化文件选择器")

        while True:
            try:
                choice = input("\n请输入选择 (直接按回车使用智能选择): ")

                # 默认选择智能选择模式
                if choice.strip() == "":
                    file_path = select_file_smart()
                    break

                choice = int(choice)
                if choice == 1:
                    file_path = select_file_smart()
                    break
                elif choice == 2:
                    file_path = select_file_dropdown()
                    break
                elif choice == 3 and TKINTER_AVAILABLE:
                    file_path = select_file_dialog()
                    break
                else:
                    print(f"无效的选择，请输入1, 2" + (" 或 3" if TKINTER_AVAILABLE else ""))
            except ValueError:
                print("请输入有效的数字")

    if not file_path:
        print("未选择文件，程序退出")
        return

    # 处理选择的文件
    print(f"\n正在处理文件: {file_path}")
    data, stats = process_reading_data(file_path)

    # 生成输出文件名
    base_name = os.path.basename(file_path)
    # 改为只使用原文件名作为输出文件名
    output_file = base_name.replace('.txt', '.json')
    # 获取rawdata目录路径
    rawdata_dir = os.path.dirname(file_path)
    # 将输出文件保存在rawdata相同目录
    output_path = os.path.join(rawdata_dir, output_file)

    # 将结果输出为JSON
    with open(output_path, 'w', encoding='utf-8') as f:
        # 添加统计信息到输出结果
        output_data = {
            "stats": stats,
            "users": data
        }
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    print(f"处理完成，共提取了{len(data)}个用户的阅读数据")
    print(f"总计参与人数: {stats['total_participants']}人")
    print(f"总计阅读书籍: {stats['total_books']}本")
    print(f"结果已保存到: {output_path}")


if __name__ == "__main__":
    main()
