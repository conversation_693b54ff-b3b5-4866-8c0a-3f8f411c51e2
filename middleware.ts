import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

// 需要保护的路径
const PROTECTED_ROUTES = ["/import-data", "/data-inspector", "/admin", "/system-config"]
// 不需要保护的路径
const PUBLIC_ROUTES = ["/login"]

// 用于跟踪应用是否已经启动
let isAppStarted = false

export async function middleware(req: NextRequest) {
  // 获取请求的路径
  const path = req.nextUrl.pathname

  // 如果是登录页面，直接允许访问
  if (path === "/login") {
    return NextResponse.next()
  }

  // 检查是否是受保护的路径
  const isProtectedRoute = PROTECTED_ROUTES.some((route) => path === route || path.startsWith(`${route}/`))

  // 如果不是受保护的路径，直接允许访问
  if (!isProtectedRoute) {
    // 应用启动时触发缓存预热
    if (!isAppStarted && !path.startsWith("/api/")) {
      isAppStarted = true

      // 使用setTimeout避免阻塞请求
      setTimeout(async () => {
        try {
          // 触发缓存预热
          await fetch(`${req.nextUrl.origin}/api/cache/warmup`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ force: false }),
          })
        } catch (error) {
          console.error("Failed to trigger cache warmup:", error)
        }
      }, 1000)
    }

    return NextResponse.next()
  }

  // 检查是否在预览模式
  const isPreviewMode =
    req.nextUrl.hostname.includes("vercel.app") ||
    req.nextUrl.hostname.includes("localhost") ||
    req.nextUrl.hostname.includes("127.0.0.1") ||
    req.nextUrl.hostname.includes("vusercontent.net") ||
    req.nextUrl.searchParams.has("preview")

  // 在预览模式下，允许访问受保护的路由
  if (isPreviewMode) {
    console.log("Preview mode detected, bypassing authentication check")
    return NextResponse.next()
  }

  try {
    // 这里应该检查身份验证状态
    // 但在预览模式下，我们已经绕过了这个检查
    // 在实际环境中，这里应该检查Supabase会话

    // 如果没有会话且是受保护的路由，重定向到登录页面
    // 由于我们无法在中间件中访问localStorage，所以这里简化处理
    // 在实际环境中，应该检查cookie或其他会话标识

    // 简单起见，我们假设用户未登录，重定向到登录页面
    const redirectUrl = new URL("/login", req.url)
    redirectUrl.searchParams.set("redirectTo", path)
    return NextResponse.redirect(redirectUrl)
  } catch (error) {
    console.error("Middleware error:", error)
    // 如果出错，重定向到登录页面
    const redirectUrl = new URL("/login", req.url)
    redirectUrl.searchParams.set("redirectTo", path)
    return NextResponse.redirect(redirectUrl)
  }
}

export const config = {
  matcher: ["/admin/:path*", "/import-data/:path*", "/data-inspector/:path*", "/login", "/system-config"],
}
