import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { createClient } from "@supabase/supabase-js"

// 需要保护的路径
const PROTECTED_ROUTES = ["/import-data", "/data-inspector", "/admin", "/admin-reports", "/system-config"]

export async function middleware(req: NextRequest) {
  // 获取请求的路径
  const path = req.nextUrl.pathname

  // 检查是否是受保护的路径
  const isProtectedRoute = PROTECTED_ROUTES.some((route) => path === route || path.startsWith(`${route}/`))

  // 如果不是受保护的路径，直接允许访问
  if (!isProtectedRoute) {
    return NextResponse.next()
  }

  // 不再使用预览模式，所有访问都需要正常认证

  try {
    // 从cookie中获取会话信息
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

    if (!supabaseUrl || !supabaseKey) {
      console.error("Missing Supabase credentials in middleware")
      return NextResponse.next() // 在缺少凭据的情况下继续，避免阻塞
    }

    // 创建Supabase客户端
    const supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: false, // 不要在localStorage中保存会话
        autoRefreshToken: false, // 不要自动刷新令牌
      },
    })

    // 从cookie中获取会话
    const authCookie = req.cookies.get("sb-auth-token")?.value

    if (authCookie) {
      // 如果找到auth cookie，则允许访问
      return NextResponse.next()
    }

    // 如果没有会话且是受保护的路由，重定向到登录页面
    const redirectUrl = new URL("/login", req.url)
    redirectUrl.searchParams.set("redirectTo", path)
    return NextResponse.redirect(redirectUrl)
  } catch (error) {
    console.error("Middleware error:", error)
    // 如果出错，允许请求继续，避免阻塞用户
    return NextResponse.next()
  }
}

export const config = {
  matcher: [
    "/admin/:path*",
    "/admin-reports",
    "/import-data/:path*",
    "/data-inspector/:path*",
    "/login",
    "/system-config",
  ],
}
