#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新书籍分类脚本

根据书籍名称和预定义的分类规则，更新JSON文件中的书籍分类信息。
分类规则基于 book-categories.md 中定义的分类系统。
"""

import json
import re
import os
from typing import List, Dict, Any


def load_json_file(file_path: str) -> Dict[str, Any]:
    """加载JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def save_json_file(data: Dict[str, Any], file_path: str) -> None:
    """保存JSON文件"""
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def categorize_book(title: str) -> List[str]:
    """根据书名和关键词匹配对书籍进行分类"""
    # 定义分类规则：分类名称 -> [关键词列表]
    category_rules = {
        "文学类": {
            "小说": ["小说", "故事", "红楼梦", "三国演义", "金瓶梅", "战争与和平", "1984", "追忆似水年华",
                   "魔山", "沙丘", "女友", "涅朵奇卡", "乌托邦", "人生海海", "罪与罚", "局外人",
                   "鼠疫", "神话", "安提戈涅", "李尔王", "哈姆雷特", "我的天才女友", "新名字的故事",
                   "欧维", "地坛", "漫步", "徒", "荒谬", "海海", "顿河", "愤怒的葡萄", "潘金莲"],
            "诗歌": ["诗", "诗歌", "诗学"],
            "戏剧": ["戏剧", "悲剧", "喜剧", "哈姆雷特", "李尔王", "安提戈涅"],
            "散文": ["散文", "游记", "漫步", "随笔", "叙事"],
            "传记": ["传记", "自传", "回忆录", "雍正传"],
            "古典": ["古典", "红楼梦", "三国演义", "金瓶梅", "论语", "古代"]
        },
        "哲学类": {
            "西方哲学": ["哲学", "存在", "形而上", "尼采", "维特根斯坦", "海德格尔", "苦论", "沉思",
                     "黑格尔", "西西弗", "柏拉图", "逻辑哲学", "第一哲学"],
            "东方哲学": ["东方哲学", "道家", "易经", "南怀瑾", "论语"],
            "伦理学": ["伦理", "道德", "人生的意义", "苦论"],
            "形而上学": ["形而上", "存在"],
            "逻辑学": ["逻辑", "思维", "思考", "刘易斯逻辑"],
            "美学": ["美学", "审美", "艺术哲学"]
        },
        "心理学类": {
            "心理学": ["心理", "意识", "潜意识", "自我", "精神分析"],
            "发展心理学": ["发展心理", "儿童心理", "青少年心理"],
            "认知心理学": ["认知", "思维", "记忆", "语言"],
            "社会心理学": ["社会心理", "群体", "关系"],
            "积极心理学": ["积极心理", "幸福", "快乐", "情绪管理"],
            "人格心理学": ["人格", "性格", "自我", "人际", "人物"]
        },
        "社会科学类": {
            "社会学": ["社会", "倦怠社会", "家族", "群体", "阶层", "阶级", "马克思"],
            "人类学": ["人类学", "人类", "文化", "族群", "习俗"],
            "政治学": ["政治", "国家", "权力", "统治", "民主", "帝国", "共产", "马克思", "恩格斯", "列宁", "毛泽东", "社会主义", "资本主义"],
            "经济学": ["经济", "资本", "市场", "贫穷", "金融", "财富"],
            "法律": ["法律", "法学", "宪法", "刑法", "民法", "权利", "规则"],
            "教育学": ["教育", "教学", "学习", "教师", "课堂", "语文教学"]
        },
        "历史类": {
            "世界史": ["世界史", "全球史", "文明史", "欧洲", "中世纪", "古代", "近代", "现代"],
            "地区史": ["中国史", "美国史", "欧洲史", "亚洲", "非洲", "俄国", "苏联"],
            "文明史": ["文明", "文化史", "艺术史"],
            "军事史": ["军事", "战争", "战略", "战术"],
            "文化史": ["文化", "思想史", "艺术史", "宗教史"]
        },
        "科学技术类": {
            "物理学": ["物理", "力学", "电磁", "量子", "相对论", "天体"],
            "生物学": ["生物", "生命", "进化", "基因", "细胞"],
            "化学": ["化学", "物质", "分子", "原子"],
            "天文学": ["天文", "宇宙", "太空", "星球", "恒星", "行星"],
            "医学": ["医学", "医疗", "健康", "疾病", "治疗", "药物"],
            "工程学": ["工程", "建筑", "设计", "机械"],
            "计算机科学": ["计算机", "算法", "程序", "软件", "人工智能"]
        },
        "艺术类": {
            "音乐": ["音乐", "歌曲", "交响曲", "乐器", "作曲", "演奏"],
            "绘画": ["绘画", "油画", "水彩", "素描", "色彩"],
            "雕塑": ["雕塑", "建筑", "装置"],
            "建筑": ["建筑", "设计", "城市", "空间"],
            "摄影": ["摄影", "图像", "照片"],
            "电影": ["电影", "导演", "影片", "剧情", "演员"]
        },
        "宗教与灵性类": {
            "宗教研究": ["宗教", "信仰", "神学", "圣经", "佛学", "伊斯兰", "道教", "儒家", "瑜伽"],
            "神学": ["神学", "上帝", "基督", "圣经", "伦理"],
            "灵修": ["灵修", "修行", "冥想", "佛法", "瑜伽", "禅修"],
            "宗教史": ["宗教史", "佛教史", "基督教史", "伊斯兰史"]
        },
        "自助与个人发展类": {
            "职业发展": ["职业", "工作", "事业", "成功", "管理", "领导", "创业"],
            "人际关系": ["人际", "关系", "沟通", "交流", "婚姻", "家庭"],
            "健康与养生": ["健康", "养生", "饮食", "运动", "睡眠", "瑜伽"],
            "财富管理": ["财富", "投资", "理财", "金钱", "经济"],
            "心灵成长": ["成长", "自我", "意识", "幸福", "快乐", "改变", "优秀的普通人"]
        },
        "参考工具书类": {
            "字典": ["字典", "词典", "辞典", "词汇"],
            "百科全书": ["百科", "全书", "通识", "手册"],
            "地图集": ["地图", "地理", "地域"],
            "手册": ["手册", "指南", "指导", "教程"]
        }
    }

    # 对英文书名的特殊处理
    english_title_categories = {
        "The Miraculous Journey of Edward Tulane": ["文学类", "儿童文学"],
        "Magic Tree House": ["文学类", "儿童文学"],
        "Shadow of the Shark": ["文学类", "儿童文学"],
        "The Walking Dead": ["文学类", "漫画"],
        "Pride and Prejudice": ["文学类", "小说"],
        "One Man's View of the World": ["社会科学类", "政治学"],
        "The Price We Pay": ["社会科学类", "经济学"]
    }

    # 检查是否为已知的英文书名
    for eng_title, cats in english_title_categories.items():
        if eng_title.lower() in title.lower():
            return cats

    # 对特定书名的精确匹配
    specific_books = {
        "红楼梦": ["文学类", "古典小说"],
        "西西弗神话": ["哲学类", "西方哲学"],
        "三国演义": ["文学类", "古典小说"],
        "金瓶梅": ["文学类", "古典小说"],
        "牛津通识读本": ["参考工具书类", "百科全书"],
        "人生的意义": ["哲学类", "伦理学"],
        "量子理论": ["科学技术类", "物理学"],
        "1984": ["文学类", "科幻小说"],
        "战争与和平": ["文学类", "小说"],
        "罪与罚": ["文学类", "小说"],
        "维特根斯坦": ["哲学类", "西方哲学"],
        "尼采": ["哲学类", "西方哲学"],
        "柏拉图全集": ["哲学类", "西方哲学"],
        "毛泽东选集": ["社会科学类", "政治学"],
        "论语集解": ["哲学类", "东方哲学"],
        "悉达多": ["文学类", "小说"],
        "游鸭：被迫迁徙的我们": ["文学类", "小说"],
        "追忆似水年华": ["文学类", "小说"],
        "第一哲学沉思集": ["哲学类", "西方哲学"],
        "我与地坛": ["文学类", "散文"],
        "稻盛和夫的人生哲学": ["哲学类", "东方哲学"],
        "第二性": ["社会科学类", "政治学"],
        "长安十二时辰": ["文学类", "历史小说"],
        "静静的顿河": ["文学类", "小说"],
        "黑格尔导论": ["哲学类", "西方哲学"],
        "精神现象学": ["哲学类", "西方哲学"],
        "沙丘": ["文学类", "科幻小说"],
        "乌托邦": ["文学类", "科幻小说"],
        "我偏爱读诗的荒谬": ["文学类", "诗歌"],
    }

    # 检查是否为特定书籍
    for book_name, cats in specific_books.items():
        if book_name in title:
            return cats

    # 通用分类匹配
    matched_categories = []
    for main_category, subcategories in category_rules.items():
        for subcategory, keywords in subcategories.items():
            for keyword in keywords:
                if keyword in title:
                    if [main_category, subcategory] not in matched_categories:
                        matched_categories.append([main_category, subcategory])

    # 如果找到匹配的类别，返回第一个匹配的主类别和子类别
    if matched_categories:
        return matched_categories[0]

    # 如果没有匹配到任何分类，则返回未分类
    return ["未分类"]


def update_book_categories(data: Dict[str, Any]) -> Dict[str, Any]:
    """更新书籍分类信息"""
    # 创建一个计数器来跟踪更新的书籍数量
    updated_count = 0
    unchanged_count = 0

    # 遍历所有用户和他们的书籍
    for user in data["users"]:
        for book in user["books"]:
            # 如果当前分类为"未分类"或只有一个分类项
            if book["category"] == ["未分类"] or len(book["category"]) == 1:
                # 获取新的分类
                new_category = categorize_book(book["title"])
                # 如果新分类不是"未分类"，则更新
                if new_category != ["未分类"]:
                    book["category"] = new_category
                    updated_count += 1
                else:
                    unchanged_count += 1
            else:
                unchanged_count += 1

    print(f"更新了 {updated_count} 本书籍的分类信息")
    print(f"保持不变的书籍: {unchanged_count} 本")

    return data


def main():
    """主函数"""
    # 文件路径
    json_file = "processed_202501.json"
    output_file = "processed_202501_categorized.json"

    # 检查文件是否存在
    if not os.path.exists(json_file):
        print(f"错误: 文件 {json_file} 不存在")
        return

    # 加载JSON文件
    print(f"正在加载 {json_file}...")
    data = load_json_file(json_file)

    # 更新书籍分类
    print("正在更新书籍分类信息...")
    updated_data = update_book_categories(data)

    # 保存更新后的JSON文件
    print(f"正在保存更新后的数据到 {output_file}...")
    save_json_file(updated_data, output_file)

    print(f"处理完成。更新后的数据已保存至 {output_file}")


if __name__ == "__main__":
    main()
