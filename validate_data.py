#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证程序 - 用于对比原始TXT文件和处理后的JSON文件，确认数据处理的准确性
"""

import re
import json
import os
import datetime
from collections import defaultdict


def extract_original_data(file_path):
    """从原始TXT文件中提取数据"""
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 移除第一行"#接龙"和月份读书分享
    month_pattern = r'^#接龙\s*\n\d+月读书分享\s*\n'
    content = re.sub(month_pattern, '', content)

    # 提取条目
    entries = re.findall(
        r'(\d+)\.\s+(.*?)(?=\n\d+\.\s+|\Z)', content, re.DOTALL)

    # 统计数据
    total_users = 0
    total_books = 0
    user_ids = []
    user_entries = {}  # 存储用户的原始条目内容

    # 用户数据存储
    for entry_num, entry_content in entries:
        # 提取用户ID
        user_match = re.match(r'([^，。：\n]+)', entry_content.strip())
        if user_match:
            total_users += 1
            user_id = user_match.group(1).strip()
            user_ids.append(user_id)
            user_entries[user_id] = entry_content.strip()  # 存储原始条目

            # 提取书籍信息
            books_content = entry_content[len(user_match.group(0)):].strip()

            # 统计书籍数量 (简化版本，仅检查《》标记的书籍)
            books = re.findall(r'《([^》]+)》', books_content)
            total_books += len(books)

            # 检查英文书名 (简化版本)
            english_books = re.findall(
                r'([A-Z][a-zA-Z\s\':#\d-]+)', books_content)
            for book_name in english_books:
                if len(book_name.split()) > 1 and book_name not in ["Magic Tree House"]:
                    total_books += 1

    return {
        "total_users": total_users,
        "total_books_approx": total_books,
        "user_ids": user_ids,
        "user_entries": user_entries  # 添加用户条目内容
    }


def load_processed_data(file_path):
    """加载处理后的JSON数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 提取统计信息
    stats = data.get("stats", {})
    users = data.get("users", [])

    # 提取用户ID和书籍信息
    user_ids = []
    user_books = {}
    total_books_actual = 0

    for user in users:
        user_id = user.get("user_id")
        books = user.get("books", [])
        user_ids.append(user_id)
        user_books[user_id] = books
        total_books_actual += len(books)

    return {
        "total_participants": stats.get("total_participants", 0),
        "total_books": stats.get("total_books", 0),
        "total_books_actual": total_books_actual,  # 实际计算的书籍总数
        "user_ids": user_ids,
        "user_books": user_books,  # 添加用户书籍信息
        "user_count": len(users)
    }


def analyze_discrepancies(original_data, processed_data):
    """分析数据差异的原因"""
    reasons = []
    details = {}

    # 用户数量差异分析
    if original_data['total_users'] != processed_data['total_participants']:
        reasons.append("用户数量不匹配可能的原因：")

        # 检查是否有条目无法提取用户ID
        original_entries = len(original_data['user_entries'])
        if original_data['total_users'] != original_entries:
            reasons.append(
                f"• 原始数据中有条目未能正确解析用户ID（解析了{original_data['total_users']}个，实际有{original_entries}个条目）")

        # 检查用户ID是否有合并情况
        name_patterns = [
            (r'小菜鸡\s*读完', '小菜鸡读完'),
            (r'小菜鸡\s*在读', '小菜鸡在读')
        ]

        similar_names = []
        for pattern, standard_name in name_patterns:
            similar_ids = [
                uid for uid in original_data['user_ids'] if re.match(pattern, uid)]
            if len(similar_ids) > 1:
                similar_names.append(
                    f"• 可能存在用户名合并: {', '.join(similar_ids)} 可能被合并为 '{standard_name}'")

        if similar_names:
            reasons.extend(similar_names)

    # 书籍数量差异分析
    if original_data['total_books_approx'] != processed_data['total_books']:
        book_diff = abs(
            original_data['total_books_approx'] - processed_data['total_books'])
        reasons.append(f"书籍数量差异({book_diff}本)可能的原因：")

        # 检查英文书名提取问题
        reasons.append("• 英文书名的提取规则可能导致误差（处理程序只提取含有空格的英文书名）")

        # 检查重复书籍问题
        reasons.append("• 同一本书在不同格式下可能被重复计算（如带编号的书籍 📖1《书名》和普通《书名》格式）")

        # 检查特殊格式
        reasons.append("• 特殊格式的书名可能无法被正确提取（如不带书名号的中文书名）")

    # 提供详细的用户和书籍信息以便进一步分析
    details = {
        "user_count_original": original_data['total_users'],
        "user_count_processed": processed_data['total_participants'],
        "book_count_original": original_data['total_books_approx'],
        "book_count_processed": processed_data['total_books'],
        "book_count_actual": processed_data['total_books_actual'],
    }

    return reasons, details


def generate_markdown_report(original_data, processed_data, original_file, processed_file):
    """生成Markdown格式的验证报告"""
    # 获取当前时间
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 计算差异
    original_users_set = set(original_data['user_ids'])
    processed_users_set = set(processed_data['user_ids'])

    common_users = original_users_set.intersection(processed_users_set)
    missing_users = original_users_set - processed_users_set
    extra_users = processed_users_set - original_users_set

    books_diff = abs(
        original_data['total_books_approx'] - processed_data['total_books'])
    books_diff_percent = (
        books_diff / processed_data['total_books']) * 100 if processed_data['total_books'] > 0 else 0

    # 分析差异原因
    discrepancy_reasons, discrepancy_details = analyze_discrepancies(
        original_data, processed_data)

    # 生成状态标记
    user_match_status = "✅" if original_data['total_users'] == processed_data['total_participants'] and len(
        missing_users) == 0 and len(extra_users) == 0 else "❌"
    books_match_status = "✅" if books_diff_percent <= 20 else "❌"

    # 构建Markdown内容
    md_content = f"""# 数据验证报告

*生成时间: {current_time}*

## 基本信息

| 项目 | 详情 |
|------|------|
| 原始文件 | `{original_file}` |
| 处理后文件 | `{processed_file}` |

## 统计数据比较

| 指标 | 原始数据 | 处理后数据 | 差异 | 状态 |
|------|---------|-----------|------|------|
| 用户数量 | {original_data['total_users']} | {processed_data['total_participants']} | {abs(original_data['total_users'] - processed_data['total_participants'])} | {user_match_status} |
| 书籍数量 | {original_data['total_books_approx']} (近似值) | {processed_data['total_books']} | {books_diff} ({books_diff_percent:.2f}%) | {books_match_status} |

## 用户匹配情况

- 共同用户数量: {len(common_users)}
- 原始数据有但处理后数据没有的用户数量: {len(missing_users)}
- 处理后数据有但原始数据没有的用户数量: {len(extra_users)}

"""

    # 添加丢失用户列表
    if len(missing_users) > 0:
        md_content += "### 丢失的用户\n\n"
        for i, user in enumerate(sorted(missing_users), 1):
            user_entry = original_data['user_entries'].get(user, "无法获取条目内容")
            md_content += f"{i}. `{user}`\n   - 原始条目: `{user_entry}`\n"
        md_content += "\n"

    # 添加额外用户列表
    if len(extra_users) > 0:
        md_content += "### 额外的用户\n\n"
        for i, user in enumerate(sorted(extra_users), 1):
            books = processed_data['user_books'].get(user, [])
            book_titles = [book.get('title', 'Unknown') for book in books]
            md_content += f"{i}. `{user}`\n   - 书籍: {', '.join([f'《{title}》' for title in book_titles])}\n"
        md_content += "\n"

    # 添加差异原因分析
    md_content += "## 差异分析\n\n"

    if discrepancy_reasons:
        for reason in discrepancy_reasons:
            md_content += f"{reason}\n"
    else:
        md_content += "无明显差异或无法确定差异原因。\n"

    # 添加验证结论
    md_content += "\n## 验证结论\n\n"

    if user_match_status == "✅":
        md_content += "- ✅ **用户数据匹配**: 原始用户全部被正确处理\n"
    else:
        md_content += "- ❌ **用户数据不完全匹配**: 请检查上述差异分析\n"

    if books_match_status == "✅":
        md_content += f"- ✅ **书籍数量基本匹配**: 差异在可接受范围内 ({books_diff_percent:.2f}%)\n"
    else:
        md_content += f"- ❌ **书籍数量差异较大**: 差异率 {books_diff_percent:.2f}%\n"

    md_content += "\n> **注意**: 书籍数量统计为近似值，因为原始文本格式复杂，难以精确提取所有书籍\n"

    return md_content


def validate_data():
    """验证数据处理的准确性"""
    # 文件路径
    original_file = os.path.join("data", "202501.txt")
    processed_file = "processed_202501.json"

    # 提取数据
    original_data = extract_original_data(original_file)
    processed_data = load_processed_data(processed_file)

    # 打印统计信息
    print("=" * 50)
    print("数据验证报告")
    print("=" * 50)
    print(f"原始文件: {original_file}")
    print(f"处理后文件: {processed_file}")
    print("\n统计信息比较:")
    print(
        f"用户数量: 原始数据={original_data['total_users']}, 处理后数据={processed_data['total_participants']}")
    print(
        f"书籍数量: 原始数据≈{original_data['total_books_approx']} (近似值), 处理后数据={processed_data['total_books']}")

    # 检查用户ID是否匹配
    original_users_set = set(original_data['user_ids'])
    processed_users_set = set(processed_data['user_ids'])

    # 计算交集、差集
    common_users = original_users_set.intersection(processed_users_set)
    missing_users = original_users_set - processed_users_set
    extra_users = processed_users_set - original_users_set

    # 打印用户匹配情况
    print("\n用户匹配情况:")
    print(f"共同用户数量: {len(common_users)}")
    print(f"原始数据中有但处理后数据中没有的用户数量: {len(missing_users)}")
    print(f"处理后数据中有但原始数据中没有的用户数量: {len(extra_users)}")

    # 验证结果
    if len(missing_users) > 0:
        print("\n警告: 部分用户在处理过程中丢失")
        print("丢失的用户:")
        for user in sorted(missing_users):
            print(f"  - {user}")

    if len(extra_users) > 0:
        print("\n警告: 处理后数据中包含原始数据中不存在的用户")
        print("额外的用户:")
        for user in sorted(extra_users):
            print(f"  - {user}")

    # 总结验证结果
    print("\n验证结论:")
    if original_data['total_users'] == processed_data['total_participants'] and len(missing_users) == 0 and len(extra_users) == 0:
        print("✅ 用户数据匹配 - 原始用户全部被正确处理")
    else:
        print("❌ 用户数据不完全匹配 - 请检查上述警告信息")

    # 由于书籍数量只是近似值，我们允许一定的误差
    books_diff = abs(
        original_data['total_books_approx'] - processed_data['total_books'])
    books_diff_percent = (
        books_diff / processed_data['total_books']) * 100 if processed_data['total_books'] > 0 else 0

    if books_diff_percent <= 20:  # 允许20%的误差
        print(f"✅ 书籍数量基本匹配 - 差异在可接受范围内 ({books_diff_percent:.2f}%)")
    else:
        print(f"❌ 书籍数量差异较大 - 差异率: {books_diff_percent:.2f}%")

    print("\n注意: 书籍数量统计为近似值，因为原始文本格式复杂，难以精确提取所有书籍")
    print("=" * 50)

    # 生成Markdown报告并保存到文件
    md_report = generate_markdown_report(
        original_data, processed_data, original_file, processed_file)

    # 生成报告文件名，格式为 validation_report_YYYYMMDD_HHMMSS.md
    report_filename = f"validation_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

    # 保存报告
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write(md_report)

    print(f"\n详细的验证报告已保存至: {report_filename}")


if __name__ == "__main__":
    validate_data()
