"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"

interface SimpleSearchInputProps {
  placeholder: string
  value: string
  onChange: (value: string) => void
  onSearch?: () => void
  onClear?: () => void
  suggestions: string[]
}

export function SimpleSearchInput({
  placeholder,
  value,
  onChange,
  onSearch,
  onClear,
  suggestions,
}: SimpleSearchInputProps) {
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([])
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  // 当输入值变化时过滤建议
  useEffect(() => {
    if (!value) {
      setFilteredSuggestions([])
      setShowSuggestions(false)
      return
    }

    const filtered = suggestions.filter((item) => item.toLowerCase().includes(value.toLowerCase())).slice(0, 10)

    setFilteredSuggestions(filtered)
    setShowSuggestions(filtered.length > 0)
  }, [value, suggestions])

  // 处理点击外部关闭建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // 处理建议项点击
  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion)
    setShowSuggestions(false)
    if (onSearch) {
      setTimeout(() => {
        onSearch()
      }, 100)
    }
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && onSearch) {
      onSearch()
      setShowSuggestions(false)
    } else if (e.key === "Escape") {
      setShowSuggestions(false)
    } else if (e.key === "ArrowDown" && showSuggestions && filteredSuggestions.length > 0) {
      // 防止光标移动到输入框末尾
      e.preventDefault()
      const firstSuggestion = document.querySelector("[data-suggestion]") as HTMLElement
      if (firstSuggestion) {
        firstSuggestion.focus()
      }
    }
  }

  // 处理建议项键盘导航
  const handleSuggestionKeyDown = (e: React.KeyboardEvent<HTMLDivElement>, index: number) => {
    if (e.key === "Enter") {
      handleSuggestionClick(filteredSuggestions[index])
    } else if (e.key === "Escape") {
      setShowSuggestions(false)
      inputRef.current?.focus()
    } else if (e.key === "ArrowDown") {
      e.preventDefault()
      const nextSuggestion = document.querySelector(`[data-suggestion="${index + 1}"]`) as HTMLElement
      if (nextSuggestion) {
        nextSuggestion.focus()
      }
    } else if (e.key === "ArrowUp") {
      e.preventDefault()
      if (index === 0) {
        inputRef.current?.focus()
      } else {
        const prevSuggestion = document.querySelector(`[data-suggestion="${index - 1}"]`) as HTMLElement
        if (prevSuggestion) {
          prevSuggestion.focus()
        }
      }
    }
  }

  // 清空搜索内容
  const handleClear = () => {
    onChange("")
    inputRef.current?.focus()
    if (onClear) {
      onClear()
    }
  }

  return (
    <div className="relative w-full">
      <div className="relative">
        <Input
          ref={inputRef}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (value && filteredSuggestions.length > 0) {
              setShowSuggestions(true)
            }
          }}
          className="w-full pr-8 h-10 text-base"
        />
        {value && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 p-1 rounded-full"
            aria-label="清空搜索"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* 建议下拉框 */}
      {showSuggestions && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200 max-h-60 overflow-auto"
        >
          {filteredSuggestions.map((suggestion, index) => (
            <div
              key={suggestion}
              data-suggestion={index}
              tabIndex={0}
              onClick={() => handleSuggestionClick(suggestion)}
              onKeyDown={(e) => handleSuggestionKeyDown(e, index)}
              className={cn(
                "px-3 py-3 text-sm cursor-pointer hover:bg-gray-100 focus:bg-gray-100 focus:outline-none",
                value === suggestion ? "bg-gray-100" : "",
              )}
            >
              {suggestion}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
