"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Menu, X, Home, BarChart2, Upload, FileText, Settings, Share2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

export function MobileNavigation() {
  const pathname = usePathname()
  const [open, setOpen] = useState(false)

  const routes = [
    {
      href: "/",
      label: "主页",
      icon: <Home className="h-5 w-5 mr-2" />,
      active: pathname === "/",
    },
    {
      href: "/data-analysis",
      label: "数据概览",
      icon: <BarChart2 className="h-5 w-5 mr-2" />,
      active: pathname === "/data-analysis",
    },
    {
      href: "/import-data",
      label: "导入数据",
      icon: <Upload className="h-5 w-5 mr-2" />,
      active: pathname.includes("/import-data"),
    },
    {
      href: "/annual-report",
      label: "年度报告",
      icon: <FileText className="h-5 w-5 mr-2" />,
      active: pathname === "/annual-report",
    },
    {
      href: "https://readmap.inklinks.io/",
      label: "资源分享",
      icon: <Share2 className="h-5 w-5 mr-2" />,
      active: false,
      external: true,
    },
    {
      href: "/admin",
      label: "管理面板",
      icon: <Settings className="h-5 w-5 mr-2" />,
      active: pathname === "/admin",
    },
  ]

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-6 w-6" />
          <span className="sr-only">打开菜单</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[250px] sm:w-[300px] pb-[72px] md:pb-0">
        <div className="flex flex-col gap-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-xl font-bold" onClick={() => setOpen(false)}>
              书读经典读书群
            </Link>
            <Button variant="ghost" size="icon" onClick={() => setOpen(false)}>
              <X className="h-5 w-5" />
            </Button>
          </div>
          <nav className="flex flex-col space-y-4">
            {routes.map((route) => (
              <Link
                key={route.href}
                href={route.href}
                onClick={() => setOpen(false)}
                target={route.external ? "_blank" : undefined}
                rel={route.external ? "noopener noreferrer" : undefined}
                className={cn(
                  "flex items-center py-2 px-3 rounded-md transition-colors",
                  route.active
                    ? "bg-primary/10 text-primary font-medium"
                    : "text-muted-foreground hover:text-primary hover:bg-primary/5",
                )}
              >
                {route.icon}
                <span>{route.label}</span>
              </Link>
            ))}
          </nav>
        </div>
      </SheetContent>
    </Sheet>
  )
}
