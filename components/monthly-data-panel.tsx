"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle, CardFooter } from "@/components/ui/card"
import { Users, BookOpen, BookMarked, TrendingUp } from "lucide-react"
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import Link from "next/link"

// 月份名称
const monthNames = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]

// Mock data for when Supabase connection is unavailable
const MOCK_MONTHLY_DATA = [
  {
    month: 1,
    monthName: "一月",
    total_participants: 34,
    total_books: 127,
    active_readers: 17,
    completed_books: 39,
    hasData: true,
  },
  {
    month: 2,
    monthName: "二月",
    total_participants: 36,
    total_books: 135,
    active_readers: 19,
    completed_books: 42,
    hasData: true,
  },
  {
    month: 3,
    monthName: "三月",
    total_participants: 38,
    total_books: 142,
    active_readers: 21,
    completed_books: 45,
    hasData: true,
  },
]

interface MonthlyDataPanelProps {
  year: number
  totalGroupMembers: number
}

export function MonthlyDataPanel({ year, totalGroupMembers }: MonthlyDataPanelProps) {
  const [monthlyData, setMonthlyData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedMonth, setSelectedMonth] = useState<string>("all")
  const [monthlyReports, setMonthlyReports] = useState<Record<number, boolean>>({})
  const [isPreviewMode, setIsPreviewMode] = useState(false)

  useEffect(() => {
    const fetchMonthlyData = async () => {
      try {
        setIsLoading(true)
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

        // Check if we're in preview mode
        const isPreview =
          typeof window !== "undefined" &&
          (window.location.hostname.includes("vercel.app") ||
            window.location.hostname.includes("localhost") ||
            window.location.search.includes("preview=true"))

        setIsPreviewMode(isPreview)

        // If Supabase environment variables are not set or we're in preview mode, use mock data
        if (!supabaseUrl || !supabaseKey || isPreview) {
          console.warn("Using mock data for monthly panel (preview mode or missing env vars)")

          // Process mock data to ensure all months
          const processedData = Array.from({ length: 12 }, (_, index) => {
            const month = index + 1
            const mockMonth = MOCK_MONTHLY_DATA.find((item) => item.month === month)

            return (
              mockMonth || {
                month,
                monthName: monthNames[index],
                total_participants: 0,
                total_books: 0,
                active_readers: 0,
                completed_books: 0,
                hasData: false,
              }
            )
          })

          setMonthlyData(processedData)

          // Mock monthly reports data
          const mockReports: Record<number, boolean> = {}
          MOCK_MONTHLY_DATA.forEach((month) => {
            mockReports[month.month] = true
          })
          setMonthlyReports(mockReports)

          setIsLoading(false)
          return
        }

        const { createClient } = await import("@supabase/supabase-js")
        const supabase = createClient(supabaseUrl, supabaseKey)

        // 获取指定年份的月度数据
        const { data, error } = await supabase
          .from("monthly_stats")
          .select("*")
          .eq("year", year)
          .order("month", { ascending: true })

        if (error) throw new Error(`获取月度数据失败: ${error.message}`)

        // 处理数据，确保所有月份都有数据
        const processedData = Array.from({ length: 12 }, (_, index) => {
          const month = index + 1
          const monthData = data?.find((item) => item.month === month)

          return {
            month,
            monthName: monthNames[index],
            total_participants: monthData?.total_participants || 0,
            total_books: monthData?.total_books || 0,
            active_readers: monthData?.active_readers || 0,
            completed_books: monthData?.completed_books || 0,
            hasData: !!monthData,
          }
        })

        setMonthlyData(processedData)

        // 获取月度报告信息
        try {
          const { data: reportsData, error: reportsError } = await supabase
            .from("monthly_reports")
            .select("month")
            .eq("year", year)

          if (reportsError) {
            // Check if the error is because the table doesn't exist
            if (reportsError.message.includes("does not exist")) {
              console.log("monthly_reports table does not exist yet, skipping report status check")
              setMonthlyReports({})
            } else {
              console.error("Error fetching monthly reports:", reportsError)
            }
          } else {
            // Create month reports mapping
            const reportsMap: Record<number, boolean> = {}
            reportsData?.forEach((report) => {
              reportsMap[report.month] = true
            })
            setMonthlyReports(reportsMap)
          }
        } catch (err) {
          console.error("Error checking monthly reports:", err)
          // Don't fail the entire component if this part fails
          setMonthlyReports({})
        }
      } catch (err) {
        console.error("Error fetching monthly data:", err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchMonthlyData()
  }, [year])

  // 获取当前选择的月份数据
  const getSelectedMonthData = () => {
    if (selectedMonth === "all") {
      return monthlyData.filter((month) => month.hasData)
    }

    const monthNumber = Number.parseInt(selectedMonth)
    return monthlyData.filter((month) => month.month === monthNumber)
  }

  const selectedData = getSelectedMonthData()

  if (isLoading) {
    return (
      <div className="mt-8">
        <h2 className="text-2xl font-bold mb-4">月度数据</h2>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    )
  }

  if (error && !isPreviewMode) {
    return (
      <div className="mt-8">
        <h2 className="text-2xl font-bold mb-4">月度数据</h2>
        <div className="bg-red-50 text-red-700 p-4 rounded-md">加载月度数据时出错: {error}</div>
      </div>
    )
  }

  // 检查是否有月度数据
  const hasMonthlyData = monthlyData.some((month) => month.hasData)

  if (!hasMonthlyData && !isPreviewMode) {
    return (
      <div className="mt-8">
        <h2 className="text-2xl font-bold mb-4">月度数据</h2>
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-muted-foreground">{year}年暂无月度数据</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="mt-8">
      <h2 className="text-2xl font-bold mb-4">月度数据</h2>

      {isPreviewMode && (
        <div className="bg-amber-50 text-amber-700 p-4 rounded-md mb-4">
          <p>预览模式：显示的是模拟数据，仅供界面预览。</p>
        </div>
      )}

      <Tabs defaultValue="all" value={selectedMonth} onValueChange={setSelectedMonth} className="mb-6">
        <TabsList className="mb-4 flex flex-wrap">
          <TabsTrigger value="all">全部</TabsTrigger>
          {monthlyData.map(
            (month) =>
              month.hasData && (
                <TabsTrigger key={month.month} value={month.month.toString()}>
                  {month.monthName}
                </TabsTrigger>
              ),
          )}
        </TabsList>
      </Tabs>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {selectedData.map((monthData) => (
          <Card key={monthData.month}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{monthData.monthName}</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-blue-500 mr-2" />
                    <span className="text-sm font-medium">参与人数</span>
                  </div>
                  <div className="text-sm">
                    <span className="font-bold">{monthData.total_participants}</span>
                    <span className="text-xs text-muted-foreground ml-1">
                      ({monthData.total_participants}/{totalGroupMembers})
                    </span>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 text-green-500 mr-2" />
                    <span className="text-sm font-medium">书籍数量</span>
                  </div>
                  <div className="text-sm font-bold">{monthData.total_books}</div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <BookMarked className="h-4 w-4 text-amber-500 mr-2" />
                    <span className="text-sm font-medium">活跃读者</span>
                  </div>
                  <div className="text-sm font-bold">{monthData.active_readers}</div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <TrendingUp className="h-4 w-4 text-purple-500 mr-2" />
                    <span className="text-sm font-medium">完成阅读</span>
                  </div>
                  <div className="text-sm font-bold">{monthData.completed_books}</div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild variant={monthlyReports[monthData.month] ? "default" : "outline"} className="w-full">
                <Link href={`/${year}/${monthData.month}?returnYear=${year}`}>
                  {monthlyReports[monthData.month] ? "查看完整报告" : "查看详情"}
                </Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
