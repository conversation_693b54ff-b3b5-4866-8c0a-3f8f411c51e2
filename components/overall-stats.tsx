import { createClient } from "@/lib/supabase"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, BookOpen, BookMarked, TrendingUp } from "lucide-react"

export async function OverallStats() {
  const supabase = createClient()

  // 获取总体统计数据
  const { data: usersCount } = await supabase.from("users").select("id", { count: "exact", head: true })

  const { data: booksCount } = await supabase.from("books").select("id", { count: "exact", head: true })

  const { data: recordsCount } = await supabase.from("reading_records").select("id", { count: "exact", head: true })

  const { data: completedCount } = await supabase
    .from("reading_records")
    .select("id", { count: "exact", head: true })
    .eq("reading_status", "读完")

  const stats = [
    {
      title: "总参与人数",
      value: usersCount?.count || 0,
      icon: <Users className="h-5 w-5 text-blue-600" />,
      description: "所有时间段的用户总数",
    },
    {
      title: "总书籍数量",
      value: booksCount?.count || 0,
      icon: <BookOpen className="h-5 w-5 text-green-600" />,
      description: "所有时间段的书籍总数",
    },
    {
      title: "总阅读记录",
      value: recordsCount?.count || 0,
      icon: <BookMarked className="h-5 w-5 text-amber-600" />,
      description: "所有时间段的阅读记录总数",
    },
    {
      title: "已完成阅读",
      value: completedCount?.count || 0,
      icon: <TrendingUp className="h-5 w-5 text-purple-600" />,
      description: "所有时间段已完成的书籍数量",
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            {stat.icon}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
