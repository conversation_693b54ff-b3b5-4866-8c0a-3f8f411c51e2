import { createClient } from "@/lib/supabase"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, BookOpen, BookMarked, TrendingUp } from "lucide-react"

export async function YearlyStats({ year }: { year: number }) {
  const supabase = createClient()

  // 获取指定年份的月度统计数据
  const { data: monthlyStats } = await supabase.from("monthly_stats").select("*").eq("year", year)

  // 计算年度汇总数据
  const yearlyData = {
    totalParticipants: 0,
    totalBooks: 0,
    activeReaders: 0,
    completedBooks: 0,
  }

  if (monthlyStats && monthlyStats.length > 0) {
    // 找出最大的总参与人数和总书籍数（因为这些是累计值）
    yearlyData.totalParticipants = Math.max(...monthlyStats.map((stat) => stat.total_participants || 0))
    yearlyData.totalBooks = Math.max(...monthlyStats.map((stat) => stat.total_books || 0))

    // 累加每月的活跃读者和完成书籍数
    yearlyData.activeReaders = monthlyStats.reduce((sum, stat) => sum + (stat.active_readers || 0), 0)
    yearlyData.completedBooks = monthlyStats.reduce((sum, stat) => sum + (stat.completed_books || 0), 0)
  }

  const stats = [
    {
      title: `${year}年参与人数`,
      value: yearlyData.totalParticipants,
      icon: <Users className="h-5 w-5 text-blue-600" />,
      description: `${year}年度的用户总数`,
    },
    {
      title: `${year}年书籍数量`,
      value: yearlyData.totalBooks,
      icon: <BookOpen className="h-5 w-5 text-green-600" />,
      description: `${year}年度的书籍总数`,
    },
    {
      title: `${year}年活跃读者`,
      value: yearlyData.activeReaders,
      icon: <BookMarked className="h-5 w-5 text-amber-600" />,
      description: `${year}年度的月度活跃读者累计`,
    },
    {
      title: `${year}年完成阅读`,
      value: yearlyData.completedBooks,
      icon: <TrendingUp className="h-5 w-5 text-purple-600" />,
      description: `${year}年度已完成的书籍数量`,
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            {stat.icon}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
