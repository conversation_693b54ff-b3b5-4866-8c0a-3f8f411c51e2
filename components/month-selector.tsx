"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// 月份名称
const monthNames = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]

interface MonthSelectorProps {
  availableYears: number[]
  availableMonths: {
    year: number
    months: number[]
  }[]
  defaultYear?: number
  defaultMonth?: number
}

export function MonthSelector({ availableYears, availableMonths, defaultYear, defaultMonth }: MonthSelectorProps) {
  const router = useRouter()
  const [selectedYear, setSelectedYear] = useState<number>(defaultYear || availableYears[0])
  const [selectedMonth, setSelectedMonth] = useState<number>(defaultMonth || 1)

  // 获取当前选择年份可用的月份
  const getAvailableMonthsForYear = (year: number) => {
    const yearData = availableMonths.find((item) => item.year === year)
    return yearData ? yearData.months : []
  }

  // 当年份变化时，更新月份选择
  const handleYearChange = (year: string) => {
    const numYear = Number.parseInt(year)
    setSelectedYear(numYear)

    // 获取该年份的可用月份
    const months = getAvailableMonthsForYear(numYear)

    // 如果当前选择的月份在新年份中不可用，则选择第一个可用月份
    if (!months.includes(selectedMonth) && months.length > 0) {
      setSelectedMonth(months[0])
    }
  }

  // 当月份变化时更新状态
  const handleMonthChange = (month: string) => {
    setSelectedMonth(Number.parseInt(month))
  }

  // 导航到选择的月份页面
  const navigateToMonth = () => {
    router.push(`/${selectedYear}/${selectedMonth}?returnYear=${selectedYear}`)
  }

  // 获取当前年份的可用月份
  const currentYearMonths = getAvailableMonthsForYear(selectedYear)

  return (
    <div className="space-y-2">
      <div className="flex gap-2">
        <Select value={selectedYear.toString()} onValueChange={handleYearChange}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="选择年份" />
          </SelectTrigger>
          <SelectContent>
            {availableYears.map((year) => (
              <SelectItem key={year} value={year.toString()}>
                {year}年
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={selectedMonth.toString()}
          onValueChange={handleMonthChange}
          disabled={currentYearMonths.length === 0}
        >
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="选择月份" />
          </SelectTrigger>
          <SelectContent>
            {currentYearMonths.map((month) => (
              <SelectItem key={month} value={month.toString()}>
                {monthNames[month - 1]}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Button onClick={navigateToMonth} className="w-full" disabled={currentYearMonths.length === 0}>
        查看月度分析报告
      </Button>
    </div>
  )
}
