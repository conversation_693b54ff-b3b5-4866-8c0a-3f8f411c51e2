"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface YearSelectorProps {
  availableYears: number[]
  currentYear: number
}

export function YearSelector({ availableYears, currentYear }: YearSelectorProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [selectedYear, setSelectedYear] = useState<number>(currentYear)

  // 当URL参数或props变化时更新选中的年份
  useEffect(() => {
    const yearParam = searchParams.get("year")
    if (yearParam && !isNaN(Number(yearParam))) {
      setSelectedYear(Number(yearParam))
    } else {
      setSelectedYear(currentYear)
    }
  }, [searchParams, currentYear])

  // 处理年份变化
  const handleYearChange = (year: string) => {
    const newYear = Number(year)
    setSelectedYear(newYear)

    // 更新URL参数
    const params = new URLSearchParams(searchParams.toString())
    params.set("year", year)
    router.push(`/?${params.toString()}`)
  }

  // 处理前一年/后一年按钮点击
  const handlePrevYear = () => {
    const sortedYears = [...availableYears].sort((a, b) => a - b)
    const currentIndex = sortedYears.indexOf(selectedYear)

    if (currentIndex > 0) {
      const prevYear = sortedYears[currentIndex - 1]
      handleYearChange(prevYear.toString())
    }
  }

  const handleNextYear = () => {
    const sortedYears = [...availableYears].sort((a, b) => a - b)
    const currentIndex = sortedYears.indexOf(selectedYear)

    if (currentIndex < sortedYears.length - 1) {
      const nextYear = sortedYears[currentIndex + 1]
      handleYearChange(nextYear.toString())
    }
  }

  // 检查是否可以导航到前一年/后一年
  const sortedYears = [...availableYears].sort((a, b) => a - b)
  const currentIndex = sortedYears.indexOf(selectedYear)
  const canGoPrev = currentIndex > 0
  const canGoNext = currentIndex < sortedYears.length - 1

  return (
    <div className="flex items-center space-x-2">
      <Button variant="outline" size="icon" onClick={handlePrevYear} disabled={!canGoPrev}>
        <ChevronLeft className="h-4 w-4" />
      </Button>

      <Select value={selectedYear.toString()} onValueChange={handleYearChange}>
        <SelectTrigger className="w-[120px]">
          <SelectValue placeholder="选择年份" />
        </SelectTrigger>
        <SelectContent>
          {sortedYears.map((year) => (
            <SelectItem key={year} value={year.toString()}>
              {year}年
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Button variant="outline" size="icon" onClick={handleNextYear} disabled={!canGoNext}>
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  )
}
