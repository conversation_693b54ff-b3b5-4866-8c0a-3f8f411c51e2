"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Check, Search } from "lucide-react"
import { cn } from "@/lib/utils"

interface SearchAutocompleteProps {
  placeholder: string
  value: string
  onChange: (value: string) => void
  onSearch: () => void
  suggestions: string[]
  suggestionType: "users" | "books" | "categories"
}

export function SearchAutocomplete({
  placeholder,
  value,
  onChange,
  onSearch,
  suggestions,
  suggestionType,
}: SearchAutocompleteProps) {
  const [open, setOpen] = useState(false)
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([])
  const inputRef = useRef<HTMLInputElement>(null)
  const [compositionValue, setCompositionValue] = useState("")

  // 当输入值变化时过滤建议
  useEffect(() => {
    if (!value) {
      setFilteredSuggestions([])
      setOpen(false)
      return
    }

    // ���滤建议
    const filtered = suggestions.filter((item) => item.toLowerCase().includes(value.toLowerCase())).slice(0, 10) // 限制最多显示10个建议

    setFilteredSuggestions(filtered)
    setOpen(filtered.length > 0)
  }, [value, suggestions])

  // 处理建议项点击
  const handleSelect = (selectedValue: string) => {
    onChange(selectedValue)
    setOpen(false)
    // 自动触发搜索
    setTimeout(() => {
      onSearch()
    }, 100)
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      onSearch()
      setOpen(false)
    }
  }

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
  }

  return (
    <div className="relative w-full">
      <div className="flex gap-2">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <div className="flex-1 relative">
              <Input
                ref={inputRef}
                placeholder={placeholder}
                value={value}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                className="w-full"
                onClick={() => {
                  if (value && filteredSuggestions.length > 0) {
                    setOpen(true)
                  }
                }}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="p-0 w-[300px]" align="start">
            <Command>
              <CommandList>
                <CommandEmpty>没有找到匹配项</CommandEmpty>
                <CommandGroup
                  heading={suggestionType === "users" ? "用户" : suggestionType === "books" ? "书籍" : "分类"}
                >
                  {filteredSuggestions.map((item) => (
                    <CommandItem key={item} value={item} onSelect={handleSelect}>
                      <Check className={cn("mr-2 h-4 w-4", value === item ? "opacity-100" : "opacity-0")} />
                      {item}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        <Button variant="outline" onClick={onSearch}>
          <Search className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
