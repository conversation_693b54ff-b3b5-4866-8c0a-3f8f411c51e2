import Link from "next/link"
import { JoinUsDialog } from "./join-us-dialog"
import { Book, BarChart2, Search, Home } from "lucide-react"

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-gray-50 border-t mt-12">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-bold mb-4">书读经典读书群</h3>
            <p className="text-gray-600 text-sm">
              我们致力于推广阅读文化，建立一个活跃的读书社区，鼓励知识分享和思想交流。
            </p>
            <div className="mt-4">
              <JoinUsDialog />
            </div>
          </div>

          <div className="flex justify-end">
            <div>
              <h3 className="text-lg font-bold mb-4">快速导航</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/" className="text-gray-600 hover:text-black flex items-center gap-2">
                    <Home className="h-4 w-4" />
                    <span>主页</span>
                  </Link>
                </li>
                <li>
                  <Link href="/dashboard" className="text-gray-600 hover:text-black flex items-center gap-2">
                    <BarChart2 className="h-4 w-4" />
                    <span>数据概览</span>
                  </Link>
                </li>
                <li>
                  <Link href="/reading-query" className="text-gray-600 hover:text-black flex items-center gap-2">
                    <Search className="h-4 w-4" />
                    <span>阅读查询</span>
                  </Link>
                </li>
                <li>
                  <Link href="/annual-report" className="text-gray-600 hover:text-black flex items-center gap-2">
                    <Book className="h-4 w-4" />
                    <span>年度报告</span>
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="border-t mt-8 pt-6 text-center text-gray-500 text-sm">
          <p>© {currentYear} 书读经典读书群. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  )
}
