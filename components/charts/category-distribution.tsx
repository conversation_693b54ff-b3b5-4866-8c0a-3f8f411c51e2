"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell, Tooltip, Legend } from "recharts"

interface CategoryDistributionProps {
  data: {
    name: string
    value: number
  }[]
  title?: string
  description?: string
}

// 修改饼图组件，增加大小并解决标签重叠问题

// 更新 COLORS 数组，确保颜色一致性
const COLORS = [
  "#FF6384", // 文学类 - 粉红色
  "#36A2EB", // 哲学类 - 蓝色
  "#FFCE56", // 社会科学类 - 黄色
  "#4BC0C0", // 历史类 - 青色
  "#9966FF", // 科学技术类 - 紫色
  "#C9CBCF", // 其他 - 灰色
  "hsl(var(--chart-7))",
  "hsl(var(--chart-8))",
]

// 更新 CategoryDistribution 组件
export function CategoryDistribution({
  data,
  title = "分类分布",
  description = "阅读书籍的分类分布情况",
}: CategoryDistributionProps) {
  // 确保数据有值
  const chartData = data.length > 0 ? data : [{ name: "暂无数据", value: 1 }]

  // 计算总数
  const total = chartData.reduce((sum, item) => sum + item.value, 0)

  // 自定义标签渲染函数，解决重叠问题
  const renderCustomizedLabel = (props: any) => {
    const { cx, cy, midAngle, innerRadius, outerRadius, percent, index, name } = props

    // 只为较大的扇区显示标签
    if (percent < 0.05) return null

    const RADIAN = Math.PI / 180
    // 增加半径，使标签更远离中心
    const radius = innerRadius + (outerRadius - innerRadius) * 1.4
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    return (
      <text
        x={x}
        y={y}
        fill={COLORS[index % COLORS.length]}
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${name}: ${(percent * 100).toFixed(1)}%`}
      </text>
    )
  }

  // 自定义图例组件
  const CustomLegend = (props: any) => {
    const { payload } = props
    if (!payload || payload.length === 0) return null

    return (
      <ul className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry, index) => (
          <li key={`item-${index}`} className="flex items-center">
            <span className="inline-block w-3 h-3 mr-2 rounded-full" style={{ backgroundColor: entry.color }} />
            <span className="text-xs">
              {entry.value}: {chartData[index].value}本
            </span>
          </li>
        ))}
      </ul>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="h-[450px]">
        {" "}
        {/* 增加高度 */}
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="45%" /* 稍微上移，为图例留出更多空间 */
              labelLine={true}
              outerRadius={150} /* 增加饼图大小 */
              innerRadius={60} /* 添加内圆，使其成为环形图 */
              fill="#8884d8"
              dataKey="value"
              nameKey="name"
              label={renderCustomizedLabel}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} stroke="#fff" strokeWidth={2} />
              ))}
            </Pie>
            <Tooltip
              formatter={(value: number) => [`${value}本 (${((value / total) * 100).toFixed(1)}%)`, "数量"]}
              contentStyle={{
                backgroundColor: "white",
                border: "1px solid #ccc",
                borderRadius: "4px",
                padding: "8px",
              }}
            />
            <Legend content={<CustomLegend />} verticalAlign="bottom" align="center" />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
