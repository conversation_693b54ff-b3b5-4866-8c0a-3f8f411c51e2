"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from "recharts"
import { BookOpen } from "lucide-react"

interface UserCount {
  id: string
  count: number
}

interface CategoryData {
  category: string
  color: string
  users: UserCount[]
}

interface CategoryUserDistributionProps {
  title?: string
  subtitle?: string
  data: CategoryData[]
}

export function CategoryUserDistribution({
  title = "用户在各类别中的阅读分布",
  subtitle = "各类别中阅读量最多的用户",
  data,
}: CategoryUserDistributionProps) {
  const [activeCategory, setActiveCategory] = useState(data[0]?.category || "")

  // 获取当前选中类别的数据
  const selectedCategoryData = data.find((item) => item.category === activeCategory) || data[0]

  // 获取当前类别的颜色
  const categoryColor = selectedCategoryData?.color || "#36A2EB"

  // 准备图表数据
  const chartData =
    selectedCategoryData?.users.map((user) => ({
      name: user.id,
      value: user.count,
    })) || []

  // 自定义提示框
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded shadow text-xs">
          <p className="font-medium">{`${label}: ${payload[0].value}本`}</p>
          <p className="text-gray-500">{`${activeCategory}类别`}</p>
        </div>
      )
    }
    return null
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center">
          <BookOpen className="h-4 w-4 mr-2 text-muted-foreground" />
          <CardTitle className="text-base font-medium">{title}</CardTitle>
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>
        <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
          <TabsList className="mb-4 flex flex-wrap">
            {data.map((item) => (
              <TabsTrigger key={item.category} value={item.category} className="flex items-center gap-1">
                <span className="inline-block w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></span>
                {item.category}
              </TabsTrigger>
            ))}
          </TabsList>

          <div className="h-[350px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} layout="vertical" margin={{ top: 5, right: 30, left: 60, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis type="category" dataKey="name" tick={{ fontSize: 12 }} width={60} />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="value" name="书籍数量" radius={[0, 4, 4, 0]}>
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={categoryColor} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
