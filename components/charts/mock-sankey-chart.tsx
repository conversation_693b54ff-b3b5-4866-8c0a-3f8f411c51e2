"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Network } from "lucide-react"
import { ResponsiveSankey } from "@nivo/sankey"

export function MockSankeyChart({ title = "用户与类别关系流图", subtitle = "用户阅读不同类别书籍的流量分布" }) {
  const [hoveredNode, setHoveredNode] = useState<string | null>(null)

  // 模拟数据
  const mockData = {
    nodes: [
      // 用户节点
      { id: "user_小菜鸡", name: "小菜鸡" },
      { id: "user_Chris.W", name: "<PERSON>.<PERSON>" },
      { id: "user_娃娃", name: "娃娃" },
      { id: "user_梦田", name: "梦田" },
      { id: "user_杜佳霖", name: "杜佳霖" },
      { id: "user_其他用户", name: "其他用户" },
      // 类别节点
      { id: "文学类", nodeColor: "#FF6384" },
      { id: "哲学类", nodeColor: "#36A2EB" },
      { id: "社会科学类", nodeColor: "#FFCE56" },
      { id: "历史类", nodeColor: "#4BC0C0" },
      { id: "科学技术类", nodeColor: "#9966FF" },
      { id: "其他", nodeColor: "#C9CBCF" },
    ],
    links: [
      // 小菜鸡的链接
      { source: "user_小菜鸡", target: "文学类", value: 18 },
      { source: "user_小菜鸡", target: "哲学类", value: 8 },
      { source: "user_小菜鸡", target: "社会科学类", value: 5 },
      { source: "user_小菜鸡", target: "历史类", value: 1 },
      { source: "user_小菜鸡", target: "科学技术类", value: 2 },
      { source: "user_小菜鸡", target: "其他", value: 11 },

      // Chris.W的链接
      { source: "user_Chris.W", target: "文学类", value: 10 },
      { source: "user_Chris.W", target: "哲学类", value: 4 },
      { source: "user_Chris.W", target: "历史类", value: 2 },
      { source: "user_Chris.W", target: "科学技术类", value: 1 },
      { source: "user_Chris.W", target: "其他", value: 2 },

      // 娃娃的链接
      { source: "user_娃娃", target: "文学类", value: 7 },
      { source: "user_娃娃", target: "其他", value: 2 },

      // 梦田的链接
      { source: "user_梦田", target: "文学类", value: 6 },
      { source: "user_梦田", target: "哲学类", value: 5 },

      // 杜佳霖的链接
      { source: "user_杜佳霖", target: "文学类", value: 5 },
      { source: "user_杜佳霖", target: "社会科学类", value: 2 },
      { source: "user_杜佳霖", target: "其他", value: 2 },

      // 其他用户的链接
      { source: "user_其他用户", target: "文学类", value: 16 },
      { source: "user_其他用户", target: "哲学类", value: 6 },
      { source: "user_其他用户", target: "社会科学类", value: 5 },
      { source: "user_其他用户", target: "历史类", value: 1 },
      { source: "user_其他用户", target: "科学技术类", value: 1 },
      { source: "user_其他用户", target: "其他", value: 5 },
    ],
  }

  // 自定义节点颜色
  const getNodeColor = (node: any) => {
    // 如果节点有自定义颜色，使用自定义颜色
    if (node.nodeColor) {
      return node.nodeColor
    }

    // 用户节点使用蓝色系，类别节点使用各自的颜色
    if (node.id.startsWith("user_")) {
      return hoveredNode === node.id ? "#2563eb" : "#3b82f6"
    }

    // 默认颜色
    return "#9ca3af"
  }

  // 自定义链接颜色
  const getLinkColor = (link: any) => {
    const sourceNode = mockData.nodes.find((n) => n.id === link.source)
    const targetNode = mockData.nodes.find((n) => n.id === link.target)

    // 如果源节点或目标节点被悬停，高亮链接
    if (hoveredNode === link.source || hoveredNode === link.target) {
      return "rgba(59, 130, 246, 0.8)"
    }

    // 默认链接颜色 - 使用目标节点的颜色但透明度降低
    if (targetNode && targetNode.nodeColor) {
      return targetNode.nodeColor.replace("rgb", "rgba").replace(")", ", 0.3)")
    }

    return "rgba(203, 213, 225, 0.3)"
  }

  // 自定义节点标签
  const getNodeLabel = (node: any) => {
    if (node.name) {
      return node.name
    }

    if (node.id.startsWith("user_")) {
      return node.id.replace("user_", "")
    }

    return node.id
  }

  // 处理节点悬停
  const handleNodeHover = (node: any) => {
    setHoveredNode(node.id)
  }

  // 处理鼠标离开
  const handleMouseLeave = () => {
    setHoveredNode(null)
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center">
          <Network className="h-4 w-4 mr-2 text-muted-foreground" />
          <CardTitle className="text-base font-medium">{title}</CardTitle>
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>
        <div
          className="h-[400px] w-full"
          style={{ height: "100%", minHeight: "400px" }}
          onMouseLeave={handleMouseLeave}
        >
          <ResponsiveSankey
            data={mockData}
            margin={{ top: 20, right: 160, bottom: 20, left: 50 }}
            align="justify"
            colors={getNodeColor}
            nodeOpacity={1}
            nodeHoverOpacity={1}
            nodeThickness={18}
            nodeSpacing={24}
            nodeBorderWidth={0}
            nodeBorderColor={{
              from: "color",
              modifiers: [["darker", 0.8]],
            }}
            linkOpacity={0.3}
            linkHoverOpacity={0.7}
            linkHoverOthersOpacity={0.1}
            linkContract={3}
            enableLinkGradient={true}
            labelPosition="outside"
            labelOrientation="horizontal"
            labelPadding={16}
            labelTextColor={{
              from: "color",
              modifiers: [["darker", 1]],
            }}
            legends={[
              {
                anchor: "right",
                direction: "column",
                translateX: 130,
                itemWidth: 100,
                itemHeight: 14,
                itemDirection: "right-to-left",
                itemsSpacing: 2,
                itemTextColor: "#999",
                symbolSize: 14,
                effects: [
                  {
                    on: "hover",
                    style: {
                      itemTextColor: "#000",
                    },
                  },
                ],
              },
            ]}
            animate={true}
            motionConfig="gentle"
            nodeTooltip={({ node }) => (
              <div
                style={{
                  background: "white",
                  padding: "9px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
              >
                <div style={{ marginBottom: 5 }}>
                  <strong>{getNodeLabel(node)}</strong>
                </div>
                <div>
                  总书籍: <strong>{node.value}</strong> 本
                </div>
              </div>
            )}
            linkTooltip={({ link }) => (
              <div
                style={{
                  background: "white",
                  padding: "9px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
              >
                <div>
                  <strong>{getNodeLabel(link.source)}</strong> → <strong>{getNodeLabel(link.target)}</strong>
                </div>
                <div>
                  书籍数量: <strong>{link.value}</strong> 本
                </div>
              </div>
            )}
            onNodeMouseEnter={handleNodeHover}
            getLinkColor={getLinkColor}
          />
        </div>
      </CardContent>
    </Card>
  )
}
