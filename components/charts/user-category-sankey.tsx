"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Network, Maximize2, AlertCircle, Loader2, Database, RefreshCw, ExternalLink } from "lucide-react"
import { ResponsiveSankey } from "@nivo/sankey"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"

interface SankeyNode {
  id: string
  name?: string
  nodeColor?: string
}

interface SankeyLink {
  source: string
  target: string
  value: number
}

interface UserCategorySankeyProps {
  title?: string
  subtitle?: string
  data?: {
    nodes: SankeyNode[]
    links: SankeyLink[]
  }
  year?: number
  month?: number
  isLoading?: boolean
  error?: string
}

export function UserCategorySankey({
  title = "用户与类别关系图",
  subtitle = "用户阅读不同类别书籍的流量分布",
  data,
  year,
  month,
  isLoading = false,
  error,
}: UserCategorySankeyProps) {
  const [hoveredNode, setHoveredNode] = useState<string | null>(null)
  const [sankeyData, setSankeyData] = useState<{ nodes: SankeyNode[]; links: SankeyLink[] } | null>(null)
  const [loading, setLoading] = useState<boolean>(isLoading)
  const [errorMessage, setErrorMessage] = useState<string | null>(error || null)
  const [cacheStatus, setCacheStatus] = useState<"hit" | "miss" | null>(null)
  const [lastUpdated, setLastUpdated] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [apiResponse, setApiResponse] = useState<any>(null)

  // 如果提供了data属性，直接使用它
  useEffect(() => {
    if (data) {
      setSankeyData(data)
    }
  }, [data])

  // 从API获取数据的函数
  const fetchSankeyData = async (showToast = false) => {
    if (!year || !month) return

    setLoading(true)
    setErrorMessage(null)

    try {
      const response = await fetch(`/api/sankey-data/${year}/${month}`)
      const responseData = await response.json()
      setApiResponse(responseData)

      // 获取缓存状态
      const cacheHeader = response.headers.get("X-Cache")
      setCacheStatus(cacheHeader === "HIT" ? "hit" : "miss")

      if (!response.ok) {
        throw new Error(responseData.error || `服务器返回错误: ${response.status}`)
      }

      // 检查数据是否为空
      if (responseData.nodes && responseData.nodes.length === 0) {
        setErrorMessage(`${year}年${month}月没有可用的数据`)
        setSankeyData(null)
      } else {
        setSankeyData(responseData)
      }

      // 设置最后更新时间
      if (responseData.meta?.generatedAt) {
        setLastUpdated(new Date(responseData.meta.generatedAt).toLocaleString())
      }

      if (showToast) {
        toast({
          title: "数据已刷新",
          description: cacheHeader === "HIT" ? "数据从缓存加载" : "数据已从数据库重新加载",
        })
      }
    } catch (error) {
      console.error("Failed to fetch sankey data:", error)
      setErrorMessage(error instanceof Error ? error.message : "加载数据失败")
      setSankeyData(null)

      if (showToast) {
        toast({
          title: "刷新失败",
          description: error instanceof Error ? error.message : "加载数据失败",
          variant: "destructive",
        })
      }
    } finally {
      setLoading(false)
    }
  }

  // 如果提供了year和month但没有提供data，从API获取数据
  useEffect(() => {
    if (!data && year && month) {
      fetchSankeyData()
    }
  }, [data, year, month])

  // 检查数据是否为空
  const hasData =
    sankeyData && sankeyData.nodes && sankeyData.nodes.length > 0 && sankeyData.links && sankeyData.links.length > 0

  // 自定义节点颜色
  const getNodeColor = (node: any) => {
    // 如果节点有自定义颜色，使用自定义颜色
    if (node.nodeColor) {
      return node.nodeColor
    }

    // 用户节点使用蓝色系，类别节点使用各自的颜色
    if (node.id.startsWith("user_")) {
      return hoveredNode === node.id ? "#2563eb" : "#3b82f6"
    }

    // 默认颜色
    return "#9ca3af"
  }

  // 自定义链接颜色
  const getLinkColor = (link: any) => {
    const sourceNode = sankeyData?.nodes.find((n) => n.id === link.source)
    const targetNode = sankeyData?.nodes.find((n) => n.id === link.target)

    // 如果源节点或目标节点被悬停，高亮链接
    if (hoveredNode === link.source || hoveredNode === link.target) {
      return "rgba(59, 130, 246, 0.8)"
    }

    // 默认链接颜色 - 使用目标节点的颜色但透明度降低
    if (targetNode && targetNode.nodeColor) {
      return targetNode.nodeColor.replace("rgb", "rgba").replace(")", ", 0.3)")
    }

    return "rgba(203, 213, 225, 0.3)"
  }

  // 自定义节点标签
  const getNodeLabel = (node: any) => {
    if (node.name) {
      return node.name
    }

    if (node.id.startsWith("user_")) {
      return node.id.replace("user_", "")
    }

    return node.id
  }

  // 处理节点悬停
  const handleNodeHover = (node: any) => {
    setHoveredNode(node.id)
  }

  // 处理鼠标离开
  const handleMouseLeave = () => {
    setHoveredNode(null)
  }

  // 手动刷新数据
  const handleRefresh = async () => {
    if (!year || !month) return

    // 先清除特���缓存
    try {
      await fetch(`/api/cache?key=sankey-data-${year}-${month}`, { method: "DELETE" })
    } catch (error) {
      console.error("Failed to clear cache:", error)
      // 继续尝试刷新数据，即使缓存清除失败
    }

    // 重新加载数据
    fetchSankeyData(true)
  }

  // 重试加载
  const handleRetry = () => {
    setRetryCount((prev) => prev + 1)
    fetchSankeyData(true)
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Network className="h-4 w-4 mr-2 text-muted-foreground" />
            <CardTitle className="text-base font-medium">{title}</CardTitle>

            {/* 缓存状态指示器 */}
            {cacheStatus && (
              <Badge variant={cacheStatus === "hit" ? "secondary" : "outline"} className="ml-2">
                <Database className="h-3 w-3 mr-1" />
                {cacheStatus === "hit" ? "缓存" : "实时"}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {/* 新增: 在新页面查看按钮 - 使用更明显的Button组件 */}
            {year && month && (
              <Button variant="outline" size="sm" asChild className="h-8 px-2 text-xs" title="在新页面查看">
                <Link href={`/chart/sankey/${year}/${month}`}>
                  <ExternalLink className="h-4 w-4 mr-1" />
                  <span>查看详情</span>
                </Link>
              </Button>
            )}

            {/* 刷新按钮 */}
            {year && month && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={loading}
                className="h-8 w-8 p-0"
                title="刷新数据"
              >
                <RefreshCw className={`h-4 w-4 text-gray-500 ${loading ? "animate-spin" : ""}`} />
                <span className="sr-only">刷新</span>
              </Button>
            )}

            {/* 全屏按钮 */}
            {year && month && (
              <Link
                href={`/fullscreen/sankey/${year}/${month}`}
                target="_blank"
                className="p-1 rounded-md hover:bg-gray-100 transition-colors"
                title="全屏查看"
              >
                <Maximize2 className="h-4 w-4 text-gray-500" />
              </Link>
            )}
          </div>
        </div>
        <div className="flex justify-between items-center">
          <p className="text-xs text-muted-foreground">{subtitle}</p>
          {lastUpdated && <p className="text-xs text-muted-foreground">最后更新: {lastUpdated}</p>}
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="h-[400px] w-full flex flex-col items-center justify-center text-gray-500">
            <Loader2 className="h-12 w-12 mb-4 text-gray-300 animate-spin" />
            <p className="text-lg font-medium">加载数据中...</p>
          </div>
        ) : errorMessage ? (
          <div className="h-[400px] w-full flex flex-col items-center justify-center text-gray-500">
            <AlertCircle className="h-12 w-12 mb-4 text-red-300" />
            <p className="text-lg font-medium text-red-500">加载数据出错</p>
            <p className="text-sm mt-2 mb-4 max-w-md text-center">{errorMessage}</p>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleRetry}>
                <RefreshCw className="h-4 w-4 mr-2" />
                重试加载
              </Button>
              {process.env.NODE_ENV === "development" && apiResponse && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    console.log("API Response:", apiResponse)
                    toast({
                      title: "API响应已记录",
                      description: "详细信息已输出到控制台",
                    })
                  }}
                >
                  查看API响应
                </Button>
              )}
            </div>
          </div>
        ) : hasData ? (
          <div
            className="h-[400px] w-full"
            style={{ height: "100%", minHeight: "400px" }}
            onMouseLeave={handleMouseLeave}
          >
            <ResponsiveSankey
              data={sankeyData!}
              margin={{ top: 20, right: 160, bottom: 20, left: 50 }}
              align="justify"
              colors={getNodeColor}
              nodeOpacity={1}
              nodeHoverOpacity={1}
              nodeThickness={18}
              nodeSpacing={24}
              nodeBorderWidth={0}
              nodeBorderColor={{
                from: "color",
                modifiers: [["darker", 0.8]],
              }}
              linkOpacity={0.3}
              linkHoverOpacity={0.7}
              linkHoverOthersOpacity={0.1}
              linkContract={3}
              enableLinkGradient={true}
              labelPosition="outside"
              labelOrientation="horizontal"
              labelPadding={16}
              labelTextColor={{
                from: "color",
                modifiers: [["darker", 1]],
              }}
              legends={[
                {
                  anchor: "right",
                  direction: "column",
                  translateX: 130,
                  itemWidth: 100,
                  itemHeight: 14,
                  itemDirection: "right-to-left",
                  itemsSpacing: 2,
                  itemTextColor: "#999",
                  symbolSize: 14,
                  effects: [
                    {
                      on: "hover",
                      style: {
                        itemTextColor: "#000",
                      },
                    },
                  ],
                },
              ]}
              animate={true}
              motionConfig="gentle"
              nodeTooltip={({ node }) => (
                <div
                  style={{
                    background: "white",
                    padding: "9px 12px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                  }}
                >
                  <div style={{ marginBottom: 5 }}>
                    <strong>{getNodeLabel(node)}</strong>
                  </div>
                  <div>
                    总书籍: <strong>{node.value}</strong> 本
                  </div>
                </div>
              )}
              linkTooltip={({ link }) => (
                <div
                  style={{
                    background: "white",
                    padding: "9px 12px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                  }}
                >
                  <div>
                    <strong>{getNodeLabel(link.source)}</strong> → <strong>{getNodeLabel(link.target)}</strong>
                  </div>
                  <div>
                    书籍数量: <strong>{link.value}</strong> 本
                  </div>
                </div>
              )}
              onNodeMouseEnter={handleNodeHover}
              getLinkColor={getLinkColor}
            />
          </div>
        ) : (
          <div className="h-[400px] w-full flex flex-col items-center justify-center text-gray-500">
            <AlertCircle className="h-12 w-12 mb-4 text-gray-300" />
            <p className="text-lg font-medium">暂无数据</p>
            <p className="text-sm mt-2 mb-4">该时间段内没有用户阅读记录</p>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
