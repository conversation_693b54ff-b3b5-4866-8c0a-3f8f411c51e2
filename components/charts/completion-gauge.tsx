"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer } from "recharts"
import { CheckCircle } from "lucide-react"

// 定义更丰富的完成状态颜色
const completionColors = {
  completed: "#4CAF50", // 绿色
  inProgress: "#2196F3", // 蓝色
  notStarted: "#FFC107", // 黄色
}

interface CompletionGaugeProps {
  title?: string
  subtitle?: string
  completed: number
  total: number
  completedLabel?: string
  remainingLabel?: string
}

export function CompletionGauge({
  title = "完成进度",
  subtitle = "已完成与总数比例",
  completed,
  total,
  completedLabel = "已完成",
  remainingLabel = "进行中",
}: CompletionGaugeProps) {
  const remaining = total - completed
  const percentage = total > 0 ? Math.round((completed / total) * 100) : 0

  const data = [
    { name: completedLabel, value: completed, color: completionColors.completed },
    { name: remainingLabel, value: remaining, color: completionColors.inProgress },
  ]

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center">
          <CheckCircle className="h-4 w-4 mr-2 text-muted-foreground" />
          <CardTitle className="text-base font-medium">{title}</CardTitle>
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>
        <div className="h-[200px] relative">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                startAngle={90}
                endAngle={-270}
                innerRadius={60}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value"
                stroke="none"
              >
                {data.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={entry.color}
                    style={{
                      filter: "drop-shadow(0px 0px 3px rgba(0,0,0,0.1))",
                      transition: "all 0.3s ease",
                    }}
                  />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <div className="text-4xl font-bold">{percentage}%</div>
            <div className="text-xs text-muted-foreground mt-1">
              {completed}/{total}
            </div>
          </div>
        </div>
        <div className="flex justify-center gap-4 mt-2">
          <div className="flex items-center">
            <span
              className="inline-block w-3 h-3 mr-2 rounded-full"
              style={{ backgroundColor: completionColors.completed }}
            />
            <span className="text-xs">{completedLabel}</span>
          </div>
          <div className="flex items-center">
            <span
              className="inline-block w-3 h-3 mr-2 rounded-full"
              style={{ backgroundColor: completionColors.inProgress }}
            />
            <span className="text-xs">{remainingLabel}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
