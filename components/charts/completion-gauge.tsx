"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart2 } from "lucide-react"

interface CompletionGaugeProps {
  title?: string
  subtitle?: string
  completed: number
  total: number
  completedPercentage: number
  inProgressPercentage: number
}

export function CompletionGauge({
  title = "阅读完成情况",
  subtitle = "已读完与进行中的书籍比例",
  completed,
  total,
  completedPercentage,
  inProgressPercentage,
}: CompletionGaugeProps) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center">
          <BarChart2 className="h-4 w-4 mr-2 text-muted-foreground" />
          <CardTitle className="text-base font-medium">{title}</CardTitle>
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center">
          {/* Gauge Chart */}
          <div className="relative w-48 h-24 mb-8">
            <div className="absolute w-full h-full overflow-hidden">
              <div className="absolute top-0 w-full h-full bg-gray-200 rounded-t-full"></div>
              <div
                className="absolute top-0 w-full h-full bg-teal-500 rounded-t-full"
                style={{
                  clipPath: `polygon(0 100%, 100% 100%, 100% ${100 - completedPercentage}%, 0 ${100 - completedPercentage}%)`,
                }}
              ></div>
            </div>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1/2 bg-gray-400"></div>
            <div className="absolute bottom-0 left-0 w-full h-1 bg-gray-400"></div>
            <div className="absolute bottom-[-30px] left-1/2 transform -translate-x-1/2 text-lg font-bold">
              {completedPercentage.toFixed(1)}%
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-8 w-full mt-4">
            <div className="flex flex-col items-center">
              <div className="text-3xl font-bold">{completed}</div>
              <div className="flex items-center mt-1">
                <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                <span className="text-sm">已读完</span>
              </div>
              <div className="text-xs text-muted-foreground mt-1">{completedPercentage.toFixed(1)}%</div>
            </div>
            <div className="flex flex-col items-center">
              <div className="text-3xl font-bold">{total - completed}</div>
              <div className="flex items-center mt-1">
                <div className="w-3 h-3 rounded-full bg-gray-300 mr-2"></div>
                <span className="text-sm">未知/进行中</span>
              </div>
              <div className="text-xs text-muted-foreground mt-1">{inProgressPercentage.toFixed(1)}%</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
