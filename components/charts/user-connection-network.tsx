"use client"

import type React from "react"

import { useRef, useEffe<PERSON>, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Network, Users, BookOpen, Filter, ZoomIn, ZoomOut, RotateCcw, Search } from "lucide-react"
import * as d3 from "d3"

// 定义数据类型
interface User {
  id: string
  name: string
  avatar: string
  books: number
}

interface Category {
  id: string
  name: string
  color: string
}

interface UserCategoryConnection {
  userId: string
  categoryId: string
  strength: number
}

interface UserConnectionNetworkProps {
  users?: User[]
  categories?: Category[]
  userCategoryConnections?: UserCategoryConnection[]
  year?: number
  title?: string
  subtitle?: string
}

// 模拟数据
const mockUsers: User[] = [
  { id: "user1", name: "小菜鸡", avatar: "小", books: 45 },
  { id: "user2", name: "Chris.W", avatar: "C", books: 19 },
  { id: "user3", name: "娃娃", avatar: "娃", books: 9 },
  { id: "user4", name: "梦田", avatar: "梦", books: 8 },
  { id: "user5", name: "杜佳霖", avatar: "杜", books: 8 },
  { id: "user6", name: "李明", avatar: "李", books: 7 },
  { id: "user7", name: "王晓", avatar: "王", books: 7 },
  { id: "user8", name: "张伟", avatar: "张", books: 6 },
  { id: "user9", name: "刘芳", avatar: "刘", books: 5 },
  { id: "user10", name: "陈晨", avatar: "陈", books: 5 },
]

const mockCategories: Category[] = [
  { id: "cat1", name: "文学类", color: "#FF6384" },
  { id: "cat2", name: "哲学类", color: "#36A2EB" },
  { id: "cat3", name: "社会科学类", color: "#FFCE56" },
  { id: "cat4", name: "历史类", color: "#4BC0C0" },
  { id: "cat5", name: "科学技术类", color: "#9966FF" },
  { id: "cat6", name: "其他", color: "#C9CBCF" },
]

// 模拟用户-类别连接数据
const mockUserCategoryConnections: UserCategoryConnection[] = [
  { userId: "user1", categoryId: "cat1", strength: 18 },
  { userId: "user1", categoryId: "cat2", strength: 8 },
  { userId: "user1", categoryId: "cat3", strength: 5 },
  { userId: "user1", categoryId: "cat4", strength: 1 },
  { userId: "user1", categoryId: "cat5", strength: 2 },
  { userId: "user1", categoryId: "cat6", strength: 11 },

  { userId: "user2", categoryId: "cat1", strength: 10 },
  { userId: "user2", categoryId: "cat2", strength: 4 },
  { userId: "user2", categoryId: "cat4", strength: 2 },
  { userId: "user2", categoryId: "cat5", strength: 1 },
  { userId: "user2", categoryId: "cat6", strength: 2 },

  { userId: "user3", categoryId: "cat1", strength: 7 },
  { userId: "user3", categoryId: "cat6", strength: 2 },

  { userId: "user4", categoryId: "cat1", strength: 6 },
  { userId: "user4", categoryId: "cat2", strength: 5 },

  { userId: "user5", categoryId: "cat1", strength: 5 },
  { userId: "user5", categoryId: "cat3", strength: 2 },
  { userId: "user5", categoryId: "cat6", strength: 2 },

  { userId: "user6", categoryId: "cat4", strength: 3 },
  { userId: "user6", categoryId: "cat3", strength: 4 },

  { userId: "user7", categoryId: "cat4", strength: 2 },
  { userId: "user7", categoryId: "cat1", strength: 5 },

  { userId: "user8", categoryId: "cat5", strength: 1 },
  { userId: "user8", categoryId: "cat6", strength: 3 },
  { userId: "user8", categoryId: "cat3", strength: 2 },

  { userId: "user9", categoryId: "cat6", strength: 2 },
  { userId: "user9", categoryId: "cat1", strength: 3 },

  { userId: "user10", categoryId: "cat1", strength: 3 },
  { userId: "user10", categoryId: "cat3", strength: 2 },
]

export function UserConnectionNetwork({
  users = mockUsers,
  categories = mockCategories,
  userCategoryConnections = mockUserCategoryConnections,
  year = new Date().getFullYear(),
  title = "用户关系网络",
  subtitle = "展示用户通过共同阅读领域形成的连接",
}: UserConnectionNetworkProps) {
  const svgRef = useRef<SVGSVGElement>(null)
  const [selectedCategories, setSelectedCategories] = useState<string[]>(categories.map((c) => c.id))
  const [strengthThreshold, setStrengthThreshold] = useState(1)
  const [hoveredNode, setHoveredNode] = useState<string | null>(null)
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [scale, setScale] = useState(1)
  const [tooltip, setTooltip] = useState<{
    visible: boolean
    x: number
    y: number
    content: React.ReactNode
  }>({
    visible: false,
    x: 0,
    y: 0,
    content: null,
  })
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedUsers, setSelectedUsers] = useState<string[]>(users.map((u) => u.id))

  // 处理类别筛选
  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories((prev) => {
      if (prev.includes(categoryId)) {
        return prev.filter((id) => id !== categoryId)
      } else {
        return [...prev, categoryId]
      }
    })
  }

  // 处理用户筛选
  const handleUserToggle = (userId: string) => {
    setSelectedUsers((prev) => {
      if (prev.includes(userId)) {
        return prev.filter((id) => id !== userId)
      } else {
        return [...prev, userId]
      }
    })
  }

  // 选择所有用户
  const selectAllUsers = () => {
    setSelectedUsers(users.map((u) => u.id))
  }

  // 取消选择所有用户
  const deselectAllUsers = () => {
    setSelectedUsers([])
  }

  // 处理节点点击
  const handleNodeClick = (nodeId: string, nodeType: "user" | "category") => {
    if (selectedNode === nodeId) {
      setSelectedNode(null)
      // 重置筛选
      selectAllUsers()
      setSelectedCategories(categories.map((c) => c.id))
    } else {
      setSelectedNode(nodeId)

      if (nodeType === "user") {
        // 如果点击了用户节点，只显示与该用户相关的类别和用户
        const relatedCategoryIds = userCategoryConnections
          .filter((conn) => conn.userId === nodeId && conn.strength >= strengthThreshold)
          .map((conn) => conn.categoryId)

        // 找出与这些类别相关的其他用户
        const relatedUserIds = new Set<string>()
        relatedUserIds.add(nodeId) // 添加选中的用户

        userCategoryConnections.forEach((conn) => {
          if (relatedCategoryIds.includes(conn.categoryId) && conn.strength >= strengthThreshold) {
            relatedUserIds.add(conn.userId)
          }
        })

        setSelectedUsers(Array.from(relatedUserIds))
        setSelectedCategories(relatedCategoryIds)
      } else if (nodeType === "category") {
        // 如果点击了类别节点，只显示与该类别相关的用户
        const relatedUserIds = userCategoryConnections
          .filter((conn) => conn.categoryId === nodeId && conn.strength >= strengthThreshold)
          .map((conn) => conn.userId)

        setSelectedUsers(relatedUserIds)
        setSelectedCategories([nodeId])
      }
    }
  }

  // 重置所有筛选
  const handleReset = () => {
    setSelectedCategories(categories.map((c) => c.id))
    setStrengthThreshold(1)
    setScale(1)
    setSelectedUsers(users.map((u) => u.id))
    setSelectedNode(null)
    setSearchTerm("")
  }

  // 缩放控制
  const handleZoomIn = () => setScale((prev) => Math.min(prev + 0.2, 2))
  const handleZoomOut = () => setScale((prev) => Math.max(prev - 0.2, 0.5))

  // 根据筛选条件过滤连接
  const filteredConnections = userCategoryConnections.filter((conn) => {
    return (
      selectedCategories.includes(conn.categoryId) &&
      selectedUsers.includes(conn.userId) &&
      conn.strength >= strengthThreshold
    )
  })

  // 获取活跃用户和类别（有连接的用户和类别）
  const activeUserIds = new Set<string>()
  const activeCategoryIds = new Set<string>()

  filteredConnections.forEach((conn) => {
    activeUserIds.add(conn.userId)
    activeCategoryIds.add(conn.categoryId)
  })

  // 如果有选中的节点，确保它在活跃节点中
  if (selectedNode) {
    const selectedNodeType = selectedNode.startsWith("user") ? "user" : "category"
    if (selectedNodeType === "user") {
      activeUserIds.add(selectedNode)
    } else {
      activeCategoryIds.add(selectedNode)
    }
  }

  const activeUsers = users.filter((user) => activeUserIds.has(user.id))
  const activeCategories = categories.filter((category) => activeCategoryIds.has(category.id))

  // 根据搜索词过滤用户列表
  const filteredUsers = users.filter((user) => user.name.toLowerCase().includes(searchTerm.toLowerCase()))

  // 使用D3.js创建力导向图
  useEffect(() => {
    if (!svgRef.current || (activeUsers.length === 0 && activeCategories.length === 0)) return

    // 清除之前的图形
    d3.select(svgRef.current).selectAll("*").remove()

    const width = svgRef.current.clientWidth
    const height = svgRef.current.clientHeight

    // 创建SVG
    const svg = d3
      .select(svgRef.current)
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", [0, 0, width, height])
      .attr("style", "max-width: 100%; height: auto;")

    // 创建缩放组
    const g = svg.append("g")

    // 创建箭头标记定义
    const defs = svg.append("defs")
    defs
      .append("marker")
      .attr("id", "arrowhead")
      .attr("viewBox", "0 -5 10 10")
      .attr("refX", 20) // 调整箭头位置，使其不覆盖节点
      .attr("refY", 0)
      .attr("orient", "auto")
      .attr("markerWidth", 6)
      .attr("markerHeight", 6)
      .attr("xoverflow", "visible")
      .append("path")
      .attr("d", "M 0,-3 L 6,0 L 0,3")
      .attr("fill", "#999")
      .attr("stroke", "none")

    // 准备节点数据 - 包括用户和类别
    const userNodes = activeUsers.map((user) => ({
      id: user.id,
      name: user.name,
      avatar: user.avatar,
      books: user.books,
      type: "user" as const,
      radius: Math.max(22, Math.min(30, 20 + user.books / 6)), // 减小节点尺寸
      selected: user.id === selectedNode,
    }))

    const categoryNodes = activeCategories.map((category) => ({
      id: category.id,
      name: category.name,
      color: category.color,
      type: "category" as const,
      radius: 24, // 增大类别节点尺寸，使其更明显
      selected: category.id === selectedNode,
    }))

    const nodes = [...userNodes, ...categoryNodes]

    // 初始化节点位置 - 采用更明确的两侧布局
    const initializeNodePositions = () => {
      // 用户节点放在左侧，均匀分布在垂直方向
      const userSectionHeight = height * 0.8
      const userSpacing = userSectionHeight / (userNodes.length + 1)

      userNodes.forEach((node, i) => {
        // 在左侧区域垂直排列，并添加一些水平随机性
        node.x = width * 0.2 + (Math.random() - 0.5) * width * 0.1
        node.y = height * 0.1 + (i + 1) * userSpacing
      })

      // 类别节点放在右侧，均匀分布在垂直方向
      const catSectionHeight = height * 0.8
      const catSpacing = catSectionHeight / (categoryNodes.length + 1)

      categoryNodes.forEach((node, i) => {
        // 在右侧区域垂直排列，并添加一些水平随机性
        node.x = width * 0.8 + (Math.random() - 0.5) * width * 0.1
        node.y = height * 0.1 + (i + 1) * catSpacing
      })
    }

    // 调用函数设置初始位置
    initializeNodePositions()

    // 准备连接数据 - 用户到类别的连接
    const links = filteredConnections.map((conn) => ({
      source: conn.userId,
      target: conn.categoryId,
      strength: conn.strength,
      value: conn.strength, // 用于力导向图的连接强度
    }))

    // 创建力导向模拟
    const simulation = d3
      .forceSimulation(nodes)
      .force(
        "link",
        d3
          .forceLink(links)
          .id((d: any) => d.id)
          .distance(250), // 进一步增加连接距离，减少节点重叠
      )
      .force("charge", d3.forceManyBody().strength(-3000)) // 进一步增加排斥力
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force(
        "collision",
        d3
          .forceCollide()
          .radius((d: any) => d.radius + 60), // 进一步增加碰撞半径，防止重叠
      )
      // 增强X方向力，使用户和类别更明确地分开
      .force(
        "x",
        d3
          .forceX()
          .x((d: any) => {
            return d.type === "user" ? width * 0.2 : width * 0.8
          })
          .strength(0.5), // 增强X方向力
      )
      // Y方向力，使节点更均匀地分布在垂直方向
      .force("y", d3.forceY(height / 2).strength(0.05))

    // 绘制连接 - 使用曲线而非直线
    const link = g
      .append("g")
      .attr("class", "links")
      .selectAll("path") // 使用path而非line
      .data(links)
      .join("path") // 使用path而非line
      .attr("stroke-width", (d) => Math.sqrt(d.strength) * 1.5)
      .attr("stroke", (d) => {
        // 使用类别的颜色
        const category = categories.find((c) => c.id === d.target)
        return category ? category.color : "#999"
      })
      .attr("stroke-opacity", 0.6)
      .attr("fill", "none") // 确保路径没有填充
      .attr("class", "link")
      .on("mouseover", (event, d) => {
        // 显示tooltip
        const user = users.find((u) => u.id === d.source)
        const category = categories.find((c) => c.id === d.target)

        if (user && category) {
          setTooltip({
            visible: true,
            x: event.pageX,
            y: event.pageY,
            content: (
              <div>
                <div className="font-medium">
                  {user.name} → {category.name}
                </div>
                <div className="text-sm mt-1">阅读强度: {d.strength}</div>
              </div>
            ),
          })
        }
      })
      .on("mouseout", () => {
        setTooltip({ ...tooltip, visible: false })
      })

    // 绘制节点
    const node = g
      .append("g")
      .attr("class", "nodes")
      .selectAll(".node")
      .data(nodes)
      .join("g")
      .attr("class", "node")
      .call(d3.drag().on("start", dragstarted).on("drag", dragged).on("end", dragended))
      .on("mouseover", (event, d) => {
        setHoveredNode(d.id)

        // 显示tooltip
        setTooltip({
          visible: true,
          x: event.pageX,
          y: event.pageY,
          content:
            d.type === "user" ? (
              <div>
                <div className="font-medium">{d.name}</div>
                <div className="text-sm">阅读书籍: {d.books}本</div>
                <div className="text-xs mt-1">
                  相关类别: {filteredConnections.filter((conn) => conn.userId === d.id).length}个
                </div>
              </div>
            ) : (
              <div>
                <div className="font-medium">{d.name}</div>
                <div className="text-sm">
                  相关用户: {filteredConnections.filter((conn) => conn.categoryId === d.id).length}人
                </div>
              </div>
            ),
        })
      })
      .on("mouseout", () => {
        setHoveredNode(null)
        setTooltip({ ...tooltip, visible: false })
      })
      .on("click", (event, d) => {
        event.stopPropagation() // 阻止事件冒泡
        handleNodeClick(d.id, d.type)
      })

    // 为用户节点添加背景矩形，使标签更清晰
    node
      .filter((d: any) => d.type === "user")
      .append("rect")
      .attr("width", (d: any) => d.name.length * 14 + 10) // 根据名称长度调整宽度
      .attr("height", 22)
      .attr("x", (d: any) => -(d.name.length * 14 + 10) / 2) // 居中
      .attr("y", -11) // 垂直居中
      .attr("rx", 4) // 圆角
      .attr("fill", "white")
      .attr("stroke", (d: any) => (d.selected ? "#1890ff" : "#d1d5db"))
      .attr("stroke-width", (d: any) => (d.selected ? 2 : 1))
      .attr("opacity", 0.9)

    // 为类别节点添加背景矩形，使标签更清晰
    node
      .filter((d: any) => d.type === "category")
      .append("rect")
      .attr("width", (d: any) => d.name.length * 14 + 10) // 根据名称长度调整宽度
      .attr("height", 22)
      .attr("x", (d: any) => -(d.name.length * 14 + 10) / 2) // 居中
      .attr("y", -11) // 垂直居中
      .attr("rx", 4) // 圆角
      .attr("fill", "white")
      .attr("stroke", (d: any) => d.color)
      .attr("stroke-width", (d: any) => (d.selected ? 2 : 1))
      .attr("opacity", 0.9)

    // 为用户节点添加圆形指示器
    node
      .filter((d: any) => d.type === "user")
      .append("circle")
      .attr("r", 8)
      .attr("cx", (d: any) => -(d.name.length * 14 + 10) / 2 + 12) // 放在文本左侧
      .attr("fill", "#3b82f6")
      .attr("class", "user-indicator")

    // 为类别节点添加颜色指示器
    node
      .filter((d: any) => d.type === "category")
      .append("circle")
      .attr("r", 8)
      .attr("cx", (d: any) => -(d.name.length * 14 + 10) / 2 + 12) // 放在文本左侧
      .attr("fill", (d: any) => d.color)
      .attr("class", "category-indicator")

    // 添加用户名称
    node
      .filter((d: any) => d.type === "user")
      .append("text")
      .attr("dy", ".35em")
      .attr("text-anchor", "middle")
      .attr("font-size", 12)
      .attr("font-weight", "bold")
      .text((d: any) => d.name)
      .attr("class", "node-text")

    // 添加类别名称
    node
      .filter((d: any) => d.type === "category")
      .append("text")
      .attr("dy", ".35em")
      .attr("text-anchor", "middle")
      .attr("font-size", 12)
      .attr("font-weight", "bold")
      .text((d: any) => d.name)
      .attr("class", "node-text")

    // 更新力导向图
    simulation.on("tick", () => {
      // 减小边界填充，使用更多空间
      const padding = 10

      // 添加额外的约束，防止节点重叠
      nodes.forEach((d: any) => {
        // 限制X坐标范围，使用户和类别更明确地分开
        if (d.type === "user") {
          d.x = Math.max(padding, Math.min(width * 0.4, d.x))
        } else {
          d.x = Math.max(width * 0.6, Math.min(width - padding, d.x))
        }

        // 限制Y坐标范围
        d.y = Math.max(padding, Math.min(height - padding, d.y))
      })

      // 更新连接线 - 使用曲线路径
      link.attr("d", (d: any) => {
        const sourceX = d.source.x
        const sourceY = d.source.y
        const targetX = d.target.x
        const targetY = d.target.y

        // 计算控制点，使曲线更平滑
        // 控制点距离源点和目标点的距离
        const dx = targetX - sourceX
        const dy = targetY - sourceY
        const dr = Math.sqrt(dx * dx + dy * dy)

        // 使用二次贝塞尔曲线
        const midX = (sourceX + targetX) / 2
        // 向中心偏移，减少交叉
        const offsetX = (width / 2 - midX) * 0.1

        return `M${sourceX},${sourceY} Q${midX + offsetX},${(sourceY + targetY) / 2} ${targetX},${targetY}`
      })

      node.attr("transform", (d: any) => `translate(${d.x},${d.y})`)
    })

    // 拖拽函数
    function dragstarted(event: any) {
      if (!event.active) simulation.alphaTarget(0.3).restart()
      event.subject.fx = event.subject.x
      event.subject.fy = event.subject.y
    }

    function dragged(event: any) {
      event.subject.fx = event.x
      event.subject.fy = event.y
    }

    function dragended(event: any) {
      if (!event.active) simulation.alphaTarget(0)
      event.subject.fx = null
      event.subject.fy = null
    }

    // 应用缩放
    g.attr("transform", `scale(${scale})`)
    g.attr("transform-origin", "center")

    // 高亮显示悬停的节点和相关连接
    if (hoveredNode) {
      const isUser = hoveredNode.startsWith("user")

      node.attr("opacity", (d: any) => {
        if (d.id === hoveredNode) return 1

        // 如果悬停的是用户，高亮显示与该用户相关的类别
        if (isUser && d.type === "category") {
          return filteredConnections.some((conn) => conn.userId === hoveredNode && conn.categoryId === d.id) ? 0.8 : 0.3
        }

        // 如果悬停的是类别，高亮显示与该类别相关的用户
        if (!isUser && d.type === "user") {
          return filteredConnections.some((conn) => conn.categoryId === hoveredNode && conn.userId === d.id) ? 0.8 : 0.3
        }

        return 0.3
      })

      link.attr("opacity", (d) => {
        if (isUser) {
          return d.source.id === hoveredNode ? 1 : 0.1
        } else {
          return d.target.id === hoveredNode ? 1 : 0.1
        }
      })
    } else if (selectedNode) {
      // 如果有选中的节点，高亮显示该节点及其连接
      const isUser = selectedNode.startsWith("user")

      node.attr("opacity", (d: any) => {
        if (d.id === selectedNode) return 1

        // 如果选中的是用户，高亮显示与该用户相关的类别
        if (isUser && d.type === "category") {
          return filteredConnections.some((conn) => conn.userId === selectedNode && conn.categoryId === d.id)
            ? 0.8
            : 0.3
        }

        // 如果选中的是类别，高亮显示与该类别相关的用户
        if (!isUser && d.type === "user") {
          return filteredConnections.some((conn) => conn.categoryId === selectedNode && conn.userId === d.id)
            ? 0.8
            : 0.3
        }

        return 0.3
      })

      link.attr("opacity", (d) => {
        if (isUser) {
          return d.source.id === selectedNode ? 1 : 0.1
        } else {
          return d.target.id === selectedNode ? 1 : 0.1
        }
      })
    } else {
      node.attr("opacity", 1)
      link.attr("opacity", 0.6)
    }

    // 添加点击空白区域取消选择的事件处理
    svg.on("click", () => {
      if (selectedNode) {
        setSelectedNode(null)
        selectAllUsers() // 重置用户筛选
        setSelectedCategories(categories.map((c) => c.id)) // 重置类别筛选
      }
    })

    // 清理函数
    return () => {
      simulation.stop()
    }
  }, [activeUsers, activeCategories, filteredConnections, scale, hoveredNode, selectedNode, users, categories])

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Network className="h-5 w-5 mr-2 text-muted-foreground" />
            <CardTitle className="text-base font-medium">{title}</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleZoomIn} title="放大">
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleZoomOut} title="缩小">
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleReset} title="重置">
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4">
          {/* 筛选面板 */}
          <div className="w-full md:w-64 space-y-4">
            <div>
              <div className="flex items-center mb-2">
                <Filter className="h-4 w-4 mr-1" />
                <h3 className="text-sm font-medium">筛选选项</h3>
              </div>

              <div className="space-y-3">
                <div>
                  <h4 className="text-xs font-medium mb-2">连接强度阈值: {strengthThreshold}</h4>
                  <Slider
                    value={[strengthThreshold]}
                    min={1}
                    max={10}
                    step={1}
                    onValueChange={(value) => setStrengthThreshold(value[0])}
                  />
                </div>

                <div>
                  <h4 className="text-xs font-medium mb-2">类别筛选:</h4>
                  <div className="flex flex-wrap gap-1">
                    {categories.map((category) => (
                      <Badge
                        key={category.id}
                        variant={selectedCategories.includes(category.id) ? "default" : "outline"}
                        className="cursor-pointer"
                        style={{
                          backgroundColor: selectedCategories.includes(category.id) ? category.color : "transparent",
                          color: selectedCategories.includes(category.id) ? "white" : "inherit",
                          borderColor: category.color,
                        }}
                        onClick={() => handleCategoryToggle(category.id)}
                      >
                        {category.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-1" />
                  <h3 className="text-sm font-medium">用户筛选</h3>
                </div>
                <div className="flex gap-1">
                  <Button variant="outline" size="sm" className="h-6 text-xs px-2" onClick={selectAllUsers}>
                    全选
                  </Button>
                  <Button variant="outline" size="sm" className="h-6 text-xs px-2" onClick={deselectAllUsers}>
                    清除
                  </Button>
                </div>
              </div>

              <div className="relative mb-2">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="搜索用户..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 h-8 text-sm"
                />
              </div>

              <div className="max-h-40 overflow-y-auto space-y-1 border rounded-md p-2">
                {filteredUsers.map((user) => (
                  <div key={user.id} className="flex items-center text-xs">
                    <Checkbox
                      id={`user-${user.id}`}
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={() => handleUserToggle(user.id)}
                      className="mr-2 h-3 w-3"
                    />
                    <Label
                      htmlFor={`user-${user.id}`}
                      className={`flex items-center text-xs cursor-pointer ${
                        user.id === selectedNode ? "font-bold text-blue-600" : ""
                      }`}
                    >
                      <div className="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center mr-2 text-xs">
                        {user.avatar}
                      </div>
                      <span>{user.name}</span>
                      <span className="ml-auto text-muted-foreground">{user.books}本</span>
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <div className="flex items-center mb-2">
                <BookOpen className="h-4 w-4 mr-1" />
                <h3 className="text-sm font-medium">连接统计</h3>
              </div>
              <p className="text-xs text-muted-foreground">
                当前显示 {filteredConnections.length}/{userCategoryConnections.length} 个连接
              </p>
              <div className="mt-2 space-y-1">
                {categories
                  .filter((cat) => selectedCategories.includes(cat.id))
                  .map((category) => {
                    const categoryConnections = filteredConnections.filter(
                      (conn) => conn.categoryId === category.id,
                    ).length

                    return (
                      <div key={category.id} className="flex items-center text-xs">
                        <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: category.color }}></div>
                        <span>{category.name}</span>
                        <span className="ml-auto">{categoryConnections}个</span>
                      </div>
                    )
                  })}
              </div>
            </div>
          </div>

          {/* 网络图 */}
          <div className="flex-1 relative">
            <div className="h-[600px] bg-gray-50 rounded-md overflow-hidden">
              {activeUsers.length === 0 && activeCategories.length === 0 ? (
                <div className="h-full flex items-center justify-center">
                  <p className="text-muted-foreground">没有符合条件的连接</p>
                </div>
              ) : (
                <svg ref={svgRef} width="100%" height="100%"></svg>
              )}

              {/* 工具提示 */}
              {tooltip.visible && (
                <div
                  className="absolute bg-white p-2 rounded shadow-md text-xs z-10 max-w-xs"
                  style={{
                    left: `${tooltip.x}px`,
                    top: `${tooltip.y}px`,
                    transform: "translate(-50%, -100%)",
                  }}
                >
                  {tooltip.content}
                </div>
              )}
            </div>

            <div className="mt-2 text-xs text-muted-foreground">
              提示: 点击用户或类别节点可以筛选相关连接，点击空白区域重置筛选
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
