"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Legend } from "recharts"

// 月份名称
const monthNames = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]

interface YearlyReadingTrendProps {
  data: {
    month: number
    active_readers: number
    completed_books: number
  }[]
  year: number
}

export function YearlyReadingTrend({ data, year }: YearlyReadingTrendProps) {
  // 处理数据，确保所有月份都有数据点
  const chartData = Array.from({ length: 12 }, (_, i) => {
    const monthNumber = i + 1
    const monthData = data.find((item) => item.month === monthNumber)

    return {
      name: monthNames[i],
      month: monthNumber,
      activeReaders: monthData?.active_readers || 0,
      completedBooks: monthData?.completed_books || 0,
    }
  })

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle>{year}年阅读趋势</CardTitle>
        <CardDescription>每月活跃读者和完成书籍数量的变化趋势</CardDescription>
      </CardHeader>
      <CardContent className="h-[400px]">
        <ChartContainer
          config={{
            activeReaders: {
              label: "活跃读者",
              color: "hsl(var(--chart-1))",
            },
            completedBooks: {
              label: "完成书籍",
              color: "hsl(var(--chart-2))",
            },
          }}
        >
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Legend />
              <Line
                type="monotone"
                dataKey="activeReaders"
                stroke="var(--color-activeReaders)"
                name="活跃读者"
                strokeWidth={2}
                activeDot={{ r: 8 }}
              />
              <Line
                type="monotone"
                dataKey="completedBooks"
                stroke="var(--color-completedBooks)"
                name="完成书籍"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
