"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON> } from "recharts"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Clock } from "lucide-react"

// 添加类别颜色映射
const categoryColorMap: Record<string, string> = {
  文学类: "#FF6384",
  哲学类: "#36A2EB",
  社会科学类: "#FFCE56",
  历史类: "#4BC0C0",
  科学技术类: "#9966FF",
  其他: "#C9CBCF",
  文学: "#FF6384",
  小说: "#FF6384",
  历史: "#4BC0C0",
  科学: "#9966FF",
  哲学: "#36A2EB",
  心理学: "#FFBB28",
  经济: "#FFCE56",
  管理: "#00C49F",
  艺术: "#FF6384",
  社会科学: "#FFCE56",
  政治: "#FFCE56",
  传记: "#4BC0C0",
  技术: "#9966FF",
  教育: "#36A2EB",
  健康: "#00C49F",
  旅行: "#4BC0C0",
  科幻: "#9966FF",
  奇幻: "#FF6384",
  悬疑: "#FFBB28",
  儿童: "#00C49F",
  漫画: "#FF6384",
  诗歌: "#FF6384",
  散文: "#FF6384",
  杂志: "#C9CBCF",
  计算机: "#9966FF",
  科技: "#9966FF",
  宗教与哲学: "#36A2EB",
  个人发展: "#36A2EB",
}

// 预定义一组丰富多彩的颜色
const colorPalette = [
  "#8884d8",
  "#82ca9d",
  "#ffc658",
  "#0088FE",
  "#FF8042",
  "#FFBB28",
  "#00C49F",
  "#a4de6c",
  "#d0ed57",
  "#83a6ed",
  "#8dd1e1",
  "#6b486b",
  "#a05d56",
  "#d0743c",
  "#ff8c00",
  "#98abc5",
  "#7b6888",
  "#6b486b",
  "#a05d56",
  "#d0743c",
]

// 根据类别名称获取颜色
function getCategoryColor(category: string): string {
  // 如果类别在映射中存在，直接返回对应颜色
  if (categoryColorMap[category]) {
    return categoryColorMap[category]
  }

  // 否则根据类别名称生成哈希值，选择一个颜色
  let hash = 0
  for (let i = 0; i < category.length; i++) {
    hash = category.charCodeAt(i) + ((hash << 5) - hash)
  }

  // 使用哈希值选择颜色
  const index = Math.abs(hash) % colorPalette.length
  return colorPalette[index]
}

interface CategoryData {
  name: string
  value: number
  percentage: string
  color: string
}

interface CategoryPieChartProps {
  title?: string
  subtitle?: string
  data: CategoryData[]
  height?: number
}

export function CategoryPieChart({
  title = "图书类别分布",
  subtitle = "按类别统计的书籍数量",
  data,
  height = 300,
}: CategoryPieChartProps) {
  const [activeIndex, setActiveIndex] = useState<number | null>(null)
  const [clickedIndex, setClickedIndex] = useState<number | null>(null)
  const [isHovering, setIsHovering] = useState(false)

  // 鼠标离开图表区域时重置状态
  const handleChartMouseLeave = () => {
    setIsHovering(false)
    if (clickedIndex === null) {
      setActiveIndex(null)
    }
  }

  // 鼠标进入图表区域时设置状态
  const handleChartMouseEnter = () => {
    setIsHovering(true)
  }

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index)
    setIsHovering(true)
  }

  const onPieLeave = () => {
    setActiveIndex(null)
    setIsHovering(false)
  }

  const onPieClick = (_: any, index: number) => {
    if (clickedIndex === index) {
      // 如果点击的是当前已选中的扇区，则取消选中
      setClickedIndex(null)
      setActiveIndex(null)
    } else {
      // 否则选中点击的扇区
      setClickedIndex(index)
      setActiveIndex(index)
    }
  }

  // 自定义标签渲染函数
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }: any) => {
    // 如果鼠标悬停在某个扇区上但没有点击选择，只显示悬停的扇区标签
    if (!clickedIndex && isHovering && activeIndex !== null && activeIndex !== index) {
      return null
    }

    // 如果有点击选中的扇区，只显示该扇区的标签
    if (clickedIndex !== null && clickedIndex !== index) {
      return null
    }

    const RADIAN = Math.PI / 180

    // 根据扇区位置调整标签距离
    let radiusMultiplier = 1.5

    // 右侧扇区标签放得更远
    if (midAngle > -90 && midAngle < 90) {
      radiusMultiplier = 1.8
    }

    const radius = innerRadius + (outerRadius - innerRadius) * radiusMultiplier
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    // 如果是选中或悬停的扇区，使用更大的字体和加粗
    const isHighlighted = clickedIndex === index || (clickedIndex === null && activeIndex === index)
    const fontSize = isHighlighted ? 14 : 12
    const fontWeight = isHighlighted ? "700" : "500"

    return (
      <text
        x={x}
        y={y}
        fill={chartData[index].color || "#000"}
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        fontSize={fontSize}
        fontWeight={fontWeight}
      >
        {`${name} ${(percent * 100).toFixed(1)}%`}
      </text>
    )
  }

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded shadow text-xs">
          <p className="font-medium">{`${payload[0].name}: ${payload[0].value}`}</p>
          <p className="text-gray-500">{`${payload[0].payload.percentage}`}</p>
        </div>
      )
    }
    return null
  }

  const CustomLegend = (props: any) => {
    const { payload } = props

    if (!payload || !Array.isArray(payload) || payload.length === 0) {
      return null
    }

    // 强制将图例分成两行
    const firstRowCount = Math.ceil(payload.length / 2)
    const firstRow = payload.slice(0, firstRowCount)
    const secondRow = payload.slice(firstRowCount)

    return (
      <div className="w-full mt-2">
        {/* 第一行图例 */}
        <ul className="flex flex-wrap justify-center gap-2 mb-2">
          {firstRow.map((entry: any, index: number) => {
            const isActive = clickedIndex === null || clickedIndex === index

            return (
              <li
                key={`item-${index}`}
                className={`flex items-center cursor-pointer transition-opacity duration-300 ${isActive ? "opacity-100" : "opacity-40"}`}
                onClick={() => onPieClick(null, index)}
                onMouseEnter={() => setActiveIndex(index)}
                onMouseLeave={() => clickedIndex === null && setActiveIndex(null)}
              >
                <span
                  className={`inline-block w-3 h-3 mr-1 rounded-full transition-all duration-300 ${
                    clickedIndex === index || (clickedIndex === null && activeIndex === index) ? "w-4 h-4" : "w-3 h-3"
                  }`}
                  style={{ backgroundColor: entry.color }}
                />
                <span
                  className={`text-xs ${
                    clickedIndex === index || (clickedIndex === null && activeIndex === index) ? "font-bold" : ""
                  }`}
                >
                  {entry.name}
                </span>
              </li>
            )
          })}
        </ul>

        {/* 第二行图例 */}
        {secondRow.length > 0 && (
          <ul className="flex flex-wrap justify-center gap-2">
            {secondRow.map((entry: any, index: number) => {
              const originalIndex = index + firstRowCount
              const isActive = clickedIndex === null || clickedIndex === originalIndex

              return (
                <li
                  key={`item-${originalIndex}`}
                  className={`flex items-center cursor-pointer transition-opacity duration-300 ${isActive ? "opacity-100" : "opacity-40"}`}
                  onClick={() => onPieClick(null, originalIndex)}
                  onMouseEnter={() => setActiveIndex(originalIndex)}
                  onMouseLeave={() => clickedIndex === null && setActiveIndex(null)}
                >
                  <span
                    className={`inline-block w-3 h-3 mr-1 rounded-full transition-all duration-300 ${
                      clickedIndex === originalIndex || (clickedIndex === null && activeIndex === originalIndex)
                        ? "w-4 h-4"
                        : "w-3 h-3"
                    }`}
                    style={{ backgroundColor: entry.color }}
                  />
                  <span
                    className={`text-xs ${
                      clickedIndex === originalIndex || (clickedIndex === null && activeIndex === originalIndex)
                        ? "font-bold"
                        : ""
                    }`}
                  >
                    {entry.name}
                  </span>
                </li>
              )
            })}
          </ul>
        )}
      </div>
    )
  }

  // 确保数据按值从大到小排序，并应用颜色
  const chartData = [...data]
    .sort((a, b) => b.value - a.value)
    .map((item) => ({
      ...item,
      color: item.color || getCategoryColor(item.name),
    }))

  // 固定为两行图例，每行高度30px
  const chartHeight = 280
  const legendHeight = 60 // 两行图例，每行30px
  const totalHeight = chartHeight + legendHeight

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex items-center">
          <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
          <CardTitle className="text-base font-medium">{title}</CardTitle>
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col justify-center pb-6">
        <div
          className="relative"
          style={{ height: `${totalHeight}px` }}
          onMouseLeave={handleChartMouseLeave}
          onMouseEnter={handleChartMouseEnter}
        >
          <div className="h-[280px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart margin={{ top: 10, right: 80, bottom: 10, left: 80 }} style={{ outline: "none" }}>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  outerRadius={90}
                  innerRadius={35}
                  fill="#8884d8"
                  dataKey="value"
                  nameKey="name"
                  label={renderCustomizedLabel}
                  onMouseEnter={onPieEnter}
                  onMouseLeave={onPieLeave}
                  onClick={onPieClick}
                  paddingAngle={2}
                  isAnimationActive={false}
                  startAngle={90}
                  endAngle={-270}
                  cursor="pointer"
                  style={{ outline: "none" }}
                >
                  {chartData.map((entry, index) => {
                    // 计算高亮效果
                    const isActive = clickedIndex === index || (clickedIndex === null && activeIndex === index)

                    // 计算透明度 - 当鼠标不在图表上时，所有扇区都显示完全不透明
                    let opacity = 1
                    if (isHovering || clickedIndex !== null) {
                      opacity = isActive ? 1 : 0.4
                    }

                    return (
                      <Cell
                        key={`cell-${index}`}
                        fill={entry.color}
                        stroke="#fff"
                        strokeWidth={isActive ? 3 : 2}
                        style={{
                          filter: isActive ? "drop-shadow(0px 0px 8px rgba(0,0,0,0.3))" : "none",
                          opacity: opacity,
                          transition: "all 0.3s ease",
                          outline: "none",
                        }}
                      />
                    )
                  })}
                </Pie>
                <Tooltip
                  content={<CustomTooltip />}
                  wrapperStyle={{ zIndex: 100 }}
                  isAnimationActive={false}
                  cursor={false}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="absolute bottom-0 left-0 right-0">
            <CustomLegend payload={chartData} />
          </div>
        </div>
        <style jsx global>{`
          /* 移除所有可能的轮廓和选择框 */
          .recharts-wrapper *,
          .recharts-sector {
            outline: none !important;
            box-shadow: none !important;
          }
          
          /* 移除可能的调试边框 */
          .recharts-wrapper *::after,
          .recharts-wrapper *::before {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
          }
        `}</style>
      </CardContent>
    </Card>
  )
}
