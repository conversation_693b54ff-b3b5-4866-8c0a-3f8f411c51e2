"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Legend, ResponsiveContainer, Toolt<PERSON> } from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Clock } from "lucide-react"

interface CategoryData {
  name: string
  value: number
  percentage: string
  color: string
}

interface CategoryPieChartProps {
  title?: string
  subtitle?: string
  data: CategoryData[]
  height?: number
}

export function CategoryPieChart({
  title = "图书类别分布",
  subtitle = "按类别统计的书籍数量",
  data,
  height = 300,
}: CategoryPieChartProps) {
  const [activeIndex, setActiveIndex] = useState<number | null>(null)
  const [clickedIndex, setClickedIndex] = useState<number | null>(null)
  const [isHovering, setIsHovering] = useState(false)

  // 鼠标离开图表区域时重置状态
  const handleChartMouseLeave = () => {
    setIsHovering(false)
    if (clickedIndex === null) {
      setActiveIndex(null)
    }
  }

  // 鼠标进入图表区域时设置状态
  const handleChartMouseEnter = () => {
    setIsHovering(true)
  }

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index)
    setIsHovering(true)
  }

  const onPieLeave = () => {
    setActiveIndex(null)
    setIsHovering(false)
  }

  const onPieClick = (_: any, index: number) => {
    if (clickedIndex === index) {
      // 如果点击的是当前已选中的扇区，则取消选中
      setClickedIndex(null)
      setActiveIndex(null)
    } else {
      // 否则选中点击的扇区
      setClickedIndex(index)
      setActiveIndex(index)
    }
  }

  // 自定义标签渲染函数
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }: any) => {
    // 如果鼠标悬停在某个扇区上但没有点击选择，只显示悬停的扇区标签
    if (!clickedIndex && isHovering && activeIndex !== null && activeIndex !== index) {
      return null
    }

    // 如果有点击选中的扇区，只显示该扇区的标签
    if (clickedIndex !== null && clickedIndex !== index) {
      return null
    }

    const RADIAN = Math.PI / 180

    // 根据扇区位置调整标签距离
    let radiusMultiplier = 1.5

    // 右侧扇区标签放得更远
    if (midAngle > -90 && midAngle < 90) {
      radiusMultiplier = 1.8
    }

    const radius = innerRadius + (outerRadius - innerRadius) * radiusMultiplier
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    // 如果是选中或悬停的扇区，使用更大的字体和加粗
    const isHighlighted = clickedIndex === index || (clickedIndex === null && activeIndex === index)
    const fontSize = isHighlighted ? 14 : 12
    const fontWeight = isHighlighted ? "700" : "500"

    return (
      <text
        x={x}
        y={y}
        fill={data[index].color || "#000"}
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        fontSize={fontSize}
        fontWeight={fontWeight}
      >
        {`${name} ${(percent * 100).toFixed(1)}%`}
      </text>
    )
  }

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded shadow text-xs">
          <p className="font-medium">{`${payload[0].name}: ${payload[0].value}`}</p>
          <p className="text-gray-500">{`${payload[0].payload.percentage}`}</p>
        </div>
      )
    }
    return null
  }

  const CustomLegend = (props: any) => {
    const { payload } = props

    if (!payload || !Array.isArray(payload) || payload.length === 0) {
      return null
    }

    return (
      <ul className="flex flex-wrap justify-center gap-4">
        {payload.map((entry: any, index: number) => {
          // 如果有点击选中的扇区，只高亮显示该扇区对应的图例
          const isActive = clickedIndex === null || clickedIndex === index

          return (
            <li
              key={`item-${index}`}
              className={`flex items-center cursor-pointer transition-opacity duration-300 ${isActive ? "opacity-100" : "opacity-40"}`}
              onClick={() => onPieClick(null, index)}
              onMouseEnter={() => setActiveIndex(index)}
              onMouseLeave={() => clickedIndex === null && setActiveIndex(null)}
            >
              <span
                className={`inline-block w-3 h-3 mr-2 rounded-full transition-all duration-300 ${
                  clickedIndex === index || (clickedIndex === null && activeIndex === index) ? "w-4 h-4" : "w-3 h-3"
                }`}
                style={{ backgroundColor: entry.color }}
              />
              <span
                className={`text-xs ${
                  clickedIndex === index || (clickedIndex === null && activeIndex === index) ? "font-bold" : ""
                }`}
              >
                {entry.value}
              </span>
            </li>
          )
        })}
      </ul>
    )
  }

  // 确保数据按值从大到小排序
  const chartData = [...data].sort((a, b) => b.value - a.value)

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex items-center">
          <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
          <CardTitle className="text-base font-medium">{title}</CardTitle>
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col justify-center pb-2">
        <div className="h-[280px]" onMouseLeave={handleChartMouseLeave} onMouseEnter={handleChartMouseEnter}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart margin={{ top: 10, right: 80, bottom: 10, left: 80 }} style={{ outline: "none" }}>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={90}
                innerRadius={35}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={renderCustomizedLabel}
                onMouseEnter={onPieEnter}
                onMouseLeave={onPieLeave}
                onClick={onPieClick}
                paddingAngle={2}
                isAnimationActive={false}
                startAngle={90}
                endAngle={-270}
                cursor="pointer"
                style={{ outline: "none" }}
              >
                {chartData.map((entry, index) => {
                  // 计算高亮效果
                  const isActive = clickedIndex === index || (clickedIndex === null && activeIndex === index)

                  // 计算透明度 - 当鼠标不在图表上时，所有扇区都显示完全不透明
                  let opacity = 1
                  if (isHovering || clickedIndex !== null) {
                    opacity = isActive ? 1 : 0.4
                  }

                  return (
                    <Cell
                      key={`cell-${index}`}
                      fill={entry.color}
                      stroke="#fff"
                      strokeWidth={isActive ? 3 : 2}
                      style={{
                        filter: isActive ? "drop-shadow(0px 0px 8px rgba(0,0,0,0.3))" : "none",
                        opacity: opacity,
                        transition: "all 0.3s ease",
                        outline: "none",
                      }}
                    />
                  )
                })}
              </Pie>
              <Tooltip
                content={<CustomTooltip />}
                wrapperStyle={{ zIndex: 100 }}
                isAnimationActive={false}
                cursor={false}
              />
              <Legend content={<CustomLegend />} verticalAlign="bottom" height={10} />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <style jsx global>{`
          /* 移除所有可能的轮廓和选择框 */
          .recharts-wrapper *,
          .recharts-sector {
            outline: none !important;
            box-shadow: none !important;
          }
          
          /* 移除可能的调试边框 */
          .recharts-wrapper *::after,
          .recharts-wrapper *::before {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
          }
        `}</style>
      </CardContent>
    </Card>
  )
}
