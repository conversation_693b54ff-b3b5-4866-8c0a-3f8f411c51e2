"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Network, Search, Users, BookOpen, Maximize2, Bug } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Switch } from "@/components/ui/switch"
import Link from "next/link"

// 定义数据类型
interface User {
  id: string
  name: string
  books: number
}

interface Category {
  id: string
  name: string
  color: string
  books: number
}

interface Relationship {
  userId: string
  categoryId: string
  books: number
}

// 在接口定义中添加新的属性
interface SimpleRelationshipChartProps {
  title?: string
  subtitle?: string
  fullscreen?: boolean
  showControls?: boolean
  year?: number
  month?: number
  defaultUserCount?: number
  defaultCategoryCount?: number
}

// Mock data for preview mode
const mockUsers: User[] = [
  { id: "user1", name: "小菜鸡", books: 45 },
  { id: "user2", name: "Chris.W", books: 19 },
  { id: "user3", name: "娃娃", books: 9 },
  { id: "user4", name: "梦田", books: 8 },
  { id: "user5", name: "杜佳霖", books: 8 },
  { id: "user6", name: "李明", books: 7 },
  { id: "user7", name: "王晓", books: 7 },
  { id: "user8", name: "张伟", books: 6 },
  { id: "user9", name: "刘芳", books: 5 },
  { id: "user10", name: "陈晨", books: 5 },
  { id: "user11", name: "赵云", books: 4 },
  { id: "user12", name: "黄河", books: 4 },
  { id: "user13", name: "周周", books: 3 },
  { id: "user14", name: "吴越", books: 3 },
  { id: "user15", name: "郑和", books: 2 },
]

const mockCategories: Category[] = [
  { id: "cat1", name: "文学类", color: "#FF6384", books: 62 },
  { id: "cat2", name: "哲学类", color: "#36A2EB", books: 23 },
  { id: "cat3", name: "社会科学类", color: "#FFCE56", books: 12 },
  { id: "cat4", name: "历史类", color: "#4BC0C0", books: 4 },
  { id: "cat5", name: "科学技术类", color: "#9966FF", books: 4 },
  { id: "cat6", name: "其他", color: "#C9CBCF", books: 22 },
]

const mockRelationships: Relationship[] = [
  // 用户1的关系
  { userId: "user1", categoryId: "cat1", books: 18 },
  { userId: "user1", categoryId: "cat2", books: 8 },
  { userId: "user1", categoryId: "cat3", books: 5 },
  { userId: "user1", categoryId: "cat4", books: 1 },
  { userId: "user1", categoryId: "cat5", books: 2 },
  { userId: "user1", categoryId: "cat6", books: 11 },

  // 用户2的关系
  { userId: "user2", categoryId: "cat1", books: 10 },
  { userId: "user2", categoryId: "cat2", books: 4 },
  { userId: "user2", categoryId: "cat4", books: 2 },
  { userId: "user2", categoryId: "cat5", books: 1 },
  { userId: "user2", categoryId: "cat6", books: 2 },

  // 用户3的关系
  { userId: "user3", categoryId: "cat1", books: 7 },
  { userId: "user3", categoryId: "cat6", books: 2 },

  // 用户4的关系
  { userId: "user4", categoryId: "cat1", books: 6 },
  { userId: "user4", categoryId: "cat2", books: 5 },

  // 用户5的关系
  { userId: "user5", categoryId: "cat1", books: 5 },
  { userId: "user5", categoryId: "cat3", books: 2 },
  { userId: "user5", categoryId: "cat6", books: 2 },

  // 添加更多用户的关系
  { userId: "user6", categoryId: "cat1", books: 4 },
  { userId: "user6", categoryId: "cat3", books: 3 },

  { userId: "user7", categoryId: "cat1", books: 5 },
  { userId: "user7", categoryId: "cat2", books: 2 },

  { userId: "user8", categoryId: "cat1", books: 3 },
  { userId: "user8", categoryId: "cat2", books: 3 },

  { userId: "user9", categoryId: "cat1", books: 3 },
  { userId: "user9", categoryId: "cat3", books: 2 },

  { userId: "user10", categoryId: "cat1", books: 3 },
  { userId: "user10", categoryId: "cat3", books: 2 },

  { userId: "user11", categoryId: "cat5", books: 2 },
  { userId: "user11", categoryId: "cat6", books: 2 },

  { userId: "user12", categoryId: "cat1", books: 2 },
  { userId: "user12", categoryId: "cat6", books: 2 },

  { userId: "user13", categoryId: "cat4", books: 2 },
  { userId: "user13", categoryId: "cat2", books: 1 },

  { userId: "user14", categoryId: "cat1", books: 2 },
  { userId: "user14", categoryId: "cat3", books: 1 },

  { userId: "user15", categoryId: "cat1", books: 1 },
  { userId: "user15", categoryId: "cat2", books: 1 },
]

// 修改组件参数，添加默认值
export function SimpleRelationshipChart({
  title = "用户与类别关系图",
  subtitle = "用户阅读不同类别书籍的流量分布",
  fullscreen = false,
  showControls = true,
  year,
  month,
  defaultUserCount = 6,
  defaultCategoryCount = 5,
}: SimpleRelationshipChartProps) {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const [chartWidth, setChartWidth] = useState(800)
  const [chartHeight, setChartHeight] = useState(400)
  const [isMounted, setIsMounted] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)

  // 添加新的状态用于搜索和筛选
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [showUserFilterPanel, setShowUserFilterPanel] = useState(false)
  const [showCategoryFilterPanel, setShowCategoryFilterPanel] = useState(false)

  // 添加用户数量和类别数量的状态
  const [userCount, setUserCount] = useState(defaultUserCount)
  const [categoryCount, setCategoryCount] = useState(defaultCategoryCount)

  // State for actual data
  const [users, setUsers] = useState<User[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [relationships, setRelationships] = useState<Relationship[]>([])

  // 添加显示数量控制面板状态
  const [showCountControlPanel, setShowCountControlPanel] = useState(false)

  // 添加调试模式状态
  const [debugMode, setDebugMode] = useState(false)

  // Check if we're in preview mode
  useEffect(() => {
    const checkPreviewMode = () => {
      const isPreview =
        typeof window !== "undefined" &&
        (window.location.hostname.includes("vercel.app") ||
          window.location.hostname.includes("localhost") ||
          window.location.search.includes("preview=true"))

      setIsPreviewMode(isPreview)
    }

    checkPreviewMode()
  }, [])

  // Load data or use mock data
  useEffect(() => {
    // 如果在预览模式，使用模拟数据
    if (isPreviewMode) {
      setUsers(mockUsers)
      setCategories(mockCategories)
      setRelationships(mockRelationships)

      // 初始化选择前N个用户和类别
      const topUsers = mockUsers
        .sort((a, b) => b.books - a.books) // 按书籍数量排序
        .slice(0, userCount)
        .map((user) => user.id)

      // 修改类别排序逻辑，确保"其他"类别排在最后
      const sortedCategories = mockCategories
        .sort((a, b) => {
          // 如果其中一个是"其他"类别，将其排在最后
          if (a.name === "其他") return 1
          if (b.name === "其他") return -1
          // 否则按书籍数量降序排序
          return b.books - a.books
        })
        .slice(0, categoryCount)
        .map((category) => category.id)

      setSelectedUsers(topUsers)
      setSelectedCategories(sortedCategories)
      return
    }

    // 如果提供了年份和月份，则获取特定月份的数据
    if (year && month) {
      // 这里应该是获取特定月份的数据的逻辑
      // 但在主页上，我们应该获取所有历史数据
      // 暂时仍使用模拟数据
      setUsers(mockUsers)
      setCategories(mockCategories)
      setRelationships(mockRelationships)
    } else {
      // 如果没有提供年份和月份，则获取所有历史数据
      // 这是主页的情况
      setUsers(mockUsers)
      setCategories(mockCategories)
      setRelationships(mockRelationships)
    }

    // 初始化选择前N个用户和类别，按书籍数量排序
    const topUsers = mockUsers
      .sort((a, b) => b.books - a.books)
      .slice(0, userCount)
      .map((user) => user.id)

    // 修改类别排序逻辑，确保"其他"类别排在最后
    const sortedCategories = mockCategories
      .sort((a, b) => {
        // 如果其中一个是"其他"类别，将其排在最后
        if (a.name === "其他") return 1
        if (b.name === "其他") return -1
        // 否则按书籍数量降序排序
        return b.books - a.books
      })
      .slice(0, categoryCount)
      .map((category) => category.id)

    setSelectedUsers(topUsers)
    setSelectedCategories(sortedCategories)
  }, [isPreviewMode, year, month, userCount, categoryCount])

  // 更新用户数量
  const handleUserCountChange = (newCount: number) => {
    setUserCount(newCount)
    // 更新选中的用户，按书籍数量排序
    const topUsers = users
      .sort((a, b) => b.books - a.books)
      .slice(0, newCount)
      .map((user) => user.id)
    setSelectedUsers(topUsers)
  }

  // 更新类别数量
  const handleCategoryCountChange = (newCount: number) => {
    setCategoryCount(newCount)
    // 更新选中的类别，按书籍数量排序，"其他"类别排在最后
    const topCategories = categories
      .sort((a, b) => {
        // 如果其中一个是"其他"类别，将其排在最后
        if (a.name === "其他") return 1
        if (b.name === "其他") return -1
        // 否则按书籍数量降序排序
        return b.books - a.books
      })
      .slice(0, newCount)
      .map((category) => category.id)
    setSelectedCategories(topCategories)
  }

  // 应用搜索和筛选
  const filteredUsers = users.filter((user) => {
    // 如果用户未被选中，则过滤掉
    if (!selectedUsers.includes(user.id)) return false

    // 如果有搜索词，则按名称过滤
    if (searchTerm && !user.name.toLowerCase().includes(searchTerm.toLowerCase())) return false

    return true
  })

  // 过滤类别
  const filteredCategories = categories.filter((category) => {
    return selectedCategories.includes(category.id)
  })

  // 过滤关系数据，只保留选中用户和选中类别的关系
  const filteredRelationships = relationships.filter(
    (rel) =>
      filteredUsers.some((user) => user.id === rel.userId) &&
      filteredCategories.some((category) => category.id === rel.categoryId),
  )

  // 计算最大关系值，用于确定线条粗细
  const maxRelationshipValue = Math.max(...filteredRelationships.map((r) => r.books), 1)

  // 处理悬停
  const handleMouseEnter = (id: string) => {
    setHoveredItem(id)
  }

  const handleMouseLeave = () => {
    setHoveredItem(null)
  }

  // 检查关系是否被高亮
  const isRelationshipHighlighted = (relationship: Relationship) => {
    if (!hoveredItem) return false
    return hoveredItem === relationship.userId || hoveredItem === relationship.categoryId
  }

  // 处理用户选择变化
  const handleUserToggle = (userId: string) => {
    setSelectedUsers((prev) => {
      if (prev.includes(userId)) {
        return prev.filter((id) => id !== userId)
      } else {
        return [...prev, userId]
      }
    })
  }

  // 处理类别选择变化
  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories((prev) => {
      if (prev.includes(categoryId)) {
        return prev.filter((id) => id !== categoryId)
      } else {
        return [...prev, categoryId]
      }
    })
  }

  // 全选/取消全选
  const handleSelectAll = (select: boolean) => {
    if (select) {
      setSelectedUsers(users.map((user) => user.id))
    } else {
      setSelectedUsers([])
    }
  }

  // 全选/取消全选类别
  const handleSelectAllCategories = (select: boolean) => {
    if (select) {
      setSelectedCategories(categories.map((category) => category.id))
    } else {
      setSelectedCategories([])
    }
  }

  // 在组件挂载后获取容器尺寸
  useEffect(() => {
    setIsMounted(true)

    const updateDimensions = () => {
      if (containerRef.current) {
        setChartWidth(containerRef.current.clientWidth)

        // 计算动态高度，基于节点数量
        const nodeCount = Math.max(filteredUsers.length, filteredCategories.length)
        // 增加每个节点的垂直空间，从80px增加到100px
        const minHeightPerNode = 100
        // 增加基础高度，从120增加到160，为图例和底部留出更多空间
        const calculatedHeight = 160 + nodeCount * minHeightPerNode
        // 设置最小和最大高度限制，增加最小高度
        const dynamicHeight = Math.max(450, Math.min(calculatedHeight, 1200))

        setChartHeight(dynamicHeight)
      }
    }

    updateDimensions()
    window.addEventListener("resize", updateDimensions)

    return () => {
      window.removeEventListener("resize", updateDimensions)
    }
  }, [filteredUsers.length, filteredCategories.length])

  // 计算用户和类别的垂直间距，增加最小间距为80px
  const userSpacing = Math.max(80, (chartHeight - 160) / Math.max(filteredUsers.length, 1))
  const categorySpacing = Math.max(80, (chartHeight - 160) / Math.max(filteredCategories.length, 1))

  // 计算用户和类别的起始位置，增加顶部间距
  const userStartY = 80 + (chartHeight - 160 - (filteredUsers.length - 1) * userSpacing) / 2
  const categoryStartY = 80 + (chartHeight - 160 - (filteredCategories.length - 1) * categorySpacing) / 2

  // 计算连接线的宽度
  const pathWidth = chartWidth - 280 // 减去左右两侧的空间

  // 渲染内容
  const renderContent = () => (
    <div
      ref={containerRef}
      id="relationship-chart-container"
      className={`${fullscreen ? "h-full" : ""} bg-gray-50 rounded-md p-4 relative overflow-hidden`}
      style={{ height: fullscreen ? "100%" : `${chartHeight}px` }}
    >
      {isPreviewMode && (
        <div className="absolute top-0 left-0 right-0 bg-amber-50 text-amber-700 p-2 text-xs text-center">
          预览模式：显示的是模拟数据，仅供界面预览
        </div>
      )}

      {/* 搜索和筛选控件 */}
      {showControls && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-20 flex gap-2">
          <div className="relative">
            <Input
              placeholder="搜索用户..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-48 h-8 pl-8"
            />
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>

          {/* 用户筛选按钮和面板 */}
          <Popover open={showUserFilterPanel} onOpenChange={setShowUserFilterPanel}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <Users className="h-4 w-4" />
                用户筛选
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-3">
              <div className="space-y-2">
                <div className="font-medium text-sm mb-2">选择用户</div>
                <div className="flex justify-between mb-2">
                  <Button variant="outline" size="sm" onClick={() => handleSelectAll(true)} className="text-xs h-7">
                    全选
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleSelectAll(false)} className="text-xs h-7">
                    取消全选
                  </Button>
                </div>
                <div className="space-y-1 max-h-48 overflow-y-auto">
                  {users.map((user) => (
                    <div key={user.id} className="flex items-center text-xs">
                      <Checkbox
                        id={`user-${user.id}`}
                        checked={selectedUsers.includes(user.id)}
                        onCheckedChange={() => handleUserToggle(user.id)}
                        className="mr-2 h-3 w-3"
                      />
                      <Label htmlFor={`user-${user.id}`} className="text-sm">
                        {user.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* 类别筛选按钮和面板 */}
          <Popover open={showCategoryFilterPanel} onOpenChange={setShowCategoryFilterPanel}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <BookOpen className="h-4 w-4" />
                类别筛选
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-3">
              <div className="space-y-2">
                <div className="font-medium text-sm mb-2">选择类别</div>
                <div className="flex justify-between mb-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSelectAllCategories(true)}
                    className="text-xs h-7"
                  >
                    全选
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSelectAllCategories(false)}
                    className="text-xs h-7"
                  >
                    取消全选
                  </Button>
                </div>
                <div className="space-y-1 max-h-48 overflow-y-auto">
                  {categories.map((category) => (
                    <div key={category.id} className="flex items-center text-xs">
                      <Checkbox
                        id={`category-${category.id}`}
                        checked={selectedCategories.includes(category.id)}
                        onCheckedChange={() => handleCategoryToggle(category.id)}
                      />
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full" style={{ backgroundColor: category.color }}></div>
                        <Label htmlFor={`category-${category.id}`} className="text-sm">
                          {category.name}
                        </Label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* 添加显示数量控制按钮和面板 */}
          <Popover open={showCountControlPanel} onOpenChange={setShowCountControlPanel}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-settings-2"
                >
                  <path d="M20 7h-9" />
                  <path d="M14 17H5" />
                  <circle cx="17" cy="17" r="3" />
                  <circle cx="7" cy="7" r="3" />
                </svg>
                显示数量
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-3">
              <div className="space-y-4">
                <div>
                  <div className="font-medium text-sm mb-2">用户显示数量: {userCount}</div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUserCountChange(Math.max(5, userCount - 5))}
                      className="text-xs h-7"
                    >
                      -5
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUserCountChange(Math.min(users.length, userCount + 5))}
                      className="text-xs h-7"
                    >
                      +5
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUserCountChange(users.length)}
                      className="text-xs h-7"
                    >
                      全部
                    </Button>
                  </div>
                </div>

                <div>
                  <div className="font-medium text-sm mb-2">类别显示数量: {categoryCount}</div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCategoryCountChange(Math.max(5, categoryCount - 5))}
                      className="text-xs h-7"
                    >
                      -5
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCategoryCountChange(Math.min(categories.length, categoryCount + 5))}
                      className="text-xs h-7"
                    >
                      +5
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCategoryCountChange(categories.length)}
                      className="text-xs h-7"
                    >
                      全部
                    </Button>
                  </div>
                </div>

                {/* 添加调试模式开关 */}
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="debug-mode" className="text-sm font-medium">
                      调试模式
                    </Label>
                    <Switch id="debug-mode" checked={debugMode} onCheckedChange={setDebugMode} />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">显示连接线详细信息</p>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      )}

      {/* 用户列表 - 左侧 */}
      <div className="absolute left-4 top-0 bottom-0 flex flex-col justify-center z-10">
        {filteredUsers.map((user, index) => (
          <div
            key={user.id}
            className={`p-2 bg-blue-100 rounded-md transition-all shadow-sm border border-blue-200 ${
              hoveredItem === user.id ? "bg-blue-200 shadow-md" : ""
            }`}
            style={{
              width: "120px",
              position: "absolute",
              top: `${userStartY + index * userSpacing}px`,
              left: 0,
              transform: "translateY(-50%)",
              opacity:
                hoveredItem &&
                hoveredItem !== user.id &&
                !filteredRelationships.some((r) => r.userId === user.id && r.categoryId === hoveredItem)
                  ? 0.5
                  : 1,
            }}
            onMouseEnter={() => handleMouseEnter(user.id)}
            onMouseLeave={handleMouseLeave}
          >
            <div className="font-medium text-sm">{user.name}</div>
            <div className="text-xs text-gray-500">{user.books} 本书</div>
          </div>
        ))}
      </div>

      {/* 类别列表 - 右侧 */}
      <div className="absolute right-4 top-0 bottom-0 flex flex-col justify-center z-10">
        {filteredCategories.map((category, index) => (
          <div
            key={category.id}
            className={`p-2 rounded-md transition-all shadow-sm border border-gray-200`}
            style={{
              backgroundColor: `${category.color}20`, // 20% opacity
              borderLeft: `4px solid ${category.color}`,
              width: "120px",
              position: "absolute",
              top: `${categoryStartY + index * categorySpacing}px`,
              right: 0,
              transform: "translateY(-50%)",
              opacity:
                hoveredItem &&
                hoveredItem !== category.id &&
                !filteredRelationships.some((r) => r.categoryId === category.id && r.userId === hoveredItem)
                  ? 0.5
                  : 1,
            }}
            onMouseEnter={() => handleMouseEnter(category.id)}
            onMouseLeave={handleMouseLeave}
          >
            <div className="font-medium text-sm">{category.name}</div>
            <div className="text-xs text-gray-500">{category.books} 本书</div>
          </div>
        ))}
      </div>

      {/* 连接线 */}
      {isMounted && (
        <svg className="absolute inset-0 w-full h-full" style={{ pointerEvents: "none" }}>
          <defs>
            <linearGradient id="gradient-blue" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.7" />
              <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.1" />
            </linearGradient>
          </defs>
          {filteredRelationships.map((rel, index) => {
            // 计算用户和类别在列表中的位置
            const userIndex = filteredUsers.findIndex((u) => u.id === rel.userId)
            const categoryIndex = filteredCategories.findIndex((c) => c.id === rel.categoryId)

            if (userIndex === -1 || categoryIndex === -1) return null

            // 计算用户和类别的垂直位置
            const userY = userStartY + userIndex * userSpacing
            const categoryY = categoryStartY + categoryIndex * categorySpacing

            // 计算连接线的起点和终点
            const startX = 124 // 用户框的右边缘
            const startY = userY // 用户框的中心点Y坐标
            const endX = chartWidth - 136 // 类别框的左边缘
            const endY = categoryY // 类别框的中心点Y坐标

            // 计算控制点，使曲线更平滑
            const controlX1 = startX + (endX - startX) / 3
            const controlY1 = startY
            const controlX2 = endX - (endX - startX) / 3
            const controlY2 = endY

            // 计算线条粗细，基于关系值，但设置上限
            const strokeWidth = Math.max(1, Math.min(6, (rel.books / maxRelationshipValue) * 6))

            // 获取类别颜色
            const category = categories.find((c) => c.id === rel.categoryId)!

            // 确定是否高亮显示
            const isHighlighted = isRelationshipHighlighted(rel)

            // 设置线条颜色和透明度
            const strokeColor = isHighlighted ? category.color : `${category.color}40`
            const opacity = hoveredItem && !isHighlighted ? 0.2 : 0.7

            // 创建唯一的渐变ID
            const gradientId = `gradient-${rel.userId}-${rel.categoryId}-${index}`

            // 获取用户和类别名称（用于调试模式）
            const userName = users.find((u) => u.id === rel.userId)?.name || rel.userId
            const categoryName = categories.find((c) => c.id === rel.categoryId)?.name || rel.categoryId

            return (
              <g key={`${rel.userId}-${rel.categoryId}-${index}`}>
                {/* 为每个关系创建一个渐变 */}
                <defs>
                  <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.7" />
                    <stop offset="100%" stopColor={category.color} stopOpacity="0.7" />
                  </linearGradient>
                </defs>
                <path
                  d={`M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`}
                  stroke={isHighlighted ? `url(#${gradientId})` : strokeColor}
                  strokeWidth={strokeWidth}
                  fill="none"
                  opacity={opacity}
                />

                {/* 调试模式下显示连接线信息 */}
                {debugMode && (
                  <text
                    x={(startX + endX) / 2}
                    y={(startY + endY) / 2 - 10}
                    textAnchor="middle"
                    fill={category.color}
                    fontSize="10"
                    fontWeight="bold"
                    pointerEvents="none"
                  >
                    {`${userName} → ${categoryName}: ${rel.books}本`}
                  </text>
                )}

                {/* 添加动画效果 - 当关系被高亮时显示，根据悬停的是用户还是类别来决定动画方向 */}
                {isHighlighted && (
                  <circle r="4" fill={category.color}>
                    <animateMotion
                      dur="3s"
                      repeatCount="indefinite"
                      path={
                        hoveredItem === rel.categoryId
                          ? `M ${endX} ${endY} C ${controlX2} ${controlY2}, ${controlX1} ${controlY1}, ${startX} ${startY}`
                          : `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`
                      }
                    />
                  </circle>
                )}

                {/* 添加书籍数量标签 */}
                {(isHighlighted || rel.books > maxRelationshipValue * 0.5) && (
                  <text
                    x={(startX + endX) / 2}
                    y={(startY + endY) / 2 + 10}
                    textAnchor="middle"
                    fill={isHighlighted ? category.color : "#666"}
                    fontSize="10"
                    fontWeight={isHighlighted ? "bold" : "normal"}
                    pointerEvents="none"
                  >
                    {rel.books}本
                  </text>
                )}
              </g>
            )
          })}
        </svg>
      )}

      {/* 图例 */}
      <div className="absolute bottom-2 left-2 bg-white p-2 rounded-md shadow-sm text-xs">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-blue-100 rounded-sm mr-1"></div>
          <span>用户</span>
        </div>
        <div className="flex items-center mt-1">
          <div className="w-3 h-3 bg-gray-200 rounded-sm mr-1"></div>
          <span>类别</span>
        </div>
        <div className="flex items-center mt-1">
          <div className="w-8 h-1 bg-gray-400 rounded-sm mr-1"></div>
          <span>阅读关系</span>
        </div>
      </div>

      {/* 无数据提示 */}
      {(filteredUsers.length === 0 || filteredCategories.length === 0) && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-80 z-20">
          <div className="text-center p-4 bg-white rounded-lg shadow-sm">
            <p className="text-lg font-medium text-gray-700">无匹配数据</p>
            <p className="text-sm text-gray-500 mt-1">请调整搜索条件或筛选选项</p>
          </div>
        </div>
      )}
    </div>
  )

  // 根据是否全屏模式返回不同的组件结构
  return fullscreen ? (
    <div className="h-full">{renderContent()}</div>
  ) : (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <Network className="h-4 w-4 mr-2 text-muted-foreground" />
            <CardTitle className="text-base font-medium">{title}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {/* 添加调试模式按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setDebugMode(!debugMode)}
              className="h-7 px-2"
              title={debugMode ? "关闭调试模式" : "开启调试模式"}
            >
              <Bug className={`h-4 w-4 ${debugMode ? "text-red-500" : "text-gray-400"}`} />
            </Button>

            {/* 添加显示数量控制按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowCountControlPanel(!showCountControlPanel)}
              className="h-7 px-2"
              title="调整显示数量"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-settings-2"
              >
                <path d="M20 7h-9" />
                <path d="M14 17H5" />
                <circle cx="17" cy="17" r="3" />
                <circle cx="7" cy="7" r="3" />
              </svg>
            </Button>

            {!fullscreen && year && month && (
              <Link
                href={`/fullscreen/sankey/${year}/${month}`}
                className="p-1 rounded-md hover:bg-gray-100 transition-colors"
                title="全屏查看"
              >
                <Maximize2 className="h-4 w-4 text-gray-500" />
              </Link>
            )}
          </div>
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>{renderContent()}</CardContent>
    </Card>
  )
}
