"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { ResponsiveContainer, ComposedChart, Bar, Line, XAxis, YAxis, CartesianGrid, Legend } from "recharts"

// 月份名称
const monthNames = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]

interface MonthlyComparisonChartProps {
  currentYearData: {
    month: number
    active_readers: number
    completed_books: number
  }[]
  previousYearData?: {
    month: number
    active_readers: number
    completed_books: number
  }[]
  currentYear: number
  previousYear?: number
}

export function MonthlyComparisonChart({
  currentYearData,
  previousYearData,
  currentYear,
  previousYear,
}: MonthlyComparisonChartProps) {
  // 处理数据，确保所有月份都有数据点
  const chartData = Array.from({ length: 12 }, (_, i) => {
    const monthNumber = i + 1
    const currentMonthData = currentYearData.find((item) => item.month === monthNumber)
    const previousMonthData = previousYearData?.find((item) => item.month === monthNumber)

    return {
      name: monthNames[i],
      month: monthNumber,
      current: currentMonthData?.completed_books || 0,
      previous: previousMonthData?.completed_books || 0,
    }
  })

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle>月度阅读对比</CardTitle>
        <CardDescription>
          {previousYear ? `${currentYear}年与${previousYear}年完成书籍数量对比` : `${currentYear}年每月完成书籍数量`}
        </CardDescription>
      </CardHeader>
      <CardContent className="h-[400px]">
        <ChartContainer
          config={{
            current: {
              label: `${currentYear}年`,
              color: "hsl(var(--chart-1))",
            },
            previous: previousYear
              ? {
                  label: `${previousYear}年`,
                  color: "hsl(var(--chart-2))",
                }
              : undefined,
          }}
        >
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Legend />
              <Bar dataKey="current" fill="var(--color-current)" name={`${currentYear}年`} radius={[4, 4, 0, 0]} />
              {previousYear && (
                <Line
                  type="monotone"
                  dataKey="previous"
                  stroke="var(--color-previous)"
                  name={`${previousYear}年`}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                />
              )}
            </ComposedChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
