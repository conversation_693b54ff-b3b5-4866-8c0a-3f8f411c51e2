"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts"

interface PopularBooksChartProps {
  data: {
    title: string
    count: number
  }[]
  title?: string
  description?: string
}

export function PopularBooksChart({
  data,
  title = "热门书籍排行",
  description = "阅读人数最多的书籍",
}: PopularBooksChartProps) {
  // 确保数据有值，并限制显示数量
  const chartData =
    data.length > 0
      ? data.slice(0, 10).map((item) => ({
          name: item.title.length > 10 ? item.title.substring(0, 10) + "..." : item.title,
          fullTitle: item.title,
          value: item.count,
        }))
      : [{ name: "暂无数据", fullTitle: "暂无数据", value: 0 }]

  // 自定义提示框
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length > 0) {
      return (
        <div className="bg-white p-2 border rounded shadow text-sm">
          <p className="font-medium">{payload[0].payload.fullTitle}</p>
          <p>阅读人数: {payload[0].value}</p>
        </div>
      )
    }
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} layout="vertical" margin={{ top: 5, right: 30, left: 80, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis type="category" dataKey="name" width={80} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar dataKey="value" name="阅读人数" fill="hsl(var(--chart-2))" radius={[0, 4, 4, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
