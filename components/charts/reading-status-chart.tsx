"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, ResponsiveContainer, <PERSON>, Tooltip } from "recharts"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { BookOpen } from "lucide-react"

// 定义更丰富的阅读状态颜色
const statusColors = {
  读完: "#4CAF50", // 绿色
  正在阅读: "#2196F3", // 蓝色
  计划阅读: "#FFC107", // 黄色
  已放弃: "#F44336", // 红色
  暂停: "#9C27B0", // 紫色
  其他: "#607D8B", // 灰蓝色
}

// 预定义一组丰富多彩的颜色作为备选
const colorPalette = [
  "#4CAF50", // 绿色
  "#2196F3", // 蓝色
  "#FFC107", // 黄色
  "#F44336", // 红色
  "#9C27B0", // 紫色
  "#607D8B", // 灰蓝色
  "#FF9800", // 橙色
  "#795548", // 棕色
  "#009688", // 青色
  "#673AB7", // 深紫色
]

// 获取阅读状态的颜色
function getStatusColor(status: string): string {
  if (statusColors[status]) {
    return statusColors[status]
  }

  // 如果没有预定义颜色，根据状态名称生成哈希值选择一个颜色
  let hash = 0
  for (let i = 0; i < status.length; i++) {
    hash = status.charCodeAt(i) + ((hash << 5) - hash)
  }

  const index = Math.abs(hash) % colorPalette.length
  return colorPalette[index]
}

interface ReadingStatusData {
  name: string
  value: number
  percentage?: string
}

interface ReadingStatusChartProps {
  data: ReadingStatusData[]
  title?: string
  subtitle?: string
}

export function ReadingStatusChart({
  data,
  title = "阅读状态分布",
  subtitle = "各阅读状态的书籍数量",
}: ReadingStatusChartProps) {
  // 计算总数
  const total = data.reduce((sum, item) => sum + item.value, 0)

  // 处理数据，添加百分比和颜色
  const chartData = data.map((item) => ({
    ...item,
    percentage: `${((item.value / total) * 100).toFixed(1)}%`,
    color: getStatusColor(item.name),
  }))

  // 自定义图例
  const CustomLegend = (props: any) => {
    const { payload } = props

    if (!payload || payload.length === 0) return null

    return (
      <ul className="flex flex-wrap justify-center gap-4 mt-2">
        {payload.map((entry: any, index: number) => (
          <li key={`item-${index}`} className="flex items-center">
            <span className="inline-block w-3 h-3 mr-2 rounded-full" style={{ backgroundColor: entry.color }} />
            <span className="text-xs">
              {entry.value}: {chartData.find((item) => item.name === entry.value)?.percentage}
            </span>
          </li>
        ))}
      </ul>
    )
  }

  // 自定义提示框
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded shadow text-xs">
          <p className="font-medium">{`${payload[0].name}: ${payload[0].value}`}</p>
          <p className="text-gray-500">{`${payload[0].payload.percentage}`}</p>
        </div>
      )
    }
    return null
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center">
          <BookOpen className="h-4 w-4 mr-2 text-muted-foreground" />
          <CardTitle className="text-base font-medium">{title}</CardTitle>
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>
        <div className="h-[200px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                labelLine={false}
              >
                {chartData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={entry.color}
                    stroke="#fff"
                    strokeWidth={2}
                    style={{
                      filter: "drop-shadow(0px 0px 2px rgba(0,0,0,0.1))",
                      transition: "all 0.3s ease",
                    }}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend content={<CustomLegend />} verticalAlign="bottom" height={36} />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
