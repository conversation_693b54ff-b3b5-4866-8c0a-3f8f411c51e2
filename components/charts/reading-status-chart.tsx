"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Cell } from "recharts"

interface ReadingStatusChartProps {
  data: {
    name: string
    value: number
  }[]
  title?: string
  description?: string
}

// 状态颜色映射
const STATUS_COLORS = {
  读完: "hsl(var(--chart-1))",
  在读: "hsl(var(--chart-2))",
  计划读: "hsl(var(--chart-3))",
  未知: "hsl(var(--chart-4))",
}

export function ReadingStatusChart({
  data,
  title = "阅读状态分布",
  description = "不同阅读状态的书籍数量",
}: ReadingStatusChartProps) {
  // 确保数据有值
  const chartData = data.length > 0 ? data : [{ name: "暂无数据", value: 0 }]

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} layout="vertical" margin={{ top: 5, right: 30, left: 60, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis type="category" dataKey="name" />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" name="书籍数量" fill="hsl(var(--chart-1))" radius={[0, 4, 4, 0]}>
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={STATUS_COLORS[entry.name] || "hsl(var(--chart-1))"} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
