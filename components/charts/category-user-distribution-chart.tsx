"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Book } from "lucide-react"

interface User {
  id: string
  name: string
  count: number
}

interface CategoryData {
  name: string
  color: string
  users: User[]
  totalBooks: number
}

interface CategoryUserDistributionChartProps {
  data: CategoryData[]
  title?: string
  subtitle?: string
}

export function CategoryUserDistributionChart({
  data,
  title = "图书类别用户分布",
  subtitle = "各类别中的阅读用户分布",
}: CategoryUserDistributionChartProps) {
  const [activeCategory, setActiveCategory] = useState<string>(data[0]?.name || "")

  // 获取当前选中类别的数据
  const activeCategoryData = data.find((category) => category.name === activeCategory)

  // 计算用户阅读量的最大值，用于确定比例条的宽度
  const maxCount = activeCategoryData?.users.reduce((max, user) => Math.max(max, user.count), 0) || 1

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center">
          <Book className="h-4 w-4 mr-2 text-muted-foreground" />
          <CardTitle className="text-base font-medium">{title}</CardTitle>
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>
        <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
          <TabsList className="mb-4 mb-8 mt-2 flex flex-wrap">
            {data.map((category) => (
              <TabsTrigger key={category.name} value={category.name} className="flex items-center gap-1">
                <span className="inline-block w-3 h-3 rounded-full" style={{ backgroundColor: category.color }}></span>
                <span>{category.name}</span>
                <span className="ml-1 text-xs text-muted-foreground">{category.totalBooks}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {data.map((category) => (
            <TabsContent key={category.name} value={category.name} className="mt-0">
              <div className="flex items-center mb-4">
                <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: category.color }}></div>
                <h3 className="text-sm font-medium">{category.name} 类别阅读用户</h3>
                <span className="ml-auto text-sm text-muted-foreground">
                  {category.users.length} 位用户 · 共 {category.totalBooks} 本书
                </span>
              </div>

              <ScrollArea className="h-[400px]">
                {" "}
                {/* 增加高度以显示更多内容 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {category.users.map((user) => (
                    <div
                      key={user.id}
                      className="flex flex-col p-3 border rounded-md hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex justify-between items-center mb-2">
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">阅读 {user.count} 本</div>
                      </div>
                      <div className="w-full bg-gray-100 rounded-full h-2">
                        <div
                          className="h-2 rounded-full"
                          style={{
                            width: `${(user.count / maxCount) * 100}%`,
                            backgroundColor: category.color,
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  )
}
