"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"

// 丰富的类别颜色映射
const categoryColors = {
  文学: "#8884d8",
  小说: "#4CAF50",
  历史: "#FF9800",
  科学: "#2196F3",
  哲学: "#9C27B0",
  艺术: "#E91E63",
  经济: "#F44336",
  心理: "#00BCD4",
  社会: "#795548",
  政治: "#607D8B",
  教育: "#3F51B5",
  科技: "#009688",
  传记: "#FFC107",
  管理: "#673AB7",
  健康: "#CDDC39",
  旅行: "#03A9F4",
  宗教: "#FF5722",
  军事: "#9E9E9E",
  法律: "#FFEB3B",
  环境: "#8BC34A",
}

// 根据类别名称获取颜色
function getCategoryColor(categoryName: string): string {
  // 如果类别名称在预定义映射中，直接返回
  if (categoryName in categoryColors) {
    return categoryColors[categoryName as keyof typeof categoryColors]
  }

  // 否则，根据类别名称生成一个哈希值，并选择一个预定义的颜色
  const colorList = Object.values(categoryColors)
  let hash = 0
  for (let i = 0; i < categoryName.length; i++) {
    hash = categoryName.charCodeAt(i) + ((hash << 5) - hash)
  }
  const index = Math.abs(hash) % colorList.length
  return colorList[index]
}

// 获取头像背景色 - 从ReaderLeaderboard组件复制过来
function getAvatarBgColor(avatar: string, name: string) {
  // 扩展的颜色映射表，包含更多字符和更丰富的颜色
  const colors = {
    // 中文字符
    小: "bg-red-100 text-red-500",
    C: "bg-purple-100 text-purple-500",
    娃: "bg-pink-100 text-pink-500",
    梦: "bg-blue-100 text-blue-500",
    杜: "bg-rose-100 text-rose-500",
    李: "bg-green-100 text-green-500",
    王: "bg-yellow-100 text-yellow-500",
    张: "bg-indigo-100 text-indigo-500",
    刘: "bg-cyan-100 text-cyan-500",
    陈: "bg-amber-100 text-amber-500",
    赵: "bg-lime-100 text-lime-500",
    黄: "bg-orange-100 text-orange-500",
    周: "bg-teal-100 text-teal-500",
    吴: "bg-violet-100 text-violet-500",
    郑: "bg-fuchsia-100 text-fuchsia-500",

    // 英文字符
    A: "bg-red-100 text-red-500",
    B: "bg-orange-100 text-orange-500",
    C: "bg-amber-100 text-amber-500",
    D: "bg-yellow-100 text-yellow-500",
    E: "bg-lime-100 text-lime-500",
    F: "bg-green-100 text-green-500",
    G: "bg-emerald-100 text-emerald-500",
    H: "bg-teal-100 text-teal-500",
    I: "bg-cyan-100 text-cyan-500",
    J: "bg-sky-100 text-sky-500",
    K: "bg-blue-100 text-blue-500",
    L: "bg-indigo-100 text-indigo-500",
    M: "bg-violet-100 text-violet-500",
    N: "bg-purple-100 text-purple-500",
    O: "bg-fuchsia-100 text-fuchsia-500",
    P: "bg-pink-100 text-pink-500",
    Q: "bg-rose-100 text-rose-500",
    R: "bg-red-100 text-red-600",
    S: "bg-orange-100 text-orange-600",
    T: "bg-amber-100 text-amber-600",
    U: "bg-yellow-100 text-yellow-600",
    V: "bg-lime-100 text-lime-600",
    W: "bg-green-100 text-green-600",
    X: "bg-emerald-100 text-emerald-600",
    Y: "bg-teal-100 text-teal-600",
    Z: "bg-cyan-100 text-cyan-600",

    // 数字
    "0": "bg-blue-100 text-blue-500",
    "1": "bg-indigo-100 text-indigo-500",
    "2": "bg-violet-100 text-violet-500",
    "3": "bg-purple-100 text-purple-500",
    "4": "bg-fuchsia-100 text-fuchsia-500",
    "5": "bg-pink-100 text-pink-500",
    "6": "bg-rose-100 text-rose-500",
    "7": "bg-red-100 text-red-500",
    "8": "bg-orange-100 text-orange-500",
    "9": "bg-amber-100 text-amber-500",
  }

  // 首先检查第一个字符是否在映射表中
  const firstChar = avatar.charAt(0).toUpperCase()
  if (colors[firstChar]) {
    return colors[firstChar]
  }

  // 如果不在映射表中，根据名称生成一个颜色
  // 使用名称的简单哈希来选择颜色
  const colorOptions = [
    "bg-red-100 text-red-500",
    "bg-orange-100 text-orange-500",
    "bg-amber-100 text-amber-500",
    "bg-yellow-100 text-yellow-500",
    "bg-lime-100 text-lime-500",
    "bg-green-100 text-green-500",
    "bg-emerald-100 text-emerald-500",
    "bg-teal-100 text-teal-500",
    "bg-cyan-100 text-cyan-500",
    "bg-sky-100 text-sky-500",
    "bg-blue-100 text-blue-500",
    "bg-indigo-100 text-indigo-500",
    "bg-violet-100 text-violet-500",
    "bg-purple-100 text-purple-500",
    "bg-fuchsia-100 text-fuchsia-500",
    "bg-pink-100 text-pink-500",
    "bg-rose-100 text-rose-500",
  ]

  // 简单的字符串哈希函数
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = (hash << 5) - hash + name.charCodeAt(i)
    hash = hash & hash // 转换为32位整数
  }

  // 使用哈希值选择颜色
  const colorIndex = Math.abs(hash) % colorOptions.length
  return colorOptions[colorIndex]
}

interface User {
  id: string
  name: string
  count: number
}

interface CategoryData {
  name: string
  color: string
  users: User[]
  totalBooks: number
}

interface CategoryUserDistributionChartProps {
  data: CategoryData[]
  title?: string
  subtitle?: string
}

export function CategoryUserDistributionChart({
  data,
  title = "图书类别用户分布",
  subtitle = "各类别中的阅读用户分布",
}: CategoryUserDistributionChartProps) {
  // 对数据按照书籍数量从多到少排序
  const sortedData = [...data].sort((a, b) => b.totalBooks - a.totalBooks)

  // 默认选择书籍数量最多的类别
  const [activeCategory, setActiveCategory] = useState(sortedData.length > 0 ? sortedData[0].name : "")

  // 获取当前选中类别的数据
  const activeCategoryData = data.find((category) => category.name === activeCategory)

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>
        <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
          <TabsList className="w-full flex flex-wrap h-auto py-1 mb-4">
            {sortedData.map((category) => (
              <TabsTrigger key={category.name} value={category.name} className="flex items-center gap-1 mb-1">
                <span
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: getCategoryColor(category.name) }}
                ></span>
                <span>{category.name}</span>
                <span className="text-xs text-muted-foreground ml-1">({category.totalBooks})</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {activeCategoryData && (
            <div className="space-y-1">
              <div className="grid grid-cols-12 text-xs font-medium text-muted-foreground mb-2">
                <div className="col-span-6">用户</div>
                <div className="col-span-3 text-right">阅读数量</div>
                <div className="col-span-3 text-right">占比</div>
              </div>

              {activeCategoryData.users.slice(0, 10).map((user) => (
                <div key={user.id} className="grid grid-cols-12 py-2 items-center border-t text-sm">
                  <div className="col-span-6 flex items-center">
                    <span
                      className={`inline-flex items-center justify-center w-6 h-6 rounded-full mr-2 ${getAvatarBgColor(
                        user.name.charAt(0),
                        user.name,
                      )}`}
                    >
                      {user.name.charAt(0)}
                    </span>
                    <span>{user.name}</span>
                  </div>
                  <div className="col-span-3 text-right">{user.count}</div>
                  <div className="col-span-3 text-right">
                    {((user.count / activeCategoryData.totalBooks) * 100).toFixed(1)}%
                  </div>
                </div>
              ))}
            </div>
          )}
        </Tabs>
      </CardContent>
    </Card>
  )
}
