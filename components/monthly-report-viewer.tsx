"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { createClient } from "@supabase/supabase-js"
import { FileText, Upload } from "lucide-react"
import ReactMarkdown from "react-markdown"
import Link from "next/link"

// 月份名称
const monthNames = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]

interface MonthlyReportViewerProps {
  year: number
  month: number
}

export function MonthlyReportViewer({ year, month }: MonthlyReportViewerProps) {
  const [report, setReport] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)

  useEffect(() => {
    // 检测是否在预览模式
    const checkPreviewMode = () => {
      const isPreview =
        window.location.hostname.includes("vercel.app") ||
        window.location.hostname.includes("vusercontent.net") ||
        window.location.search.includes("preview=true")

      setIsPreviewMode(isPreview)
    }

    checkPreviewMode()
  }, [])

  useEffect(() => {
    const fetchReport = async () => {
      setIsLoading(true)
      setError(null)

      // 如果是预览模式，使用模拟数据
      if (isPreviewMode) {
        setTimeout(() => {
          setReport(null)
          setError(`${year}年${monthNames[month - 1]}的报告尚未上传`)
          setIsLoading(false)
        }, 800)
        return
      }

      try {
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

        if (!supabaseUrl || !supabaseKey) {
          throw new Error("Supabase环境变量未设置")
        }

        const supabase = createClient(supabaseUrl, supabaseKey)

        // 获取月度报告
        const { data, error } = await supabase
          .from("monthly_reports")
          .select("*")
          .eq("year", year)
          .eq("month", month)
          .single()

        if (error) {
          if (error.code === "PGRST116") {
            // 没有找到记录
            setError(`${year}年${monthNames[month - 1]}的报告尚未上传`)
          } else if (error.message.includes("does not exist")) {
            // 表不存在
            setError(`月度报告功能尚未设置，请先创建数据表`)
            console.log("monthly_reports table does not exist yet")
          } else {
            throw error
          }
        } else {
          setReport(data)
        }
      } catch (err) {
        console.error("Error fetching report:", err)
        setError(err.message || "获取报告时发生错误")
      } finally {
        setIsLoading(false)
      }
    }

    fetchReport()
  }, [year, month, isPreviewMode])

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-12 w-3/4" />
        <Skeleton className="h-64 w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-medium text-red-500 mb-2">{error}</h3>
            <p className="text-muted-foreground mb-6">您可以通过管理员面板上传本月的详细报告</p>
            <Button asChild variant="outline">
              <Link href="/admin/upload-report">
                <Upload className="h-4 w-4 mr-2" />
                上传月度报告
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="prose prose-sm md:prose-base lg:prose-lg max-w-none">
          <ReactMarkdown>{report?.content || ""}</ReactMarkdown>
        </div>
      </CardContent>
    </Card>
  )
}
