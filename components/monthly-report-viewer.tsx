"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { But<PERSON> } from "@/components/ui/button"
import { createClient } from "@/lib/supabase/client"
import { FileText, Upload, Printer, Download, Copy, Check } from "lucide-react"
import ReactMarkdown from "react-markdown"
import Link from "next/link"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"

// 月份名称
const monthNames = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]

interface MonthlyReportViewerProps {
  year: number
  month: number
}

export function MonthlyReportViewer({ year, month }: MonthlyReportViewerProps) {
  const [report, setReport] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [copied, setCopied] = useState(false)

  useEffect(() => {
    // 检测是否在预览模式
    const checkPreviewMode = () => {
      const isPreview =
        window.location.hostname.includes("vercel.app") ||
        window.location.hostname.includes("vusercontent.net") ||
        window.location.search.includes("preview=true")

      setIsPreviewMode(isPreview)
    }

    checkPreviewMode()
  }, [])

  useEffect(() => {
    const fetchReport = async () => {
      setIsLoading(true)
      setError(null)

      // 如果是预览模式，使用模拟数据
      if (isPreviewMode) {
        setTimeout(() => {
          setReport({
            id: 1,
            year: year,
            month: month,
            title: `${year}年${monthNames[month - 1]}读书群月度报告`,
            content: getSampleMarkdown(year, month),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          setIsLoading(false)
        }, 800)
        return
      }

      try {
        // 使用客户端 Supabase 实例
        const supabase = createClient()
        console.log(`正在获取 ${year}年${month}月 的报告...`)

        // 获取月度报告
        const { data, error } = await supabase
          .from("monthly_reports")
          .select("*")
          .eq("year", year)
          .eq("month", month)
          .single()

        console.log("查询结果:", { data, error })

        if (error) {
          if (error.code === "PGRST116") {
            // 没有找到记录
            setError(`${year}年${monthNames[month - 1]}的报告尚未上传`)
          } else if (error.message.includes("does not exist")) {
            // 表不存在
            setError(`月度报告功能尚未设置，请先创建数据表`)
            console.log("monthly_reports table does not exist yet")
          } else {
            throw error
          }
        } else {
          setReport(data)
        }
      } catch (err) {
        console.error("Error fetching report:", err)
        setError(err.message || "获取报告时发生错误")
      } finally {
        setIsLoading(false)
      }
    }

    fetchReport()
  }, [year, month, isPreviewMode])

  const handlePrint = () => {
    window.print()
  }

  const handleDownload = () => {
    if (!report) return

    const fileName = `${year}年${month}月读书群月度报告.md`
    const blob = new Blob([report.content], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)

    const a = document.createElement("a")
    a.href = url
    a.download = fileName
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleCopy = () => {
    if (!report) return

    navigator.clipboard.writeText(report.content).then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    })
  }

  if (isLoading) {
    return (
      <Card className="border shadow-sm">
        <CardContent className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
            <div className="pt-4">
              <Skeleton className="h-64 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="border shadow-sm">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-medium text-red-500 mb-2">{error}</h3>
            <p className="text-muted-foreground mb-6">您可以通过管理员面板上传本月的详细报告</p>
            <Button asChild variant="outline">
              <Link href="/admin/routes/monthly-reports">
                <Upload className="h-4 w-4 mr-2" />
                上传月度报告
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border shadow-sm">
      <CardContent className="p-0">
        <Tabs defaultValue="preview" className="w-full">
          <div className="flex items-center justify-between border-b px-4 py-2">
            <h3 className="text-lg font-medium">{report.title || `${year}年${monthNames[month - 1]}读书群月度报告`}</h3>
            <div className="flex space-x-2">
              <TabsList>
                <TabsTrigger value="preview">预览</TabsTrigger>
                <TabsTrigger value="source">源码</TabsTrigger>
              </TabsList>
              <Button variant="outline" size="sm" onClick={handlePrint} title="打印报告">
                <Printer className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload} title="下载Markdown文件">
                <Download className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleCopy} title="复制Markdown内容">
                {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <TabsContent value="preview" className="mt-0">
            <ScrollArea className="h-[calc(100vh-300px)] min-h-[500px]">
              <div className="p-6 md:p-8">
                <article className="prose prose-sm md:prose-base lg:prose-lg dark:prose-invert max-w-none">
                  <ReactMarkdown>{report?.content || ""}</ReactMarkdown>
                </article>
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="source" className="mt-0">
            <ScrollArea className="h-[calc(100vh-300px)] min-h-[500px]">
              <div className="p-4 bg-muted/30">
                <pre className="whitespace-pre-wrap text-sm font-mono p-4 bg-muted rounded-md overflow-auto">
                  {report?.content || ""}
                </pre>
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

// 生成示例Markdown内容（仅在预览模式下使用）
function getSampleMarkdown(year: number, month: number) {
  return `# ${year}年${monthNames[month - 1]}读书群月度报告

## 概述

本月，我们的读书群共有 **42** 位成员参与阅读活动，总共阅读了 **78** 本书籍，其中 **53** 本已读完。与上月相比，参与人数增加了 **15%**，阅读总量增加了 **23%**。

## 阅读类别分析

本月阅读的书籍主要集中在以下几个类别：

| 类别 | 书籍数量 | 占比 |
|------|----------|------|
| 文学 | 24 | 30.8% |
| 历史 | 15 | 19.2% |
| 科学技术 | 12 | 15.4% |
| 哲学 | 10 | 12.8% |
| 社会科学 | 9 | 11.5% |
| 其他 | 8 | 10.3% |

## 阅读之星

恭喜以下成员成为本月的阅读之星：

1. **张三** - 阅读 8 本，完成率 100%
2. **李四** - 阅读 7 本，完成率 85.7%
3. **王五** - 阅读 6 本，完成率 83.3%

## 推荐书籍

本月群内最受欢迎的三本书是：

### 《思考，快与慢》

![思考，快与慢](/placeholder.svg?height=150&width=100&query=思考，快与慢)

作者：丹尼尔·卡尼曼

推荐理由：本书揭示了人类思考的两种模式：系统1是快速、直觉和情感的；系统2是缓慢、深思熟虑和理性的。通过理解这两种思考模式，我们可以更好地认识自己的决策过程，避免常见的认知偏差。

### 《人类简史》

![人类简史](/placeholder.svg?height=150&width=100&query=人类简史)

作者：尤瓦尔·赫拉利

推荐理由：这是一部宏大的人类发展史，从认知革命、农业革命到科学革命，赫拉利用生动的语言和独特的视角，重新审视了人类历史的进程，引发我们对人类过去、现在和未来的深刻思考。

### 《百年孤独》

![百年孤独](/placeholder.svg?height=150&width=100&query=百年孤独)

作者：加西亚·马尔克斯

推荐理由：这部魔幻现实主义的经典之作，讲述了布恩迪亚家族七代人的故事。马尔克斯创造了一个神奇的马孔多世界，通过家族的兴衰展现了拉丁美洲的历史和文化，是世界文学的瑰宝。

## 下月阅读计划

我们计划在下个月开展以下活动：

1. **主题阅读月** - 以"科技与人文的交融"为主题，推荐相关书籍
2. **线上读书会** - 每周四晚8点，讨论当周阅读内容
3. **作者专访** - 邀请本地作家分享创作经验

## 总结

本月的阅读活动丰富多彩，成员们的参与热情高涨。我们看到了更多元化的阅读选择，以及更深入的讨论。希望在下个月，我们能继续保持这种良好的阅读氛围，共同成长。

---

*报告生成日期：${new Date().toLocaleDateString()}*
`
}
