"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Home, BarChart2, BookOpen, Calendar, Settings } from "lucide-react"

export function MobileBottomNav() {
  const pathname = usePathname()

  const routes = [
    {
      href: "/",
      label: "主页",
      icon: <Home className="h-5 w-5" />,
      active: pathname === "/",
    },
    {
      href: "/data-analysis",
      label: "数据",
      icon: <BarChart2 className="h-5 w-5" />,
      active: pathname === "/data-analysis",
    },
    {
      href: "/reading-query",
      label: "查询",
      icon: <BookOpen className="h-5 w-5" />,
      active: pathname === "/reading-query",
    },
    {
      href: "/annual-report",
      label: "报告",
      icon: <Calendar className="h-5 w-5" />,
      active: pathname === "/annual-report",
    },
    {
      href: "/admin",
      label: "管理",
      icon: <Settings className="h-5 w-5" />,
      active: pathname.startsWith("/admin"),
    },
  ]

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 z-50 bg-white border-t shadow-sm">
      <div className="flex items-center justify-around">
        {routes.map((route) => (
          <Link
            key={route.href}
            href={route.href}
            className={cn(
              "flex flex-col items-center py-2 px-3 min-w-[60px]",
              route.active ? "text-primary" : "text-muted-foreground hover:text-primary",
            )}
          >
            {route.icon}
            <span className="text-xs mt-1">{route.label}</span>
          </Link>
        ))}
      </div>
      {/* 添加安全区域填充，适配有底部操作条的设备 */}
      <div className="h-safe-area-bottom bg-white" />
    </div>
  )
}
