import Link from "next/link"
import { Home, BarChart2, Settings } from "lucide-react"
import { MobileNavigation } from "./mobile-navigation"

export function Header() {
  return (
    <header className="border-b">
      <div className="container mx-auto py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MobileNavigation />
            <Link href="/" className="text-xl font-bold">
              书读经典读书群
            </Link>
          </div>
          <nav className="hidden md:flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-1 text-sm hover:text-blue-600 transition-colors">
              <Home className="h-4 w-4" />
              <span>主页</span>
            </Link>
            <Link
              href="/data-analysis"
              className="flex items-center space-x-1 text-sm hover:text-blue-600 transition-colors"
            >
              <BarChart2 className="h-4 w-4" />
              <span>数据概览</span>
            </Link>
            <Link href="/admin" className="flex items-center space-x-1 text-sm hover:text-blue-600 transition-colors">
              <Settings className="h-4 w-4" />
              <span>管理面板</span>
            </Link>
          </nav>
        </div>
      </div>
    </header>
  )
}
