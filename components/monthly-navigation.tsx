import Link from "next/link"
import { createClient } from "@/lib/supabase"
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Calendar } from "lucide-react"

export async function MonthlyNavigation({ year }: { year: number }) {
  const supabase = createClient()

  // 获取有数据的月份
  const { data: monthlyStats } = await supabase
    .from("monthly_stats")
    .select("month, active_readers, completed_books")
    .eq("year", year)
    .order("month")

  // 月份名称
  const monthNames = [
    "一月",
    "二月",
    "三月",
    "四月",
    "五月",
    "六月",
    "七月",
    "八月",
    "九月",
    "十月",
    "十一月",
    "十二月",
  ]

  // 创建所有月份的数组，标记哪些月份有数据
  const months = Array.from({ length: 12 }, (_, i) => {
    const monthNumber = i + 1
    const monthData = monthlyStats?.find((stat) => stat.month === monthNumber)

    return {
      number: monthNumber,
      name: monthNames[i],
      hasData: !!monthData,
      activeReaders: monthData?.active_readers || 0,
      completedBooks: monthData?.completed_books || 0,
    }
  })

  return (
    <div>
      <h2 className="text-2xl font-bold mb-4">{year}年月度数据</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {months.map((month) => (
          <Card key={month.number} className={month.hasData ? "" : "opacity-60"}>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                {month.name}
              </CardTitle>
              <CardDescription>
                {month.hasData ? `${month.activeReaders} 位活跃读者，${month.completedBooks} 本已完成` : "暂无数据"}
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <Button
                asChild
                variant={month.hasData ? "default" : "outline"}
                disabled={!month.hasData}
                className="w-full"
              >
                <Link href={`/${year}/${month.number}?returnYear=${year}`}>查看详情</Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
