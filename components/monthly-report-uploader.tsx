"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { createClient } from "@/lib/supabase/client"
import { useToast } from "@/components/ui/use-toast"

export function MonthlyReportUploader() {
  const [year, setYear] = useState<number>(new Date().getFullYear())
  const [month, setMonth] = useState<number>(() => {
    const currentMonth = new Date().getMonth() + 1 // 获取当前月份（1-12）
    return currentMonth === 1 ? 12 : currentMonth - 1 // 如果是1月，则返回12月，否则返回上个月
  })
  const [title, setTitle] = useState<string>("")
  const [content, setContent] = useState<string>("")
  const [file, setFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isPreview, setIsPreview] = useState<boolean>(false)
  const { toast } = useToast()

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (!selectedFile) return

    setFile(selectedFile)

    // 读取文件内容
    const reader = new FileReader()
    reader.onload = (event) => {
      if (event.target?.result) {
        setContent(event.target.result as string)
      }
    }
    reader.readAsText(selectedFile)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const supabase = createClient()

      // 检查表是否存在
      const { error: checkError } = await supabase.from("monthly_reports").select("id").limit(1)

      if (checkError && checkError.message.includes("does not exist")) {
        toast({
          title: "错误",
          description: "monthly_reports表不存在，请先创建表",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      // 检查是否已存在该月份的报告
      const { data: existingReport } = await supabase
        .from("monthly_reports")
        .select("id")
        .eq("year", year)
        .eq("month", month)
        .single()

      let operation
      if (existingReport) {
        // 更新现有报告
        operation = supabase
          .from("monthly_reports")
          .update({
            title: title || `${year}年${month}月读书报告`,
            content,
            updated_at: new Date().toISOString(),
          })
          .eq("id", existingReport.id)
      } else {
        // 插入新报告
        operation = supabase.from("monthly_reports").insert({
          year,
          month,
          title: title || `${year}年${month}月读书报告`,
          content,
        })
      }

      const { error } = await operation

      if (error) {
        console.error("Error saving report:", error)
        toast({
          title: "保存失败",
          description: error.message,
          variant: "destructive",
        })
      } else {
        toast({
          title: "保存成功",
          description: `${year}年${month}月的读书报告已${existingReport ? "更新" : "保存"}`,
        })
        // 重置表单
        if (!existingReport) {
          setContent("")
          setFile(null)
        }
      }
    } catch (error) {
      console.error("Error:", error)
      toast({
        title: "发生错误",
        description: "保存报告时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const loadExistingReport = async () => {
    setIsLoading(true)
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from("monthly_reports")
        .select("title, content")
        .eq("year", year)
        .eq("month", month)
        .single()

      if (error) {
        if (error.code === "PGRST116") {
          toast({
            title: "未找到报告",
            description: `${year}年${month}月的读书报告不存在`,
          })
        } else {
          toast({
            title: "加载失败",
            description: error.message,
            variant: "destructive",
          })
        }
      } else if (data) {
        setTitle(data.title || "")
        setContent(data.content || "")
        toast({
          title: "加载成功",
          description: `已加载${year}年${month}月的读书报告`,
        })
      }
    } catch (error) {
      console.error("Error loading report:", error)
      toast({
        title: "发生错误",
        description: "加载报告时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 简单的Markdown转HTML函数
  const markdownToHtml = (markdown: string) => {
    if (!markdown) return ""

    // 非常基础的Markdown转换
    return markdown
      .replace(/^# (.*$)/gm, "<h1>$1</h1>")
      .replace(/^## (.*$)/gm, "<h2>$1</h2>")
      .replace(/^### (.*$)/gm, "<h3>$1</h3>")
      .replace(/\*\*(.*)\*\*/gm, "<strong>$1</strong>")
      .replace(/\*(.*)\*/gm, "<em>$1</em>")
      .replace(/\n/gm, "<br>")
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">上传月度报告</h2>
        <Button variant="outline" onClick={() => setIsPreview(!isPreview)}>
          {isPreview ? "编辑模式" : "预览模式"}
        </Button>
      </div>

      <Card className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="year">年份</Label>
              <Input
                id="year"
                type="number"
                value={year}
                onChange={(e) => setYear(Number.parseInt(e.target.value))}
                min="2000"
                max="2100"
                required
              />
            </div>
            <div>
              <Label htmlFor="month">月份</Label>
              <Input
                id="month"
                type="number"
                value={month}
                onChange={(e) => setMonth(Number.parseInt(e.target.value))}
                min="1"
                max="12"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="title">标题（可选）</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder={`${year}年${month}月读书报告`}
            />
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <Label htmlFor="content">报告内容 (Markdown格式)</Label>
              <Button type="button" variant="outline" size="sm" onClick={loadExistingReport}>
                加载现有报告
              </Button>
            </div>

            {isPreview ? (
              <div
                className="border rounded-md p-4 min-h-[300px] prose max-w-none"
                dangerouslySetInnerHTML={{ __html: markdownToHtml(content) }}
              />
            ) : (
              <Textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="# 月度读书报告\n\n## 本月概况\n\n## 精选书籍\n\n## 读者反馈"
                className="min-h-[300px] font-mono"
              />
            )}
          </div>

          <div>
            <Label htmlFor="file">或上传Markdown文件</Label>
            <Input id="file" type="file" accept=".md,.markdown,.txt" onChange={handleFileChange} />
          </div>

          <div className="flex justify-end space-x-4">
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "保存中..." : "保存报告"}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  )
}
