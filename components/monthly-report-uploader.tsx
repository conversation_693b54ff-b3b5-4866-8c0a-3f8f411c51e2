"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createClient } from "@supabase/supabase-js"
import { FileText, Upload, Loader2, Eye, RefreshCw } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// 月份名称
const monthNames = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]

export function MonthlyReportUploader() {
  const [isUploading, setIsUploading] = useState(false)
  const [message, setMessage] = useState<{ type: "success" | "error"; text: string } | null>(null)
  const [year, setYear] = useState<number>(new Date().getFullYear())
  const [month, setMonth] = useState<number>(new Date().getMonth() + 1)
  const [markdownContent, setMarkdownContent] = useState<string>("")
  const [file, setFile] = useState<File | null>(null)
  const [existingReports, setExistingReports] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("upload")

  // 生成年份选项（当前年份和前后5年）
  const currentYear = new Date().getFullYear()
  const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i)

  // 加载已有的报告
  useEffect(() => {
    const fetchExistingReports = async () => {
      try {
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        const supabase = createClient(supabaseUrl, supabaseKey)

        const { data, error } = await supabase
          .from("monthly_reports")
          .select("*")
          .order("year", { ascending: false })
          .order("month", { ascending: true })

        if (error) {
          if (error.message.includes("does not exist")) {
            console.log("monthly_reports表不存在")
            setExistingReports([])
          } else {
            throw error
          }
        } else {
          setExistingReports(data || [])
        }
      } catch (error) {
        console.error("Error fetching reports:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchExistingReports()
  }, [])

  // 加载特定报告内容
  const loadReport = async (reportYear: number, reportMonth: number) => {
    try {
      setIsLoading(true)
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      const supabase = createClient(supabaseUrl, supabaseKey)

      const { data, error } = await supabase
        .from("monthly_reports")
        .select("content")
        .eq("year", reportYear)
        .eq("month", reportMonth)
        .single()

      if (error) throw error

      setYear(reportYear)
      setMonth(reportMonth)
      setMarkdownContent(data.content)
      setActiveTab("upload")
    } catch (error) {
      console.error("Error loading report:", error)
      setMessage({ type: "error", text: `加载报告失败: ${error.message}` })
    } finally {
      setIsLoading(false)
    }
  }

  // 删除报告
  const deleteReport = async (id: number) => {
    if (!confirm("确定要删除这个报告吗？此操作不可撤销。")) {
      return
    }

    try {
      setIsLoading(true)
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      const supabase = createClient(supabaseUrl, supabaseKey)

      const { error } = await supabase.from("monthly_reports").delete().eq("id", id)

      if (error) throw error

      // 刷新报告列表
      const { data: updatedReports } = await supabase
        .from("monthly_reports")
        .select("*")
        .order("year", { ascending: false })
        .order("month", { ascending: true })

      setExistingReports(updatedReports || [])
      setMessage({ type: "success", text: "报告已成功删除" })
    } catch (error) {
      console.error("Error deleting report:", error)
      setMessage({ type: "error", text: `删除报告失败: ${error.message}` })
    } finally {
      setIsLoading(false)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (!selectedFile) return

    setFile(selectedFile)

    // 读取文件内容
    const reader = new FileReader()
    reader.onload = (event) => {
      const content = event.target?.result as string
      setMarkdownContent(content)
    }
    reader.readAsText(selectedFile)
  }

  const handleUpload = async () => {
    if (!markdownContent) {
      setMessage({ type: "error", text: "请先选择Markdown文件或输入内容" })
      return
    }

    setIsUploading(true)
    setMessage(null)

    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      const supabase = createClient(supabaseUrl, supabaseKey)

      // 检查是否已存在该月份的报告
      const { data: existingReport, error: checkError } = await supabase
        .from("monthly_reports")
        .select("id")
        .eq("year", year)
        .eq("month", month)
        .maybeSingle()

      if (checkError) {
        // 检查是否是因为表不存在
        if (checkError.message.includes("does not exist")) {
          setMessage({
            type: "error",
            text: "monthly_reports表不存在，请先创建数据表。您可以运行create-monthly-reports-table.sql脚本创建表。",
          })
          return
        }
        throw checkError
      }

      if (existingReport) {
        // 更新现有报告
        const { error } = await supabase
          .from("monthly_reports")
          .update({
            content: markdownContent,
            updated_at: new Date().toISOString(),
          })
          .eq("id", existingReport.id)

        if (error) throw error
        setMessage({ type: "success", text: `${year}年${monthNames[month - 1]}报告已更新` })
      } else {
        // 创建新报告
        const { error } = await supabase.from("monthly_reports").insert({
          year,
          month,
          title: `${year}年${monthNames[month - 1]}读书群阅读数据分析报告`,
          content: markdownContent,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })

        if (error) throw error
        setMessage({ type: "success", text: `${year}年${monthNames[month - 1]}报告已上传` })
      }

      // 刷新报告列表
      const { data: updatedReports } = await supabase
        .from("monthly_reports")
        .select("*")
        .order("year", { ascending: false })
        .order("month", { ascending: true })

      setExistingReports(updatedReports || [])

      // 清空文件选择
      setFile(null)
    } catch (error) {
      console.error("Upload error:", error)
      setMessage({ type: "error", text: `上传失败: ${error.message}` })
    } finally {
      setIsUploading(false)
    }
  }

  const refreshReports = async () => {
    try {
      setIsLoading(true)
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      const supabase = createClient(supabaseUrl, supabaseKey)

      const { data, error } = await supabase
        .from("monthly_reports")
        .select("*")
        .order("year", { ascending: false })
        .order("month", { ascending: true })

      if (error) throw error
      setExistingReports(data || [])
    } catch (error) {
      console.error("Error refreshing reports:", error)
      setMessage({ type: "error", text: `刷新报告列表失败: ${error.message}` })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>月度报告管理</CardTitle>
        <CardDescription>上传和管理Markdown格式的月度阅读报告</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="upload">上传报告</TabsTrigger>
            <TabsTrigger value="manage">管理报告</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="year">年份</Label>
                <Select value={year.toString()} onValueChange={(value) => setYear(Number.parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择年份" />
                  </SelectTrigger>
                  <SelectContent>
                    {yearOptions.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}年
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="month">月份</Label>
                <Select value={month.toString()} onValueChange={(value) => setMonth(Number.parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择月份" />
                  </SelectTrigger>
                  <SelectContent>
                    {monthNames.map((name, index) => (
                      <SelectItem key={index} value={(index + 1).toString()}>
                        {name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="file">上传Markdown文件</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="file"
                  type="file"
                  accept=".md,.markdown,text/markdown"
                  onChange={handleFileChange}
                  className="flex-1"
                />
                <Button variant="outline" onClick={() => document.getElementById("file")?.click()}>
                  <FileText className="h-4 w-4 mr-2" />
                  选择文件
                </Button>
              </div>
              {file && <p className="text-sm text-muted-foreground">已选择: {file.name}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Markdown内容</Label>
              <Textarea
                id="content"
                value={markdownContent}
                onChange={(e) => setMarkdownContent(e.target.value)}
                placeholder="# 2025年1月读书群阅读数据分析报告"
                className="min-h-[300px] font-mono"
              />
            </div>

            {message && (
              <Alert
                variant={message.type === "error" ? "destructive" : "default"}
                className={message.type === "success" ? "bg-green-50 text-green-700 border-green-200" : ""}
              >
                <AlertDescription>{message.text}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-between mt-4">
              <Button variant="outline" asChild>
                <Link href={`/${year}/${month}`} target="_blank">
                  <Eye className="h-4 w-4 mr-2" />
                  预览当前月份
                </Link>
              </Button>
              <Button onClick={handleUpload} disabled={isUploading}>
                {isUploading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    上传中...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    上传报告
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="manage">
            <div className="flex justify-between mb-4">
              <h3 className="text-lg font-medium">已有报告</h3>
              <Button variant="outline" size="sm" onClick={refreshReports} disabled={isLoading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
                刷新
              </Button>
            </div>

            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : existingReports.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                暂无报告数据。请先上传报告，或确认monthly_reports表已创建。
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>年份</TableHead>
                      <TableHead>月份</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead>更新时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {existingReports.map((report) => (
                      <TableRow key={report.id}>
                        <TableCell>{report.year}年</TableCell>
                        <TableCell>{monthNames[report.month - 1]}</TableCell>
                        <TableCell>{new Date(report.created_at).toLocaleString()}</TableCell>
                        <TableCell>{new Date(report.updated_at).toLocaleString()}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" size="sm" onClick={() => loadReport(report.year, report.month)}>
                              编辑
                            </Button>
                            <Button variant="outline" size="sm" asChild>
                              <Link href={`/${report.year}/${report.month}`} target="_blank">
                                预览
                              </Link>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                              onClick={() => deleteReport(report.id)}
                            >
                              删除
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
