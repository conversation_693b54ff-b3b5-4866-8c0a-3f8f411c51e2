"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface Reader {
  rank: number
  name: string
  avatar: string
  totalBooks: number
  completedBooks: number
  completionRate: number
}

interface ReaderLeaderboardProps {
  title?: string
  subtitle?: string
  readers: Reader[]
  showCount?: number
  pageSize?: number
}

export function ReaderLeaderboard({
  title = "阅读排行榜",
  subtitle = "阅读量最多的成员",
  readers,
  showCount = 15,
  pageSize = 15,
}: ReaderLeaderboardProps) {
  const [currentPage, setCurrentPage] = useState(0)

  // 限制显示的读者数量
  const displayReaders = readers.slice(0, showCount)

  // 计算总页数
  const totalPages = Math.ceil(displayReaders.length / pageSize)

  // 获取当前页的读者
  const currentReaders = displayReaders.slice(currentPage * pageSize, (currentPage + 1) * pageSize)

  // 页面导航
  const goToNextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1)
    }
  }

  const goToPrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1)
    }
  }

  // 获取排名对应的背景色
  const getRankBgColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "bg-amber-500"
      case 2:
        return "bg-gray-400"
      case 3:
        return "bg-amber-700"
      default:
        return "bg-gray-200"
    }
  }

  // 获取排名对应的文字颜色
  const getRankTextColor = (rank: number) => {
    switch (rank) {
      case 1:
      case 2:
      case 3:
        return "text-white"
      default:
        return "text-gray-700"
    }
  }

  // 获取头像背景色
  const getAvatarBgColor = (avatar: string) => {
    const colors = {
      小: "bg-red-100 text-red-500",
      C: "bg-purple-100 text-purple-500",
      娃: "bg-pink-100 text-pink-500",
      梦: "bg-blue-100 text-blue-500",
      杜: "bg-rose-100 text-rose-500",
      李: "bg-green-100 text-green-500",
      王: "bg-yellow-100 text-yellow-500",
      张: "bg-indigo-100 text-indigo-500",
      刘: "bg-cyan-100 text-cyan-500",
      陈: "bg-amber-100 text-amber-500",
      赵: "bg-lime-100 text-lime-500",
      黄: "bg-orange-100 text-orange-500",
      周: "bg-teal-100 text-teal-500",
      吴: "bg-violet-100 text-violet-500",
      郑: "bg-fuchsia-100 text-fuchsia-500",
    }
    return colors[avatar.charAt(0)] || "bg-gray-100 text-gray-500"
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CardTitle className="text-base font-medium">{title}</CardTitle>
          </div>
          {totalPages > 1 && (
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={goToPrevPage}
                disabled={currentPage === 0}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-xs text-muted-foreground">
                {currentPage + 1} / {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={goToNextPage}
                disabled={currentPage === totalPages - 1}
                className="h-8 w-8 p-0"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          <div className="grid grid-cols-12 text-xs font-medium text-muted-foreground mb-2">
            <div className="col-span-1">排名</div>
            <div className="col-span-3">成员</div>
            <div className="col-span-2 text-right">阅读书籍</div>
            <div className="col-span-2 text-right">已读完</div>
            <div className="col-span-4 text-right">完成率</div>
          </div>

          {currentReaders.map((reader) => (
            <div key={reader.rank} className="grid grid-cols-12 py-2 items-center border-t text-sm">
              <div className="col-span-1">
                <span
                  className={`inline-flex items-center justify-center w-6 h-6 rounded-full ${getRankBgColor(
                    reader.rank,
                  )} ${getRankTextColor(reader.rank)}`}
                >
                  {reader.rank}
                </span>
              </div>
              <div className="col-span-3 flex items-center">
                <span
                  className={`inline-flex items-center justify-center w-6 h-6 rounded-full mr-2 ${getAvatarBgColor(
                    reader.avatar,
                  )}`}
                >
                  {reader.avatar.charAt(0)}
                </span>
                <span>{reader.name}</span>
              </div>
              <div className="col-span-2 text-right">{reader.totalBooks}</div>
              <div className="col-span-2 text-right">{reader.completedBooks}</div>
              <div className="col-span-4 flex items-center justify-end">
                <div className="w-full max-w-[100px] bg-gray-200 rounded-full h-2 mr-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${reader.completionRate}%` }}></div>
                </div>
                <span className="text-xs">{reader.completionRate}%</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
