"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface Reader {
  rank: number
  name: string
  avatar: string
  totalBooks: number
  completedBooks: number
  completionRate: number
}

interface ReaderLeaderboardProps {
  title?: string
  subtitle?: string
  readers: Reader[]
  showCount?: number
  pageSize?: number
}

export function ReaderLeaderboard({
  title = "阅读排行榜",
  subtitle = "阅读量最多的成员",
  readers,
  showCount = 15,
  pageSize = 15,
}: ReaderLeaderboardProps) {
  const [currentPage, setCurrentPage] = useState(0)

  // 限制显示的读者数量
  const displayReaders = readers.slice(0, showCount)

  // 计算总页数
  const totalPages = Math.ceil(displayReaders.length / pageSize)

  // 获取当前页的读者
  const currentReaders = displayReaders.slice(currentPage * pageSize, (currentPage + 1) * pageSize)

  // 页面导航
  const goToNextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1)
    }
  }

  const goToPrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1)
    }
  }

  // 获取排名对应的背景色
  const getRankBgColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "bg-amber-500"
      case 2:
        return "bg-gray-400"
      case 3:
        return "bg-amber-700"
      default:
        return "bg-gray-200"
    }
  }

  // 获取排名对应的文字颜色
  const getRankTextColor = (rank: number) => {
    switch (rank) {
      case 1:
      case 2:
      case 3:
        return "text-white"
      default:
        return "text-gray-700"
    }
  }

  // 获取头像背景色
  const getAvatarBgColor = (avatar: string, name: string) => {
    // 扩展的颜色映射表，包含更多字符和更丰富的颜色
    const colors = {
      // 中文字符
      小: "bg-red-100 text-red-500",
      C: "bg-purple-100 text-purple-500",
      娃: "bg-pink-100 text-pink-500",
      梦: "bg-blue-100 text-blue-500",
      杜: "bg-rose-100 text-rose-500",
      李: "bg-green-100 text-green-500",
      王: "bg-yellow-100 text-yellow-500",
      张: "bg-indigo-100 text-indigo-500",
      刘: "bg-cyan-100 text-cyan-500",
      陈: "bg-amber-100 text-amber-500",
      赵: "bg-lime-100 text-lime-500",
      黄: "bg-orange-100 text-orange-500",
      周: "bg-teal-100 text-teal-500",
      吴: "bg-violet-100 text-violet-500",
      郑: "bg-fuchsia-100 text-fuchsia-500",

      // 英文字符
      A: "bg-red-100 text-red-500",
      B: "bg-orange-100 text-orange-500",
      C: "bg-amber-100 text-amber-500",
      D: "bg-yellow-100 text-yellow-500",
      E: "bg-lime-100 text-lime-500",
      F: "bg-green-100 text-green-500",
      G: "bg-emerald-100 text-emerald-500",
      H: "bg-teal-100 text-teal-500",
      I: "bg-cyan-100 text-cyan-500",
      J: "bg-sky-100 text-sky-500",
      K: "bg-blue-100 text-blue-500",
      L: "bg-indigo-100 text-indigo-500",
      M: "bg-violet-100 text-violet-500",
      N: "bg-purple-100 text-purple-500",
      O: "bg-fuchsia-100 text-fuchsia-500",
      P: "bg-pink-100 text-pink-500",
      Q: "bg-rose-100 text-rose-500",
      R: "bg-red-100 text-red-600",
      S: "bg-orange-100 text-orange-600",
      T: "bg-amber-100 text-amber-600",
      U: "bg-yellow-100 text-yellow-600",
      V: "bg-lime-100 text-lime-600",
      W: "bg-green-100 text-green-600",
      X: "bg-emerald-100 text-emerald-600",
      Y: "bg-teal-100 text-teal-600",
      Z: "bg-cyan-100 text-cyan-600",

      // 数字
      "0": "bg-blue-100 text-blue-500",
      "1": "bg-indigo-100 text-indigo-500",
      "2": "bg-violet-100 text-violet-500",
      "3": "bg-purple-100 text-purple-500",
      "4": "bg-fuchsia-100 text-fuchsia-500",
      "5": "bg-pink-100 text-pink-500",
      "6": "bg-rose-100 text-rose-500",
      "7": "bg-red-100 text-red-500",
      "8": "bg-orange-100 text-orange-500",
      "9": "bg-amber-100 text-amber-500",
    }

    // 首先检查第一个字符是否在映射表中
    const firstChar = avatar.charAt(0).toUpperCase()
    if (colors[firstChar]) {
      return colors[firstChar]
    }

    // 如果不在映射表中，根据名称生成一个颜色
    // 使用名称的简单哈希来选择颜色
    const colorOptions = [
      "bg-red-100 text-red-500",
      "bg-orange-100 text-orange-500",
      "bg-amber-100 text-amber-500",
      "bg-yellow-100 text-yellow-500",
      "bg-lime-100 text-lime-500",
      "bg-green-100 text-green-500",
      "bg-emerald-100 text-emerald-500",
      "bg-teal-100 text-teal-500",
      "bg-cyan-100 text-cyan-500",
      "bg-sky-100 text-sky-500",
      "bg-blue-100 text-blue-500",
      "bg-indigo-100 text-indigo-500",
      "bg-violet-100 text-violet-500",
      "bg-purple-100 text-purple-500",
      "bg-fuchsia-100 text-fuchsia-500",
      "bg-pink-100 text-pink-500",
      "bg-rose-100 text-rose-500",
    ]

    // 简单的字符串哈希函数
    let hash = 0
    for (let i = 0; i < name.length; i++) {
      hash = (hash << 5) - hash + name.charCodeAt(i)
      hash = hash & hash // 转换为32位整数
    }

    // 使用哈希值选择颜色
    const colorIndex = Math.abs(hash) % colorOptions.length
    return colorOptions[colorIndex]
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CardTitle className="text-base font-medium">{title}</CardTitle>
          </div>
          {totalPages > 1 && (
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={goToPrevPage}
                disabled={currentPage === 0}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-xs text-muted-foreground">
                {currentPage + 1} / {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={goToNextPage}
                disabled={currentPage === totalPages - 1}
                className="h-8 w-8 p-0"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          <div className="grid grid-cols-12 text-xs font-medium text-muted-foreground mb-2">
            <div className="col-span-1">排名</div>
            <div className="col-span-3">成员</div>
            <div className="col-span-2 text-right">阅读书籍</div>
            <div className="col-span-2 text-right">已读完</div>
            <div className="col-span-4 text-right">完成率</div>
          </div>

          {currentReaders.map((reader) => (
            <div key={reader.rank} className="grid grid-cols-12 py-2 items-center border-t text-sm">
              <div className="col-span-1">
                <span
                  className={`inline-flex items-center justify-center w-6 h-6 rounded-full ${getRankBgColor(
                    reader.rank,
                  )} ${getRankTextColor(reader.rank)}`}
                >
                  {reader.rank}
                </span>
              </div>
              <div className="col-span-3 flex items-center">
                <span
                  className={`inline-flex items-center justify-center w-6 h-6 rounded-full mr-2 ${getAvatarBgColor(
                    reader.avatar,
                    reader.name,
                  )}`}
                >
                  {reader.avatar.charAt(0)}
                </span>
                <span>{reader.name}</span>
              </div>
              <div className="col-span-2 text-right">{reader.totalBooks}</div>
              <div className="col-span-2 text-right">{reader.completedBooks}</div>
              <div className="col-span-4 flex items-center justify-end">
                <div className="w-full max-w-[100px] bg-gray-200 rounded-full h-2 mr-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${reader.completionRate}%` }}></div>
                </div>
                <span className="text-xs">{reader.completionRate}%</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
