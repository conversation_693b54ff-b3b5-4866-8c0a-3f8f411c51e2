"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Mail, User, BookOpen, Copy, CheckCircle2, UserPlus } from "lucide-react"
import { cn } from "@/lib/utils"

interface JoinUsDialogProps {
  children?: React.ReactNode
  className?: string
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive"
  size?: "default" | "sm" | "lg" | "icon"
}

export function JoinUsDialog({ children, className, variant = "default", size = "default" }: JoinUsDialogProps) {
  const [open, setOpen] = useState(false)
  const [copiedWechat, setCopiedWechat] = useState(false)
  const [copiedEmail, setCopiedEmail] = useState(false)

  const wechatId = "spookfox"
  const email = "<EMAIL>"

  // 内联实现剪贴板功能
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      return true
    } catch (error) {
      console.error("复制到剪贴板失败:", error)
      return false
    }
  }

  const handleCopyWechat = async () => {
    const success = await copyToClipboard(wechatId)
    if (success) {
      setCopiedWechat(true)
      setTimeout(() => setCopiedWechat(false), 2000)
    }
  }

  const handleCopyEmail = async () => {
    const success = await copyToClipboard(email)
    if (success) {
      setCopiedEmail(true)
      setTimeout(() => setCopiedEmail(false), 2000)
    }
  }

  return (
    <>
      {children ? (
        <div onClick={() => setOpen(true)}>{children}</div>
      ) : (
        <Button
          variant="default"
          size={size}
          onClick={() => setOpen(true)}
          className={cn(
            "bg-black hover:bg-gray-800 text-white shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden group",
            className,
          )}
        >
          <UserPlus className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
          <span className="relative z-10">加入我们</span>
          <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
        </Button>
      )}

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <div className="flex items-center justify-center mb-2">
              <div className="bg-blue-50 p-3 rounded-full">
                <BookOpen className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <DialogTitle className="text-center text-xl font-bold">欢迎加入我们的读书群！</DialogTitle>
          </DialogHeader>

          <div className="mt-4 space-y-6">
            <p className="text-center text-muted-foreground">
              读书群内有各种学科的阅读爱好者，我们每天通过群接龙分享自己的阅读书籍，月终根据这些信息分享，总结汇总在本网站中。
            </p>

            <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
              <h3 className="text-lg font-medium mb-4 text-center">加入方式</h3>

              <div className="space-y-4">
                <div className="bg-white p-3 rounded-md border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center gap-3">
                    <div className="bg-blue-50 p-2 rounded-full">
                      <User className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">添加群主微信</div>
                      <div className="text-sm text-muted-foreground flex items-center justify-between">
                        <span>{wechatId}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 px-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          onClick={handleCopyWechat}
                        >
                          {copiedWechat ? <CheckCircle2 className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
                          {copiedWechat ? "已复制" : "复制"}
                        </Button>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">备注"读书群"</div>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-3 rounded-md border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center gap-3">
                    <div className="bg-blue-50 p-2 rounded-full">
                      <Mail className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">发送邮件</div>
                      <div className="text-sm text-muted-foreground flex items-center justify-between">
                        <span className="truncate max-w-[180px]">{email}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 px-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          onClick={handleCopyEmail}
                        >
                          {copiedEmail ? <CheckCircle2 className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
                          {copiedEmail ? "已复制" : "复制"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center text-sm text-muted-foreground">期待您的加入，一起分享阅读的乐趣！</div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
