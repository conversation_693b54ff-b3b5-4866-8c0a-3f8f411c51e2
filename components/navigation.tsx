"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Home, BarChart3, Upload, FileText } from "lucide-react"

export function Navigation() {
  const pathname = usePathname()

  const routes = [
    {
      href: "/",
      label: "主页",
      icon: <Home className="h-5 w-5" />,
      active: pathname === "/",
    },
    {
      href: "/dashboard",
      label: "数据分析",
      icon: <BarChart3 className="h-5 w-5" />,
      active: pathname === "/dashboard",
    },
    {
      href: "/import-data",
      label: "导入数据",
      icon: <Upload className="h-5 w-5" />,
      active: pathname.includes("/import-data"),
    },
    {
      href: "/reports",
      label: "年度报告",
      icon: <FileText className="h-5 w-5" />,
      active: pathname === "/reports",
    },
  ]

  return (
    <nav className="flex items-center space-x-4 lg:space-x-6 mx-6">
      {routes.map((route) => (
        <Link
          key={route.href}
          href={route.href}
          className={cn(
            "flex items-center text-sm font-medium transition-colors hover:text-primary",
            route.active ? "text-black dark:text-white" : "text-muted-foreground",
          )}
        >
          {route.icon}
          <span className="ml-2">{route.label}</span>
        </Link>
      ))}
    </nav>
  )
}
