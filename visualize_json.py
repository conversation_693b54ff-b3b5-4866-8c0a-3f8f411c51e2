import json
import os
import glob
import readline
import platform
from collections import defaultdict


def get_json_files():
    """获取rawdata目录下的所有JSON文件"""
    data_dir = os.path.join(os.path.dirname(
        os.path.abspath(__file__)), 'rawdata')
    files = glob.glob(os.path.join(data_dir, '*.json'))
    return [os.path.basename(f) for f in files]


def completer(text, state):
    """用于自动补全的函数"""
    json_files = get_json_files()
    matches = [f for f in json_files if f.startswith(text)]
    if state < len(matches):
        return matches[state]
    else:
        return None


def select_file_smart():
    """智能文件选择 - 增强的自动补全和预设默认值"""
    json_files = get_json_files()
    if not json_files:
        print("数据目录中没有找到任何JSON文件")
        return None

    # 设置自动补全
    readline.set_completer(completer)

    # 根据操作系统设置不同的补全方式
    if platform.system() == 'Darwin':  # macOS
        readline.parse_and_bind("bind ^I rl_complete")
    else:
        readline.parse_and_bind("tab: complete")

    # 显示文件列表，用彩色高亮最新的文件
    print("\n可用的JSON文件:")

    # 按修改时间排序文件
    data_dir = os.path.join(os.path.dirname(
        os.path.abspath(__file__)), 'rawdata')
    sorted_files = sorted(
        [(f, os.path.getmtime(os.path.join(data_dir, f))) for f in json_files],
        key=lambda x: x[1],
        reverse=True  # 最新的文件排在前面
    )

    # 设置最新文件为默认选择
    default_file = sorted_files[0][0] if sorted_files else None

    # 显示文件列表，最新的文件带星号标记
    for i, (f, mtime) in enumerate(sorted_files, 1):
        if i == 1:  # 最新文件
            print(f"  {i}. \033[1m{f}\033[0m (最新，推荐)")  # 粗体显示
        else:
            print(f"  {i}. {f}")

    # 询问用户是否使用默认文件
    if default_file:
        choice = input(
            f"\n按回车键选择默认文件 [{default_file}] 或输入编号(1-{len(sorted_files)}): ")

        if choice.strip() == "":
            # 用户按回车，使用默认文件
            return os.path.join('rawdata', default_file)

        # 尝试解析数字选择
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(sorted_files):
                return os.path.join('rawdata', sorted_files[idx][0])
        except ValueError:
            # 不是数字，尝试作为文件名处理
            if choice in json_files:
                return os.path.join('rawdata', choice)

        # 如果以上都不匹配，回到手动输入
        print("无效选择，请手动输入文件名:")

    # 手动输入模式
    while True:
        prompt = "\n请输入要处理的文件名 (按Tab键自动补全"
        if default_file:
            prompt += f"，默认为 {default_file}"
        prompt += "): "

        file_name = input(prompt)

        # 如果输入为空且有默认值，使用默认值
        if file_name.strip() == "" and default_file:
            return os.path.join('rawdata', default_file)

        if file_name in json_files:
            return os.path.join('rawdata', file_name)
        else:
            print(f"文件 '{file_name}' 不存在，请重新输入")


def visualize_json(file_path):
    """将JSON数据转换为人类可读的文本格式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"错误：无法读取JSON文件 - {e}")
        return None

    # 创建可视化输出
    output = []

    # 获取基本信息用于控制台显示
    base_name = os.path.basename(file_path).replace('.json', '')
    month = base_name[:6]  # 假设格式为YYYYMM

    # 统计信息 - 仅用于控制台显示，不写入文件
    stats = data.get('stats', {})
    total_participants = stats.get('total_participants', 0)

    # 按照读书数量排序用户
    users = sorted(data.get('users', []), key=lambda x: x.get(
        'total_books', 0), reverse=True)

    # 分类统计
    category_count = defaultdict(int)
    reading_status_count = defaultdict(int)

    # 实际书籍总数，通过累加所有用户的书籍来计算
    actual_total_books = 0

    # 添加所有用户的信息，即使没有阅读记录
    for i, user in enumerate(users, 1):
        user_id = user.get('user_id', '未知用户')
        total_books = user.get('total_books', 0)

        # 累加实际书籍数量
        actual_total_books += total_books

        # 添加用户信息，无论是否有读书记录
        output.append(f"## {i}. {user_id} ({total_books}本)")

        # 用户的书籍列表
        books = user.get('books', [])
        for j, book in enumerate(books, 1):
            title = book.get('title', '未知书名')
            status = book.get('reading_status', '未知状态')
            progress = book.get('progress', '未知进度')
            categories = book.get('category', ['未分类'])

            # 更新分类统计
            for category in categories:
                category_count[category] += 1

            # 更新阅读状态统计
            reading_status_count[status] += 1

            # 格式化分类信息
            category_info = '、'.join(categories)

            output.append(
                f"   {j}. 《{title}》 - {status} ({progress}) [{category_info}]")

        # 如果没有书籍记录，添加一个提示信息
        if total_books == 0:
            output.append("   (暂无阅读记录)")

        output.append("")  # 空行分隔

    # 返回可视化内容和统计信息（统计信息仅用于控制台显示）
    return "\n".join(output), {
        "month": month,
        "total_participants": total_participants,
        "total_books": actual_total_books,  # 使用实际计算的书籍总数
        "category_count": category_count,
        "reading_status_count": reading_status_count
    }


def main():
    print("欢迎使用JSON可视化工具！")
    print("=" * 50)

    # 获取文件列表
    json_files = get_json_files()
    if not json_files:
        print("错误: 数据目录中没有找到任何JSON文件")
        return

    # 选择要可视化的文件
    file_path = select_file_smart()

    if not file_path:
        print("未选择文件，程序退出")
        return

    # 处理选择的文件
    print(f"\n正在处理文件: {file_path}")
    result = visualize_json(file_path)

    if result is None:
        print("处理文件时出错，程序退出")
        return

    visualized_content, stats = result

    # 生成输出文件名
    base_name = os.path.basename(file_path)
    output_name = base_name.replace('.json', '_visualized.md')

    # 获取rawdata目录路径
    output_dir = os.path.dirname(file_path)
    output_path = os.path.join(output_dir, output_name)

    # 保存可视化结果 - 只保存用户和书籍信息
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(visualized_content)

    # 在控制台显示完整的报告信息
    print("\n" + "=" * 50)
    print(f"# {stats['month'][:4]}年{stats['month'][4:6]}月读书分享可视化报告")
    print("\n## 总体统计")
    print(f"- 参与人数: {stats['total_participants']}人")
    print(f"- 阅读书籍总数: {stats['total_books']}本")

    # 移除书籍分类统计部分
    # print("\n## 书籍分类统计")
    # sorted_categories = sorted(
    #     stats['category_count'].items(), key=lambda x: x[1], reverse=True)
    # for category, count in sorted_categories:
    #     print(f"- {category}: {count}本")

    print("\n## 阅读状态统计")
    for status, count in stats['reading_status_count'].items():
        print(f"- {status}: {count}本")

    print("\n" + "=" * 50)
    print(f"处理完成，结果已保存到: {output_path}")
    print(f"注意：完整的用户书单已保存到文件，统计信息仅在此控制台显示")


if __name__ == "__main__":
    main()
