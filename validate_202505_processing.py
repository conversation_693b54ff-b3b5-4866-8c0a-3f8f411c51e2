#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import re
from collections import defaultdict

def validate_processing():
    """
    校验202505.txt的处理结果
    """
    # 读取原始文件
    with open('rawdata/202505.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 读取处理后的JSON文件
    with open('rawdata/202505.json', 'r', encoding='utf-8') as f:
        json_data = json.load(f)
    
    # 分析原始文件中的用户
    user_pattern = re.compile(r'^\s*(\d+)\.\s+(.+)')
    original_users = {}
    user_occurrences = defaultdict(list)
    
    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        user_match = user_pattern.match(line)
        if user_match:
            number = user_match.group(1)
            user_info = user_match.group(2).strip()
            
            # 提取用户名（第一个空格前的内容）
            parts = user_info.split()
            if parts:
                user_name = parts[0]
                
                # 处理特殊情况：🌴 丽 🌴
                if len(parts) > 1 and parts[1] == "丽" and "🌴" in user_name:
                    user_name = "丽"
                
                # 清理用户名
                if "丽" in user_name and "🌴" in user_name:
                    user_name = "丽"
                
                original_users[number] = user_name
                user_occurrences[user_name].append(number)
    
    print("=== 原始文件分析 ===")
    print(f"总编号数量: {len(original_users)}")
    print(f"唯一用户数量: {len(user_occurrences)}")
    print()
    
    print("=== 重复用户分析 ===")
    duplicate_users = {user: numbers for user, numbers in user_occurrences.items() if len(numbers) > 1}
    
    for user, numbers in duplicate_users.items():
        print(f"用户 '{user}' 出现 {len(numbers)} 次，编号: {', '.join(numbers)}")
    
    print()
    print("=== JSON文件分析 ===")
    json_users = [user['user_id'] for user in json_data['users']]
    print(f"JSON中用户数量: {len(json_users)}")
    print(f"JSON中总书籍数量: {json_data['stats']['total_books']}")
    print()
    
    print("=== 用户对比 ===")
    original_unique_users = set(user_occurrences.keys())
    json_unique_users = set(json_users)
    
    print(f"原始文件唯一用户: {len(original_unique_users)}")
    print(f"JSON文件用户: {len(json_unique_users)}")
    
    # 检查是否有用户丢失
    missing_users = original_unique_users - json_unique_users
    extra_users = json_unique_users - original_unique_users
    
    if missing_users:
        print(f"丢失的用户: {missing_users}")
    
    if extra_users:
        print(f"额外的用户: {extra_users}")
    
    if len(original_unique_users) == len(json_unique_users) and not missing_users and not extra_users:
        print("✅ 用户数量匹配正确！")
    else:
        print("❌ 用户数量不匹配")
    
    print()
    print("=== 详细重复情况 ===")
    for user, numbers in duplicate_users.items():
        print(f"\n用户: {user}")
        for num in numbers:
            # 找到对应的行
            for line_num, line in enumerate(lines, 1):
                if line.strip().startswith(f"{num}."):
                    print(f"  编号{num} (第{line_num}行): {line.strip()}")
                    break

if __name__ == "__main__":
    validate_processing()
