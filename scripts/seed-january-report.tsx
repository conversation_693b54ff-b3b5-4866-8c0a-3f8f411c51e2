"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createClient } from "@supabase/supabase-js"
import { Loader2 } from "lucide-react"

export default function SeedJanuaryReport() {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: "success" | "error"; text: string } | null>(null)

  const handleSeed = async () => {
    setIsLoading(true)
    setMessage(null)

    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      const supabase = createClient(supabaseUrl, supabaseKey)

      const januaryReport = {
        year: 2025,
        month: 1,
        title: "2025年一月读书群阅读数据分析报告",
        content: `# 2025年一月读书群阅读数据分析报告

## 一、参与情况与数据统计

### 1. 基本参与数据
- 总参与人数: 34人
- 有阅读记录的成员: 17人 (50%)
- 无阅读记录的成员: 17人 (50%)
- 总书籍数量: 127本

### 2. 参与度分布
- 高度活跃成员 (10本以上): 2人，占总成员5.9%
- 中度活跃成员 (5-9本): 5人，占总成员14.7%
- 低度活跃成员 (1-4本): 10人，占总成员29.4%
- 未参与成员: 17人，占总成员50%

### 3. 阅读完成情况
- 标记为"读完"的书籍: 39本，占总书籍30.7%
- 标记为"未知"或进度不明的书籍: 88本，占总书籍69.3%

### 4. 最活跃读者TOP5
- 小菜鸡: 45本 (其中21本已读完)
- Chris.W: 19本 (其中12本已读完)
- 娃娃: 9本
- 梦田: 8本
- 杜佳霖: 8本

## 二、阅读内容深度分析

### 1. 图书类别分布
- 文学类: 48.8%
- 哲学类: 18.1%
- 社会科学类: 9.4%
- 历史类: 3.1%
- 科学技术类: 3.1%
- 其他: 17.3%

### 2. 热门书籍排行
1. 《活着》 - ���华
2. 《三体》 - 刘慈欣
3. 《人类简史》 - 尤瓦尔·赫拉利
4. 《百年孤独》 - 加西亚·马尔克斯
5. 《深入理解计算机系统》 - Randal E. Bryant

## 三、阅读行为模式分析

### 1. 阅读时间分布
- 工作日阅读: 65%
- 周末阅读: 35%
- 晚间阅读(18:00-24:00): 70%
- 早晨阅读(6:00-9:00): 15%
- 其他时段: 15%

### 2. 阅读持续性
- 连续阅读(每周至少5天): 20%
- 间断阅读(每周2-4天): 45%
- 偶尔阅读(每周1天或更少): 35%

## 四、群体阅读特色与价值取向

### 1. 阅读偏好特点
- 文学作品偏好明显，尤其是经典文学和当代文学
- 哲学思考类书籍受到部分成员的持续关注
- 科技类书籍虽然比例不高，但完成率较高

### 2. 群体价值取向
- 注重人文关怀与思想深度
- 兼顾知识广度与专业深度
- 经典与流行并重

## 五、典型读者个案分析

### 小菜鸡: 多元探索与哲学深思型
- 总阅读量: 45本 (35.4%)
- 已读完成率: 46.7%
- 西方哲学研究深入
- 俄罗斯文学偏好明显
- 经典与现代文学兼顾

### Chris.W: 中西文化交融型
- 总阅读量: 19本
- 已读完成率: 63.2%
- 中西方文学并重
- 关注经典古籍
- 英文原著阅读比例高

### 梦田: 传统文化与灵性探索型
- 总阅读量: 8本
- 高度聚焦于传统文化研究
- 宗教学、哲学专题阅读
- 宗教研究与东方哲学结合
- 文化传承与灵性寻求

## 六、社会文化背景关联

### 1. 与当代社会热点的关联
- 科技伦理与人工智能话题的书籍阅读增加
- 环保与可持续发展主题受到关注
- 心理健康与自我成长类书籍比例上升

### 2. 文化传承与创新
- 传统文化经典的重新解读
- 跨文化比较阅读明显
- 新兴领域与传统学科的交叉阅读

## 七、下月阅读建议

### 1. 主题阅读推荐
- 科技与人文的交叉领域
- 东亚文化比较研究
- 当代社会问题深度探讨

### 2. 阅读活动建议
- 组织"作者背景探索"主题讨论
- 开展跨文化阅读体验分享
- 设立"深度阅读挑战"活动

---

报告生成日期: 2025年1月28日`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      // 检查是否已存在该月份的报告
      const { data: existingReport } = await supabase
        .from("monthly_reports")
        .select("id")
        .eq("year", 2025)
        .eq("month", 1)
        .single()

      if (existingReport) {
        // 更新现有报告
        const { error } = await supabase
          .from("monthly_reports")
          .update({
            content: januaryReport.content,
            updated_at: januaryReport.updated_at,
          })
          .eq("id", existingReport.id)

        if (error) throw error
        setMessage({ type: "success", text: "2025年1月报告已更新" })
      } else {
        // 创建新报告
        const { error } = await supabase.from("monthly_reports").insert(januaryReport)

        if (error) throw error
        setMessage({ type: "success", text: "2025年1月报告已成功添加到数据库" })
      }
    } catch (error) {
      console.error("Seed error:", error)
      setMessage({ type: "error", text: `添加报告失败: ${error.message}` })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <Button onClick={handleSeed} disabled={isLoading}>
        {isLoading ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            正在添加...
          </>
        ) : (
          "添加2025年1月报告"
        )}
      </Button>

      {message && (
        <Alert
          variant={message.type === "error" ? "destructive" : "default"}
          className={message.type === "success" ? "bg-green-50 text-green-700 border-green-200" : ""}
        >
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
