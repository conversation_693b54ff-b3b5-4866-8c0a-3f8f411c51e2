import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { NextResponse } from "next/server"

export const dynamic = "force-dynamic"

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // 获取所有主分类
    const { data: mainCategoriesData, error: mainCategoriesError } = await supabase
      .from("main_categories")
      .select("id, name")
      .order("name", { ascending: true })

    if (mainCategoriesError) {
      console.error("Error fetching main categories:", mainCategoriesError)
      return NextResponse.json({ error: mainCategoriesError.message }, { status: 500 })
    }

    // 获取所有子分类
    const { data: subCategoriesData, error: subCategoriesError } = await supabase
      .from("sub_categories")
      .select("id, name")
      .order("name", { ascending: true })

    if (subCategoriesError) {
      console.error("Error fetching sub categories:", subCategoriesError)
      return NextResponse.json({ error: subCategoriesError.message }, { status: 500 })
    }

    // 合并所有分类
    const allCategories = [...mainCategoriesData, ...subCategoriesData]

    return NextResponse.json(allCategories)
  } catch (error) {
    console.error("Unexpected error in categories API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
