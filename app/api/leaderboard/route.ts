import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { NextResponse } from "next/server"

// 设置为动态路由，确保每次请求都获取最新数据
export const dynamic = "force-dynamic"
export const revalidate = 0

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // 获取用户阅读记录统计
    const { data: readingStats, error: readingError } = await supabase.from("reading_records").select("user_id, status")

    if (readingError) {
      console.error("Error fetching reading records:", readingError)
      return NextResponse.json({ error: readingError.message }, { status: 500 })
    }

    // 获取所有用户信息
    const { data: users, error: usersError } = await supabase.from("users").select("user_id, name")

    if (usersError) {
      console.error("Error fetching users:", usersError)
      return NextResponse.json({ error: usersError.message }, { status: 500 })
    }

    // 处理数据，计算每个用户的阅读统计
    const userMap = new Map()

    // 初始化用户数据
    users.forEach((user) => {
      userMap.set(user.user_id, {
        name: user.name,
        avatar: user.name.charAt(0),
        totalBooks: 0,
        completedBooks: 0,
        completionRate: 0,
      })
    })

    // 统计阅读记录
    readingStats.forEach((record) => {
      if (userMap.has(record.user_id)) {
        const userData = userMap.get(record.user_id)
        userData.totalBooks++

        if (record.status === "已读完") {
          userData.completedBooks++
        }

        userMap.set(record.user_id, userData)
      }
    })

    // 计算完成率并转换为数组
    const leaderboardData = Array.from(userMap.entries()).map(([userId, data]) => {
      // 计算完成率
      const completionRate =
        data.totalBooks > 0 ? Number.parseFloat(((data.completedBooks / data.totalBooks) * 100).toFixed(1)) : 0

      return {
        userId,
        name: data.name,
        avatar: data.avatar,
        totalBooks: data.totalBooks,
        completedBooks: data.completedBooks,
        completionRate,
      }
    })

    // 按总书籍数量排序
    leaderboardData.sort((a, b) => b.totalBooks - a.totalBooks)

    // 添加排名
    const rankedData = leaderboardData.map((user, index) => ({
      ...user,
      rank: index + 1,
    }))

    // 只返回前20名
    const top20 = rankedData.slice(0, 20)

    return NextResponse.json(top20, {
      headers: {
        "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
        Pragma: "no-cache",
        Expires: "0",
        "Surrogate-Control": "no-store",
      },
    })
  } catch (error) {
    console.error("Unexpected error in leaderboard API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
