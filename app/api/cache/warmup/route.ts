import { NextResponse } from "next/server"
import { startWarmup, getWarmupStatus, getWarmupConfig, updateWarmupConfig } from "@/lib/cache-warmup"

export async function GET() {
  try {
    const status = getWarmupStatus()
    const config = getWarmupConfig()

    return NextResponse.json({
      status,
      config,
    })
  } catch (error) {
    return NextResponse.json({ error: "Failed to get warmup status" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const force = body.force === true

    // 如果提供了配置，更新配置
    if (body.config) {
      updateWarmupConfig(body.config)
    }

    // 启动预热
    const success = await startWarmup(force)

    return NextResponse.json({
      success,
      message: success ? "Cache warmup started" : "Cache warmup failed or already in progress",
      status: getWarmupStatus(),
    })
  } catch (error) {
    return NextResponse.json({ error: "Failed to start cache warmup" }, { status: 500 })
  }
}
