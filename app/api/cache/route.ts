import { NextResponse } from "next/server"
import { clearCache, getCacheStats, removeFrom<PERSON>ache } from "@/lib/cache"

export async function GET() {
  try {
    const stats = getCacheStats()
    return NextResponse.json(stats)
  } catch (error) {
    return NextResponse.json({ error: "Failed to get cache stats" }, { status: 500 })
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const key = searchParams.get("key")

    if (key) {
      // 删除特定缓存
      await removeFromCache(key)
      return NextResponse.json({ message: `Cache for key '${key}' cleared` })
    } else {
      // 清除所有缓存
      await clearCache()
      return NextResponse.json({ message: "All cache cleared" })
    }
  } catch (error) {
    return NextResponse.json({ error: "Failed to clear cache" }, { status: 500 })
  }
}
