import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { NextResponse } from "next/server"

export const dynamic = "force-dynamic"

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // 获取所有书籍
    const { data: booksData, error: booksError } = await supabase
      .from("books")
      .select("id, title")
      .order("title", { ascending: true })

    if (booksError) {
      console.error("Error fetching books:", booksError)
      return NextResponse.json({ error: booksError.message }, { status: 500 })
    }

    return NextResponse.json(booksData)
  } catch (error) {
    console.error("Unexpected error in books API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
