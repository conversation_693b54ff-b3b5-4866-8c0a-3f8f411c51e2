import { createClient } from "@/lib/supabase"
import { NextResponse } from "next/server"
import { getFromCache, setCache } from "@/lib/cache"

// 缓存时间设置
const CACHE_TTL = 10 * 60 * 1000 // 10分钟

export async function GET(request: Request, { params }: { params: { year: string; month: string } }) {
  try {
    const year = Number.parseInt(params.year)
    const month = Number.parseInt(params.month)

    if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
      console.error(`Invalid year or month parameter: year=${params.year}, month=${params.month}`)
      return NextResponse.json({ error: "Invalid year or month parameter" }, { status: 400 })
    }

    // 生成缓存键
    const cacheKey = `sankey-data-${year}-${month}`

    // 尝试从缓存获取数据
    const cachedData = await getFromCache(cacheKey)
    if (cachedData) {
      // 添加缓存标记到响应头
      const headers = new Headers()
      headers.set("X-Cache", "HIT")
      return NextResponse.json(cachedData, { headers })
    }

    console.log(`Cache miss for ${cacheKey}, fetching from database...`)

    const supabase = createClient()

    // 获取指定年月的数据
    const startDate = new Date(year, month - 1, 1).toISOString()
    const endDate = new Date(year, month, 0).toISOString() // 当月最后一天

    // 1. 获取有阅读记录的用户
    const { data: users, error: usersError } = await supabase
      .from("reading_records")
      .select(`
        user_id,
        users (
          id,
          user_id
        )
      `)
      .gte("created_at", startDate)
      .lte("created_at", endDate)
      .order("user_id")

    if (usersError) {
      console.error("Error fetching users:", usersError)
      return NextResponse.json({ error: usersError.message }, { status: 500 })
    }

    // 2. 获取用户阅读的书籍类别数据
    const { data: readingData, error: readingError } = await supabase
      .from("reading_records")
      .select(`
        id,
        users (
          id,
          user_id
        ),
        books (
          id,
          title,
          main_category_id,
          main_categories (
            id,
            name
          )
        )
      `)
      .gte("created_at", startDate)
      .lte("created_at", endDate)

    if (readingError) {
      console.error("Error fetching reading data:", readingError)
      return NextResponse.json({ error: readingError.message }, { status: 500 })
    }

    // 检查是否有数据
    if (!readingData || readingData.length === 0) {
      console.log(`No reading data found for ${year}-${month}`)
      return NextResponse.json(
        {
          nodes: [],
          links: [],
          meta: {
            year,
            month,
            generatedAt: new Date().toISOString(),
            message: "No data available for this period",
          },
        },
        { status: 200 },
      )
    }

    // 处理数据，构建桑基图所需的节点和链接
    const userMap = new Map()
    const categoryMap = new Map()
    const userCategoryLinks = new Map()

    // 用于存储用户ID到用户名的映射
    const userIdToName = new Map()

    // 用于存储类别ID到类别名称的映射
    const categoryIdToName = new Map()

    // 用于存储类别ID到颜色的映射
    const categoryColors = {
      文学类: "#FF6384",
      哲学类: "#36A2EB",
      社会科学类: "#FFCE56",
      历史类: "#4BC0C0",
      科学技术类: "#9966FF",
      其他: "#C9CBCF",
    }

    // 处理用户数据
    users?.forEach((record) => {
      if (record.users) {
        const userId = record.users.user_id
        userIdToName.set(record.users.id, userId)
      }
    })

    // 处理阅读记录数据
    readingData?.forEach((record) => {
      if (!record.users || !record.books || !record.books.main_categories) {
        console.log("Skipping record with missing data:", record.id)
        return
      }

      const userId = record.users.id
      const userName = userIdToName.get(userId) || `用户${userId}`
      const categoryId = record.books.main_category_id
      const categoryName = record.books.main_categories.name || "未分类"

      // 存储类别名称
      categoryIdToName.set(categoryId, categoryName)

      // 用户-类别链接的唯一键
      const linkKey = `${userId}-${categoryId}`

      // 更新用户-类别链接
      if (userCategoryLinks.has(linkKey)) {
        userCategoryLinks.set(linkKey, userCategoryLinks.get(linkKey) + 1)
      } else {
        userCategoryLinks.set(linkKey, 1)
      }

      // 更新用户统计
      if (userMap.has(userId)) {
        userMap.set(userId, userMap.get(userId) + 1)
      } else {
        userMap.set(userId, 1)
      }

      // 更新类别统计
      if (categoryMap.has(categoryId)) {
        categoryMap.set(categoryId, categoryMap.get(categoryId) + 1)
      } else {
        categoryMap.set(categoryId, 1)
      }
    })

    // 检查是否有有效的链接数据
    if (userCategoryLinks.size === 0) {
      console.log(`No valid links found for ${year}-${month}`)
      return NextResponse.json(
        {
          nodes: [],
          links: [],
          meta: {
            year,
            month,
            generatedAt: new Date().toISOString(),
            message: "No valid relationship data available for this period",
          },
        },
        { status: 200 },
      )
    }

    // 构建桑基图节点
    const nodes = []

    // 添加用户节点
    const userEntries = Array.from(userMap.entries())

    // 按阅读量排序用户
    userEntries.sort((a, b) => b[1] - a[1])

    // 取前5名用户，其余归为"其他用户"
    const topUsers = userEntries.slice(0, 5)
    const otherUsers = userEntries.slice(5)

    // 添加前5名用户节点
    topUsers.forEach(([userId, count]) => {
      const userName = userIdToName.get(userId) || `用户${userId}`
      nodes.push({ id: `user_${userId}`, name: userName })
    })

    // 如果有其他用户，添加"其他用户"节点
    if (otherUsers.length > 0) {
      nodes.push({ id: "user_其他用户", name: "其他用户" })
    }

    // 添加类别节点
    categoryMap.forEach((count, categoryId) => {
      const categoryName = categoryIdToName.get(categoryId) || "未分类"
      nodes.push({
        id: categoryName,
        nodeColor: categoryColors[categoryName] || "#C9CBCF",
      })
    })

    // 构建桑基图链接
    const links = []

    // 添加前5名用户的链接
    topUsers.forEach(([userId]) => {
      categoryMap.forEach((_, categoryId) => {
        const linkKey = `${userId}-${categoryId}`
        if (userCategoryLinks.has(linkKey)) {
          const value = userCategoryLinks.get(linkKey)
          const categoryName = categoryIdToName.get(categoryId) || "未分类"
          links.push({
            source: `user_${userId}`,
            target: categoryName,
            value,
          })
        }
      })
    })

    // 添加"其他用户"的链接
    if (otherUsers.length > 0) {
      const otherUserCategoryMap = new Map()

      otherUsers.forEach(([userId]) => {
        categoryMap.forEach((_, categoryId) => {
          const linkKey = `${userId}-${categoryId}`
          if (userCategoryLinks.has(linkKey)) {
            const categoryName = categoryIdToName.get(categoryId) || "未分类"
            const value = userCategoryLinks.get(linkKey)

            if (otherUserCategoryMap.has(categoryName)) {
              otherUserCategoryMap.set(categoryName, otherUserCategoryMap.get(categoryName) + value)
            } else {
              otherUserCategoryMap.set(categoryName, value)
            }
          }
        })
      })

      otherUserCategoryMap.forEach((value, categoryName) => {
        links.push({
          source: "user_其他用户",
          target: categoryName,
          value,
        })
      })
    }

    // 构建响应数据
    const responseData = {
      nodes: nodes,
      links: links,
      meta: {
        year: year,
        month: month,
        generatedAt: new Date().toISOString(),
        totalUsers: userMap.size,
        totalCategories: categoryMap.size,
        totalLinks: links.length,
      },
    }

    // 缓存数据
    await setCache(cacheKey, responseData, CACHE_TTL)

    // 添加缓存标记到响应头
    const headers = new Headers()
    headers.set("X-Cache", "MISS")

    console.log(`Successfully generated sankey data for ${year}-${month}: ${nodes.length} nodes, ${links.length} links`)

    // 返回桑基图数据
    return NextResponse.json(responseData, { headers })
  } catch (error) {
    console.error("Error processing sankey data:", error)
    return NextResponse.json(
      {
        error: "Failed to process sankey data",
        message: error instanceof Error ? error.message : "Unknown error",
        stack: process.env.NODE_ENV === "development" ? (error instanceof Error ? error.stack : undefined) : undefined,
      },
      { status: 500 },
    )
  }
}
