import { createClient } from "@/lib/supabase"
import { NextResponse } from "next/server"
import { getFromCache, setCache } from "@/lib/cache"

// 缓存时间设置
const CACHE_TTL = 10 * 60 * 1000 // 10分钟

export async function GET(request: Request, { params }: { params: { year: string; month: string } }) {
  console.log(`Sankey data API called for year=${params.year}, month=${params.month}`)

  try {
    const year = Number.parseInt(params.year)
    const month = Number.parseInt(params.month)

    if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
      console.error(`Invalid year or month parameter: year=${params.year}, month=${params.month}`)
      return NextResponse.json({ error: "Invalid year or month parameter" }, { status: 400 })
    }

    // 获取URL参数
    const url = new URL(request.url)
    const skipCache = url.searchParams.get("skipCache") === "true"
    const showAllUsers = url.searchParams.get("showAllUsers") === "true"
    const topUserCount = Number.parseInt(url.searchParams.get("topUsers") || "6") || 6

    // 生成缓存键
    const cacheKey = `sankey-data-${year}-${month}-${showAllUsers ? "all" : topUserCount}`

    // 尝试从缓存获取数据，除非明确要求跳过缓存
    if (!skipCache) {
      const cachedData = await getFromCache(cacheKey)
      if (cachedData) {
        // 添加缓存标记到响应头
        const headers = new Headers()
        headers.set("X-Cache", "HIT")
        return NextResponse.json(cachedData, { headers })
      }
    }

    console.log(`Cache miss for ${cacheKey}, fetching from database...`)

    const supabase = createClient()

    // 修复时间范围计算，确保准确地从当月1日00:00:00到当月最后一日23:59:59
    // 创建UTC日期对象，避免时区问题
    const startDate = new Date(Date.UTC(year, month - 1, 1, 0, 0, 0)).toISOString()

    // 获取当月最后一天
    const lastDay = new Date(Date.UTC(year, month, 0, 23, 59, 59)).toISOString()

    console.log(`查询时间范围: ${startDate} 到 ${lastDay}`)

    // 1. 获取有阅读记录的用户
    const { data: users, error: usersError } = await supabase
      .from("reading_records")
      .select(`
        user_id,
        users (
          id,
          user_id
        )
      `)
      .gte("created_at", startDate)
      .lte("created_at", lastDay)
      .order("user_id")

    if (usersError) {
      console.error("Error fetching users:", usersError)
      return NextResponse.json({ error: usersError.message }, { status: 500 })
    }

    console.log(`Retrieved ${users?.length || 0} users with reading records`)

    // 2. 获取用户阅读的书籍类别数据
    const { data: readingData, error: readingError } = await supabase
      .from("reading_records")
      .select(`
        id,
        users (
          id,
          user_id
        ),
        books (
          id,
          title,
          main_category_id,
          main_categories (
            id,
            name
          )
        )
      `)
      .gte("created_at", startDate)
      .lte("created_at", lastDay)

    if (readingError) {
      console.error("Error fetching reading data:", readingError)
      return NextResponse.json({ error: readingError.message }, { status: 500 })
    }

    console.log(`Retrieved ${readingData?.length || 0} reading records`)

    // 检查是否有数据
    if (!readingData || readingData.length === 0) {
      console.log(`No reading data found for ${year}-${month}`)
      return NextResponse.json(
        {
          nodes: [],
          links: [],
          meta: {
            year,
            month,
            generatedAt: new Date().toISOString(),
            message: "No data available for this period",
          },
        },
        { status: 200 },
      )
    }

    // 处理数据，构建桑基图所需的节点和链接
    const userMap = new Map()
    const categoryMap = new Map()
    const userCategoryLinks = new Map()

    // 用于存储用户ID到用户名的映射
    const userIdToName = new Map()

    // 用于存储类别ID到类别名称的映射
    const categoryIdToName = new Map()

    // 用于存储类别名称到ID的映射（用于反向查找）
    const categoryNameToId = new Map()

    // 用于存储类别ID到颜色的映射
    const categoryColors = {
      文学: "#FF6384",
      宗教与哲学: "#36A2EB",
      社会科学: "#FFCE56",
      历史: "#4BC0C0",
      个人发展: "#36A2EB",
      其他: "#C9CBCF",
    }

    // 处理用户数据
    users?.forEach((record) => {
      if (record.users) {
        const userId = record.users.user_id
        userIdToName.set(record.users.id, userId)
      }
    })

    // 处理阅读记录数据
    readingData?.forEach((record) => {
      if (!record.users || !record.books || !record.books.main_categories) {
        console.log("Skipping record with missing data:", record.id)
        return
      }

      const userId = record.users.id
      const userName = userIdToName.get(userId) || `用户${userId}`
      const categoryId = record.books.main_category_id
      const categoryName = record.books.main_categories.name || "未分类"

      // 存储类别名称和ID的映射
      categoryIdToName.set(categoryId, categoryName)
      categoryNameToId.set(categoryName, categoryId)

      // 用户-类别链接的唯一键
      const linkKey = `${userId}-${categoryId}`

      // 更新用户-类别链接
      if (userCategoryLinks.has(linkKey)) {
        userCategoryLinks.set(linkKey, userCategoryLinks.get(linkKey) + 1)
      } else {
        userCategoryLinks.set(linkKey, 1)
      }

      // 更新用户统计
      if (userMap.has(userId)) {
        userMap.set(userId, userMap.get(userId) + 1)
      } else {
        userMap.set(userId, 1)
      }

      // 更新类别统计 - 使用类别名称作为键，与图书类别用户分布保持一致
      if (categoryMap.has(categoryName)) {
        categoryMap.set(categoryName, categoryMap.get(categoryName) + 1)
      } else {
        categoryMap.set(categoryName, 1)
      }
    })

    // 检查是否有有效的链接数据
    if (userCategoryLinks.size === 0) {
      console.log(`No valid links found for ${year}-${month}`)
      return NextResponse.json(
        {
          nodes: [],
          links: [],
          meta: {
            year,
            month,
            generatedAt: new Date().toISOString(),
            message: "No valid relationship data available for this period",
          },
        },
        { status: 200 },
      )
    }

    // 构建桑基图节点
    const nodes = []

    // 添加用户节点
    const userEntries = Array.from(userMap.entries())

    // 按阅读量排序用户
    userEntries.sort((a, b) => b[1] - a[1])

    // 记录用户数据，用于调试
    console.log(`Total users with reading records: ${userEntries.length}`)
    userEntries.forEach(([userId, count]) => {
      const userName = userIdToName.get(userId) || `用户${userId}`
      console.log(`User: ${userName}, Books: ${count}`)
    })

    // 获取阅读量最多的前N名用户（默认为6名）
    const topUserEntries = userEntries.slice(0, topUserCount)

    console.log(`Selected top ${topUserEntries.length} users:`)
    topUserEntries.forEach(([userId, count]) => {
      const userName = userIdToName.get(userId) || `用户${userId}`
      console.log(`Top User: ${userName}, Books: ${count}`)
    })

    // 获取所有类别，而不是只使用特定类别
    const allCategories = Array.from(categoryMap.keys())

    // 按照书籍数量排序类别
    const sortedCategories = allCategories.sort((a, b) => {
      return categoryMap.get(b) - categoryMap.get(a)
    })

    // 选择前5个最热门的类别
    const topCategories = sortedCategories.slice(0, 5)

    console.log(`Top 5 categories: ${topCategories.join(", ")}`)

    // 记录类别数据，用于调试
    console.log(`Total categories: ${categoryMap.size}`)
    categoryMap.forEach((count, categoryName) => {
      console.log(`Category: ${categoryName}, Books: ${count}`)
    })

    // 添加前N名用户节点
    topUserEntries.forEach(([userId, count]) => {
      const userName = userIdToName.get(userId) || `用户${userId}`
      nodes.push({
        id: `user_${userId}`,
        name: userName,
        value: count, // 添加总阅读量作为节点值
      })
    })

    // 过滤类别，只包括前5个最热门的类别
    const filteredCategoryEntries = Array.from(categoryMap.entries()).filter(([categoryName]) => {
      return topCategories.includes(categoryName)
    })

    console.log(`Filtered categories: ${filteredCategoryEntries.length}`)
    filteredCategoryEntries.forEach(([categoryName, count]) => {
      console.log(`Filtered Category: ${categoryName}, Books: ${count}`)
    })

    // 添加过滤后的类别节点
    filteredCategoryEntries.forEach(([categoryName, count]) => {
      nodes.push({
        id: categoryName,
        name: categoryName,
        nodeColor: categoryColors[categoryName] || "#C9CBCF",
        value: count, // 添加总书籍数作为节点值
      })
    })

    // 构建桑基图链接
    const links = []

    // 清除现有链接数组（如果存在）
    links.length = 0

    // 只添加前N名用户和前5个类别之间的链接
    topUserEntries.forEach(([userId]) => {
      filteredCategoryEntries.forEach(([categoryName]) => {
        const categoryId = categoryNameToId.get(categoryName)
        if (categoryId) {
          const linkKey = `${userId}-${categoryId}`
          if (userCategoryLinks.has(linkKey)) {
            const value = userCategoryLinks.get(linkKey)
            links.push({
              source: `user_${userId}`,
              target: categoryName,
              value,
            })
          }
        }
      })
    })

    console.log(`Generated links: ${links.length}`)
    links.forEach((link) => {
      console.log(`Link: ${link.source} -> ${link.target}, Value: ${link.value}`)
    })

    // 构建响应数据
    const responseData = {
      nodes: nodes,
      links: links,
      meta: {
        year: year,
        month: month,
        generatedAt: new Date().toISOString(),
        totalUsers: userMap.size,
        totalCategories: categoryMap.size,
        totalLinks: links.length,
        userCounts: Object.fromEntries(
          Array.from(userMap.entries()).map(([userId, count]) => [userIdToName.get(userId) || `用户${userId}`, count]),
        ),
      },
    }

    console.log(
      `Generated sankey data: ${nodes.length} nodes, ${links.length} links, ${userMap.size} users, ${categoryMap.size} categories`,
    )

    // 缓存数据
    await setCache(cacheKey, responseData, CACHE_TTL)

    // 添加缓存标记到响应头
    const headers = new Headers()
    headers.set("X-Cache", "MISS")

    console.log(`Successfully generated sankey data for ${year}-${month}: ${nodes.length} nodes, ${links.length} links`)

    // 返回桑基图数据
    return NextResponse.json(responseData, { headers })
  } catch (error) {
    console.error("Error processing sankey data:", error)
    return NextResponse.json(
      {
        error: "Failed to process sankey data",
        message: error instanceof Error ? error.message : "Unknown error",
        stack: process.env.NODE_ENV === "development" ? (error instanceof Error ? error.stack : undefined) : undefined,
      },
      { status: 500 },
    )
  }
}
