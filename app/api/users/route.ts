import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { NextResponse } from "next/server"

export const dynamic = "force-dynamic"

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // 获取所有用户
    const { data: usersData, error: usersError } = await supabase
      .from("users")
      .select("id, user_id")
      .order("user_id", { ascending: true })

    if (usersError) {
      console.error("Error fetching users:", usersError)
      return NextResponse.json({ error: usersError.message }, { status: 500 })
    }

    return NextResponse.json(usersData)
  } catch (error) {
    console.error("Unexpected error in users API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
