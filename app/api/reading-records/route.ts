import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { NextResponse } from "next/server"

// 设置为动态路由，确保每次请求都获取最新数据
export const dynamic = "force-dynamic"
export const revalidate = 0

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const searchTerm = searchParams.get("searchTerm") || ""
    const year = searchParams.get("year")
    const month = searchParams.get("month")

    // 添加时间戳参数，防止浏览器缓存
    const timestamp = Date.now()
    console.log(`API Request [${timestamp}] - Year: ${year}, Month: ${month}, SearchTerm: ${searchTerm}`)

    // 创建新的Supabase客户端实例，避免使用缓存的连接
    const supabase = createRouteHandlerClient({ cookies })

    // 构建基本查询
    let query = supabase.from("reading_records").select(`
        id,
        reading_status,
        progress,
        created_at,
        books (
          title,
          main_categories (
            name
          ),
          sub_categories (
            name
          )
        ),
        users (
          user_id
        )
      `)

    // 添加时间筛选
    if (year && month) {
      const startDate = new Date(`${year}-${month.padStart(2, "0")}-01`)
      const endDate = new Date(startDate)
      endDate.setMonth(endDate.getMonth() + 1)

      console.log(`Filtering by date range: ${startDate.toISOString()} to ${endDate.toISOString()}`)

      query = query.gte("created_at", startDate.toISOString())
      query = query.lt("created_at", endDate.toISOString())
    } else if (year) {
      const startDate = new Date(`${year}-01-01`)
      const endDate = new Date(`${Number.parseInt(year) + 1}-01-01`)

      console.log(`Filtering by year range: ${startDate.toISOString()} to ${endDate.toISOString()}`)

      query = query.gte("created_at", startDate.toISOString())
      query = query.lt("created_at", endDate.toISOString())
    }

    // 添加搜索条件
    if (searchTerm) {
      // 获取匹配书名的书籍ID
      const { data: bookIds, error: bookError } = await supabase
        .from("books")
        .select("id")
        .ilike("title", `%${searchTerm}%`)

      if (bookError) {
        console.error("Error searching books:", bookError)
        return NextResponse.json({ error: bookError.message }, { status: 500 })
      }

      // 获取匹配用户ID的用户ID
      const { data: userIds, error: userError } = await supabase
        .from("users")
        .select("id")
        .ilike("user_id", `%${searchTerm}%`)

      if (userError) {
        console.error("Error searching users:", userError)
        return NextResponse.json({ error: userError.message }, { status: 500 })
      }

      // 获取匹配分类名称的分类ID
      const { data: categoryIds, error: categoryError } = await supabase
        .from("main_categories")
        .select("id")
        .ilike("name", `%${searchTerm}%`)

      if (categoryError) {
        console.error("Error searching categories:", categoryError)
        return NextResponse.json({ error: categoryError.message }, { status: 500 })
      }

      // 构建OR条件
      const orConditions = []

      if (bookIds && bookIds.length > 0) {
        orConditions.push(`book_id.in.(${bookIds.map((b) => b.id).join(",")})`)
      }

      if (userIds && userIds.length > 0) {
        orConditions.push(`user_id.in.(${userIds.map((u) => u.id).join(",")})`)
      }

      if (categoryIds && categoryIds.length > 0) {
        // 获取带有这些分类的书籍
        const { data: categoryBookIds, error: catBookError } = await supabase
          .from("books")
          .select("id")
          .in(
            "main_category_id",
            categoryIds.map((c) => c.id),
          )

        if (!catBookError && categoryBookIds && categoryBookIds.length > 0) {
          orConditions.push(`book_id.in.(${categoryBookIds.map((b) => b.id).join(",")})`)
        }
      }

      // 应用OR条件
      if (orConditions.length > 0) {
        query = query.or(orConditions.join(","))
      } else if (searchTerm) {
        // 如果没有找到任何匹配，但有搜索词，返回空结果
        return NextResponse.json([])
      }
    }

    // 执行查询，禁用缓存
    const { data, error } = await query.order("created_at", { ascending: false })

    if (error) {
      console.error("Error fetching reading records:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // 添加调试日志
    console.log(`API Response [${timestamp}] - Records count: ${data?.length || 0}`)

    // 设置响应头，防止缓存
    return NextResponse.json(data, {
      headers: {
        "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
        Pragma: "no-cache",
        Expires: "0",
        "Surrogate-Control": "no-store",
      },
    })
  } catch (error) {
    console.error("Unexpected error in reading records API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
