import { UserCategorySankey } from "@/components/charts/user-category-sankey"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

interface SankeyPageProps {
  params: {
    year: string
    month: string
  }
}

export default function SankeyPage({ params }: SankeyPageProps) {
  const year = Number.parseInt(params.year)
  const month = Number.parseInt(params.month)

  return (
    <div className="container mx-auto p-4 md:p-8">
      <div className="mb-6 flex items-center">
        <Link href="/" className="mr-4">
          <Button variant="outline" size="sm" className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            返回首页
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">
          {year}年{month}月 用户与类别关系图
        </h1>
      </div>

      <div className="h-[calc(100vh-150px)] min-h-[600px]">
        <UserCategorySankey
          year={year}
          month={month}
          title={`${year}年${month}月 用户与类别关系图`}
          subtitle="用户阅读不同类别书籍的流量分布（大屏展示）"
        />
      </div>
    </div>
  )
}
