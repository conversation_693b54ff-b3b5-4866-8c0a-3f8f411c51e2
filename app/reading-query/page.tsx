"use client"

import { useState, useEffect, useCallback } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Filter, ArrowLeft, SlidersHorizontal } from "lucide-react"
import Link from "next/link"
import { SimpleSearchInput } from "@/components/simple-search-input"

interface ReadingRecord {
  id: string
  books?: {
    title?: string
    main_categories?: { name?: string }
    sub_categories?: { name?: string }
  }
  users?: { user_id?: string }
  reading_status?: string
  progress?: string
  created_at?: string
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}

const getStatusColor = (status: string | undefined): string => {
  switch (status) {
    case "读完":
      return "bg-green-100 text-green-800"
    case "在读":
      return "bg-blue-100 text-blue-800"
    case "计划读":
      return "bg-amber-100 text-amber-800"
    default:
      return "bg-amber-100 text-amber-800"
  }
}

// 获取上个月的月份和年份
const getPreviousMonth = (): { year: string; month: string } => {
  const today = new Date()
  // 创建上个月的日期
  const prevMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
  return {
    year: prevMonth.getFullYear().toString(),
    month: (prevMonth.getMonth() + 1).toString(),
  }
}

const ReadingQueryPage = () => {
  const prevMonth = getPreviousMonth()
  const [records, setRecords] = useState<ReadingRecord[]>([])
  const [allUsers, setAllUsers] = useState<string[]>([])
  const [allBooks, setAllBooks] = useState<string[]>([])
  const [allCategories, setAllCategories] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [timeFilter, setTimeFilter] = useState("all")
  const [selectedYear, setSelectedYear] = useState<string>(prevMonth.year)
  const [selectedMonth, setSelectedMonth] = useState<string>(prevMonth.month)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // 获取所有用户列表和书籍列表
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        // 获取用户列表
        const usersResponse = await fetch("/api/users")
        if (!usersResponse.ok) {
          throw new Error(`HTTP error! status: ${usersResponse.status}`)
        }
        const usersData = await usersResponse.json()
        setAllUsers(usersData.map((user: any) => user.user_id).filter(Boolean))

        // 获取书籍列表
        const booksResponse = await fetch("/api/books")
        if (booksResponse.ok) {
          const booksData = await booksResponse.json()
          setAllBooks(booksData.map((book: any) => book.title).filter(Boolean))
        }

        // 获取分类列表
        const categoriesResponse = await fetch("/api/categories")
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json()
          setAllCategories(categoriesData.map((category: any) => category.name).filter(Boolean))
        }
      } catch (error) {
        console.error("Failed to fetch data:", error)
      }
    }

    fetchAllData()
  }, [])

  // 处理时间筛选变化
  const handleTimeFilterChange = (value: string) => {
    setTimeFilter(value)

    // 如果切换到按月份筛选，确保使用上个月作为默认值
    if (value === "month") {
      const prevMonth = getPreviousMonth()
      setSelectedYear(prevMonth.year)
      setSelectedMonth(prevMonth.month)
    }
  }

  // 获取记录的函数 - 使用useCallback确保函数引用稳定
  const fetchReadingRecords = useCallback(
    async (overrideSearchQuery?: string) => {
      setIsLoading(true)
      setError(null)

      try {
        // 构建查询参数
        const params = new URLSearchParams()

        // 使用覆盖的搜索查询（如果提供）或当前的搜索查询
        const queryToUse = overrideSearchQuery !== undefined ? overrideSearchQuery : searchQuery
        if (queryToUse) params.append("searchTerm", queryToUse)

        if (timeFilter === "year") {
          params.append("year", selectedYear)
        } else if (timeFilter === "month") {
          params.append("year", selectedYear)
          params.append("month", selectedMonth)
        }

        // 发送请求
        const response = await fetch(`/api/reading-records?${params.toString()}`)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        setRecords(data)
      } catch (error) {
        console.error("Failed to fetch reading records:", error)
        setError("加载阅读记录失败，请检查网络连接或稍后重试。")
        toast({
          title: "加载阅读记录失败",
          description: "请检查网络连接或稍后重试。",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    },
    [searchQuery, timeFilter, selectedYear, selectedMonth, toast],
  )

  // 当筛选条件变化时重新获取数据
  useEffect(() => {
    fetchReadingRecords()
  }, [timeFilter, selectedYear, selectedMonth, fetchReadingRecords])

  // 重置筛选条件
  const resetFilters = () => {
    setSearchQuery("")
    setTimeFilter("all")
    const prevMonth = getPreviousMonth()
    setSelectedYear(prevMonth.year)
    setSelectedMonth(prevMonth.month)
    // 使用空字符串覆盖搜索查询，确保获取未筛选的数据
    fetchReadingRecords("")
  }

  // 清空搜索并重新获取数据
  const handleClearSearch = () => {
    // 先设置搜索查询为空
    setSearchQuery("")
    // 然后使用空字符串作为覆盖参数调用fetchReadingRecords
    fetchReadingRecords("")
  }

  // 处理搜索输入变化
  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
    // 如果清空搜索，立即重新获取数据
    if (!value) {
      fetchReadingRecords("")
    } else {
      // 当输入内容时，延迟300ms后自动搜索
      const timer = setTimeout(() => {
        fetchReadingRecords()
      }, 300)
      return () => clearTimeout(timer)
    }
  }

  // 获取所有可能的建议
  const getAllSuggestions = () => {
    // 合并所有建议并去重
    return [...new Set([...allUsers, ...allBooks, ...allCategories])]
  }

  return (
    <div className="container mx-auto py-4 px-4 sm:px-6 sm:py-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" asChild className="h-9 w-9 sm:h-9 sm:w-auto p-0 sm:p-2">
            <Link href="/">
              <ArrowLeft className="h-4 w-4 sm:mr-1" />
              <span className="hidden sm:inline">返回首页</span>
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold">阅读数据查询</h1>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={resetFilters} className="flex-1 sm:flex-auto">
            <SlidersHorizontal className="h-4 w-4 mr-1" />
            重置筛选
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardContent className="pt-4 sm:pt-6">
          <div className="flex items-center gap-2 mb-4">
            <Filter className="h-5 w-5" />
            <h2 className="text-lg font-medium">筛选条件</h2>
          </div>

          <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-calendar"
                >
                  <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                  <line x1="16" x2="16" y1="2" y2="6" />
                  <line x1="8" x2="8" y1="2" y2="6" />
                  <line x1="3" x2="21" y1="10" y2="10" />
                </svg>
                <span className="text-sm font-medium">时间筛选</span>
              </div>
              <Tabs defaultValue="all" value={timeFilter} onValueChange={handleTimeFilterChange} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="all">全部</TabsTrigger>
                  <TabsTrigger value="year">按年份</TabsTrigger>
                  <TabsTrigger value="month">按月份</TabsTrigger>
                </TabsList>
                <TabsContent value="year" className="mt-2">
                  <Select value={selectedYear} onValueChange={setSelectedYear}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择年份" />
                    </SelectTrigger>
                    <SelectContent>
                      {["2025", "2024", "2023"].map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}年
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </TabsContent>
                <TabsContent value="month" className="mt-2 space-y-2">
                  <Select value={selectedYear} onValueChange={setSelectedYear}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择年份" />
                    </SelectTrigger>
                    <SelectContent>
                      {["2025", "2024", "2023"].map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}年
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择月份" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => (i + 1).toString()).map((month) => (
                        <SelectItem key={month} value={month}>
                          {month}月
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </TabsContent>
              </Tabs>
            </div>

            <div>
              <div className="flex items-center gap-2 mb-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-search"
                >
                  <circle cx="11" cy="11" r="8" />
                  <path d="m21 21-4.3-4.3" />
                </svg>
                <span className="text-sm font-medium">关键词搜索</span>
              </div>
              <SimpleSearchInput
                placeholder="搜索书名、分类或用户..."
                value={searchQuery}
                onChange={handleSearchChange}
                onClear={handleClearSearch}
                suggestions={getAllSuggestions()}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">{error}</div>}

      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-4">
        <div className="text-sm text-gray-500">找到 {records.length} 条阅读记录</div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">加载数据中...</span>
        </div>
      ) : (
        <div className="mb-4">
          <div className="text-xs text-muted-foreground mb-2 flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-1"
            >
              <path d="M17 13h-5v5h5v-5Z" />
              <path d="M12 2H2v10h10V2Z" />
              <path d="M22 12h-5v10h5V12Z" />
              <path d="M12 12h-5v10h5V12Z" />
            </svg>
            <span className="sm:hidden">左右滑动查看更多内容</span>
          </div>
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto w-full" style={{ WebkitOverflowScrolling: "touch" }}>
              <div className="min-w-[800px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[200px]">书名</TableHead>
                      <TableHead className="w-[150px]">分类</TableHead>
                      <TableHead className="w-[120px]">用户</TableHead>
                      <TableHead className="w-[100px]">状态</TableHead>
                      <TableHead className="w-[80px]">进度</TableHead>
                      <TableHead className="w-[150px]">添加时间</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {records.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                          没有找到符合条件的阅读记录
                        </TableCell>
                      </TableRow>
                    ) : (
                      records.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell className="font-medium">{record.books?.title || "未知书籍"}</TableCell>
                          <TableCell>{record.books?.main_categories?.name || "未分类"}</TableCell>
                          <TableCell>{record.users?.user_id || "未知用户"}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(record.reading_status)}`}>
                              {record.reading_status || "未知"}
                            </span>
                          </TableCell>
                          <TableCell>{record.progress || "0%"}</TableCell>
                          <TableCell>{record.created_at ? formatDate(record.created_at) : "未知"}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ReadingQueryPage
