"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Head<PERSON> } from "@/components/header"
import { Users, BookOpen, BookMarked, TrendingUp } from "lucide-react"
import { getSystemConfig } from "@/lib/config"
import { MonthlyDataPanel } from "@/components/monthly-data-panel"
import { SimpleRelationshipChart } from "@/components/charts/simple-relationship-chart"

// Mock data for when Supabase connection is unavailable
const MOCK_DATA = {
  overallStats: {
    totalParticipants: 34,
    totalBooks: 127,
    totalActiveReaders: 17,
    totalCompletedBooks: 39,
    totalGroupMembers: 182,
  },
  yearlyStats: {
    year: new Date().getFullYear(),
    participants: 34,
    books: 127,
    activeReaders: 17,
    completedBooks: 39,
  },
  monthlyData: [],
  categoryData: [],
}

export default function DataAnalysisPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [monthlyData, setMonthlyData] = useState<any[]>([])
  const [categoryData, setCategoryData] = useState<any[]>([])
  const [overallStats, setOverallStats] = useState({
    totalParticipants: 0,
    totalBooks: 0,
    totalActiveReaders: 0,
    totalCompletedBooks: 0,
    totalGroupMembers: 0,
  })

  const [yearlyStats, setYearlyStats] = useState({
    year: new Date().getFullYear(),
    participants: 0,
    books: 0,
    activeReaders: 0,
    completedBooks: 0,
  })

  useEffect(() => {
    const fetchData = async () => {
      try {
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

        // Check if Supabase environment variables are available
        if (!supabaseUrl || !supabaseKey) {
          console.warn("Supabase environment variables not set, using mock data")

          // Use mock data when environment variables are not available
          setOverallStats(MOCK_DATA.overallStats)
          setYearlyStats(MOCK_DATA.yearlyStats)
          setMonthlyData(MOCK_DATA.monthlyData)
          setCategoryData(MOCK_DATA.categoryData)

          // Set a more user-friendly error message
          setError("预览环境中无法连接到数据库。这是正常的，实际部署时将正常工作。")
          setIsLoading(false)
          return
        }

        // If environment variables are available, proceed with normal data fetching
        const { createClient } = await import("@supabase/supabase-js")
        const supabase = createClient(supabaseUrl, supabaseKey)

        // Get total group members
        let totalGroupMembers = 0
        try {
          const totalGroupMembersStr = await getSystemConfig("total_group_members")
          totalGroupMembers = totalGroupMembersStr ? Number.parseInt(totalGroupMembersStr) : 0
        } catch (configError) {
          console.warn("Failed to get total_group_members config:", configError)
        }

        // Get current year
        const currentYear = new Date().getFullYear()

        // Get monthly stats from all years
        const { data: allMonthlyStats, error: monthlyStatsError } = await supabase
          .from("monthly_stats")
          .select("*")
          .order("year", { ascending: false })
          .order("month", { ascending: false })

        if (monthlyStatsError) {
          throw new Error(`获取月度统计数据失败: ${monthlyStatsError.message}`)
        }

        // Calculate overall stats
        let maxTotalParticipants = 0
        let maxTotalBooks = 0
        let maxTotalActiveReaders = 0
        let maxTotalCompletedBooks = 0

        // Calculate yearly stats
        let yearParticipants = 0
        let yearBooks = 0
        let yearActiveReaders = 0
        let yearCompletedBooks = 0

        if (allMonthlyStats && allMonthlyStats.length > 0) {
          // Calculate overall stats - max values from all years
          maxTotalParticipants = Math.max(...allMonthlyStats.map((item) => item.total_participants || 0))
          maxTotalBooks = Math.max(...allMonthlyStats.map((item) => item.total_books || 0))
          maxTotalActiveReaders = Math.max(...allMonthlyStats.map((item) => item.active_readers || 0))
          maxTotalCompletedBooks = Math.max(...allMonthlyStats.map((item) => item.completed_books || 0))

          // Filter current year stats
          const currentYearStats = allMonthlyStats.filter((item) => item.year === currentYear)

          if (currentYearStats.length > 0) {
            // Get max values for current year
            yearParticipants = Math.max(...currentYearStats.map((item) => item.total_participants || 0))
            yearBooks = Math.max(...currentYearStats.map((item) => item.total_books || 0))
            yearActiveReaders = Math.max(...currentYearStats.map((item) => item.active_readers || 0))
            yearCompletedBooks = Math.max(...currentYearStats.map((item) => item.completed_books || 0))
          }
        }

        // Ensure overall stats are at least as large as yearly stats
        const finalTotalParticipants = Math.max(maxTotalParticipants, yearParticipants)

        setOverallStats({
          totalParticipants: finalTotalParticipants,
          totalBooks: Math.max(maxTotalBooks, yearBooks),
          totalActiveReaders: Math.max(maxTotalActiveReaders, yearActiveReaders),
          totalCompletedBooks: Math.max(maxTotalCompletedBooks, yearCompletedBooks),
          totalGroupMembers,
        })

        setYearlyStats({
          year: currentYear,
          participants: yearParticipants,
          books: yearBooks,
          activeReaders: yearActiveReaders,
          completedBooks: yearCompletedBooks,
        })

        // Get monthly data
        const { data: monthlyStats, error: monthlyError } = await supabase
          .from("monthly_stats")
          .select("*")
          .order("year", { ascending: false })
          .order("month", { ascending: true })

        if (monthlyError) throw new Error(`获取月度数据失败: ${monthlyError.message}`)
        setMonthlyData(monthlyStats || [])

        // Get category data
        const { data: mainCategories, error: categoriesError } = await supabase.from("main_categories").select(`
            id,
            name,
            books (
              id
            )
          `)

        if (categoriesError) throw new Error(`获取分类数据失败: ${categoriesError.message}`)
        setCategoryData(mainCategories || [])
      } catch (err) {
        console.error("Error fetching data:", err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6">数据概览</h1>

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        ) : error ? (
          <div className="bg-amber-50 text-amber-700 p-4 rounded-md mb-6">
            <p>{error}</p>
            <p className="mt-2 text-sm">以下显示的是模拟数据，仅供界面预览。</p>
          </div>
        ) : null}

        <Tabs defaultValue="trends">
          <TabsList className="mb-6">
            <TabsTrigger value="trends">阅读趋势</TabsTrigger>
            <TabsTrigger value="yearly">年度数据</TabsTrigger>
            <TabsTrigger value="monthly">月度数据</TabsTrigger>
            <TabsTrigger value="categories">书籍分类</TabsTrigger>
            <TabsTrigger value="users">用户分析</TabsTrigger>
          </TabsList>

          <TabsContent value="trends">
            <div className="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>月度阅读完成情况</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center bg-gray-50 rounded-md">
                    <p className="text-muted-foreground">图表开发中...</p>
                  </div>
                </CardContent>
              </Card>

              <SimpleRelationshipChart
                title="用户与类别关系流图"
                subtitle="展示用户阅读不同类别书籍的流量分布"
                year={new Date().getFullYear()}
                month={new Date().getMonth() + 1}
              />
            </div>
          </TabsContent>

          <TabsContent value="yearly">
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold">年度数据</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* 年度统计卡片 */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium">{yearlyStats.year}年参与人数</p>
                        <h3 className="text-3xl font-bold mt-2">{yearlyStats.participants}</h3>
                        <p className="text-xs text-muted-foreground mt-1">
                          {yearlyStats.participants}/{overallStats.totalGroupMembers} 群成员参与
                        </p>
                      </div>
                      <Users className="h-5 w-5 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium">{yearlyStats.year}年书籍数量</p>
                        <h3 className="text-3xl font-bold mt-2">{yearlyStats.books}</h3>
                        <p className="text-xs text-muted-foreground mt-1">{yearlyStats.year}年度的书籍总数</p>
                      </div>
                      <BookOpen className="h-5 w-5 text-green-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium">{yearlyStats.year}年活跃读者</p>
                        <h3 className="text-3xl font-bold mt-2">{yearlyStats.activeReaders}</h3>
                        <p className="text-xs text-muted-foreground mt-1">{yearlyStats.year}年度的月度活跃读者累计</p>
                      </div>
                      <BookMarked className="h-5 w-5 text-amber-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium">{yearlyStats.year}年完成阅读</p>
                        <h3 className="text-3xl font-bold mt-2">{yearlyStats.completedBooks}</h3>
                        <p className="text-xs text-muted-foreground mt-1">{yearlyStats.year}年度已完成的书籍数量</p>
                      </div>
                      <TrendingUp className="h-5 w-5 text-purple-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>年度阅读趋势</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center bg-gray-50 rounded-md">
                    <p className="text-muted-foreground">图表开发中...</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="monthly">
            <MonthlyDataPanel year={yearlyStats.year} totalGroupMembers={overallStats.totalGroupMembers} />
          </TabsContent>

          <TabsContent value="categories">
            <div className="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>书籍分类统计</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center bg-gray-50 rounded-md">
                    <p className="text-muted-foreground">图表开发中...</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>热门分类排行</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center bg-gray-50 rounded-md">
                    <p className="text-muted-foreground">图表开发中...</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="users">
            <div className="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>用户活跃度分析</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center bg-gray-50 rounded-md">
                    <p className="text-muted-foreground">图表开发中...</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>阅读完成率分析</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center bg-gray-50 rounded-md">
                    <p className="text-muted-foreground">图表开发中...</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
