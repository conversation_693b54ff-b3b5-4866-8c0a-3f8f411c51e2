"use client"

import { useState, useEffect } from "react"
import { SimpleRelationshipChart } from "@/components/charts/simple-relationship-chart"
import { X, ZoomIn, ZoomOut, RotateCcw, Download, Info } from "lucide-react"
import Link from "next/link"
import { Toaster } from "@/components/ui/toaster"
import { But<PERSON> } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

// 月份名称
const monthNames = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]

export default function FullscreenSankeyPage({
  params,
}: {
  params: { year: string; month: string }
}) {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [scale, setScale] = useState(1)
  const [isInfoOpen, setIsInfoOpen] = useState(false)
  const year = Number.parseInt(params.year)
  const month = Number.parseInt(params.month)
  const monthName = monthNames[month - 1]

  // 在组件挂载时请求全屏
  useEffect(() => {
    const requestFullscreen = async () => {
      try {
        if (document.documentElement.requestFullscreen) {
          await document.documentElement.requestFullscreen()
          setIsFullscreen(true)
        }
      } catch (err) {
        console.error("无法进入全屏模式:", err)
      }
    }

    // 添加全屏变化事件监听器
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener("fullscreenchange", handleFullscreenChange)
    requestFullscreen()

    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange)
      // 退出全屏
      if (document.fullscreenElement && document.exitFullscreen) {
        document.exitFullscreen().catch((err) => {
          console.error("退出全屏时出错:", err)
        })
      }
    }
  }, [])

  // 处理缩放
  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.1, 1.5))
  }

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.1, 0.5))
  }

  const handleResetZoom = () => {
    setScale(1)
  }

  // 处理下载图表
  const handleDownload = () => {
    const svgElement = document.querySelector("#relationship-chart-container svg")
    if (!svgElement) return

    try {
      // 创建一个新的SVG元素，包含完整的样式
      const svgData = new XMLSerializer().serializeToString(svgElement)
      const svgBlob = new Blob([svgData], { type: "image/svg+xml;charset=utf-8" })
      const svgUrl = URL.createObjectURL(svgBlob)

      // 创建下载链接
      const downloadLink = document.createElement("a")
      downloadLink.href = svgUrl
      downloadLink.download = `关系图-${year}年${monthName}.svg`
      document.body.appendChild(downloadLink)
      downloadLink.click()
      document.body.removeChild(downloadLink)
      URL.revokeObjectURL(svgUrl)
    } catch (error) {
      console.error("下载图表失败:", error)
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <Toaster />

      {/* 顶部工具栏 */}
      <div className="fixed top-0 left-0 right-0 bg-white border-b z-10 px-4 py-2 flex justify-between items-center">
        <h1 className="text-xl font-bold flex items-center">
          {year}年{monthName} - 用户与类别关系流图
        </h1>
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleZoomIn}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>放大</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleZoomOut}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>缩小</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleResetZoom}>
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>重置缩放</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleDownload}>
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>下载图表</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Dialog open={isInfoOpen} onOpenChange={setIsInfoOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon">
                <Info className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>关于此图表</DialogTitle>
                <DialogDescription>
                  此图表展示了{year}年{monthName}用户与图书类别之间的关系。
                </DialogDescription>
              </DialogHeader>
              <div className="py-4">
                <h4 className="font-medium mb-2">使用说明:</h4>
                <ul className="list-disc pl-5 space-y-1 text-sm">
                  <li>将鼠标悬停在用户或类别上可以高亮显示相关连接</li>
                  <li>使用顶部工具栏的放大/缩小按钮调整视图</li>
                  <li>点击下载按钮可以保存图表为SVG格式</li>
                </ul>
              </div>
            </DialogContent>
          </Dialog>

          <Link href={`/${year}/${month}`}>
            <Button variant="outline" className="flex items-center gap-1">
              <X className="h-4 w-4" />
              <span>关闭全屏</span>
            </Button>
          </Link>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="pt-14 pb-4 px-4 h-screen flex items-center justify-center">
        <div
          className="w-full h-full transition-transform duration-200 ease-in-out"
          style={{ transform: `scale(${scale})` }}
        >
          <SimpleRelationshipChart
            title="用户与类别关系图 (全屏视图)"
            subtitle={`${year}年${monthName} - 展示用户阅读不同类别书籍的流量分布`}
            fullscreen={true}
            showControls={true} // 显式启用控制面板
          />
        </div>
      </div>
    </div>
  )
}
