"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { getAllSystemConfig, updateSystemConfig } from "@/lib/config"
import { useRouter } from "next/navigation"
import { createClient } from "@supabase/supabase-js"

export default function SystemConfigPage() {
  const [configs, setConfigs] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<string | null>(null)
  const [user, setUser] = useState<any>(null)
  const router = useRouter()

  // 加载数据
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

        if (!supabaseUrl || !supabaseKey) {
          throw new Error("Supabase环境变量未设置")
        }

        const supabase = createClient(supabaseUrl, supabaseKey)

        const {
          data: { user },
        } = await supabase.auth.getUser()

        if (!user) {
          // 未登录，重定向到登录页面
          router.push("/login?redirectTo=/system-config")
          return
        }

        setUser(user)

        // 加载配置数据
        const configData = await getAllSystemConfig()
        setConfigs(configData)
      } catch (err) {
        console.error("Error loading configs:", err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [router])

  // 处理配置更新
  const handleConfigUpdate = async (key: string, value: string) => {
    setMessage(null)
    setError(null)

    try {
      const success = await updateSystemConfig(key, value)

      if (success) {
        setMessage(`配置 ${key} 已成功更新`)

        // 刷新配置列表
        const configData = await getAllSystemConfig()
        setConfigs(configData)
      } else {
        setError(`更新配置 ${key} 失败`)
      }
    } catch (err) {
      console.error("Error updating config:", err)
      setError(err.message)
    }
  }

  // 处理输入变化
  const handleInputChange = (index: number, value: string) => {
    const updatedConfigs = [...configs]
    updatedConfigs[index].value = value
    setConfigs(updatedConfigs)
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 container mx-auto py-8">
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>系统配置</CardTitle>
            <CardDescription>管理系统配置参数</CardDescription>
            <div className="mt-2 p-2 bg-blue-50 text-blue-700 text-sm rounded-md">
              <p>
                提示：您可以在此页面编辑"加入我们"按钮点击后显示的文本内容。查找配置项 <strong>join_us_text</strong>。
              </p>
            </div>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {message && (
              <Alert className="mb-4 bg-green-50 text-green-700 border-green-200">
                <AlertDescription>{message}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-6">
              {configs.map((config, index) => (
                <div key={config.key} className="border p-4 rounded-md">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label htmlFor={`config-${config.key}`} className="text-base font-medium">
                        {config.key}
                      </Label>
                      <span className="text-xs text-muted-foreground">
                        最后更新: {new Date(config.updated_at).toLocaleString()}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">{config.description}</p>
                    <div className="flex gap-2">
                      <Input
                        id={`config-${config.key}`}
                        value={config.value}
                        onChange={(e) => handleInputChange(index, e.target.value)}
                      />
                      <Button onClick={() => handleConfigUpdate(config.key, config.value)}>更新</Button>
                    </div>
                  </div>
                </div>
              ))}

              {configs.length === 0 && <div className="text-center py-8 text-muted-foreground">暂无系统配置数据</div>}
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
