"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Upload, FileUp, Plus, Trash2, Save } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Header } from "@/components/header"
import { createClient } from "@/lib/supabase/client"

// 示例数据，用于预览和测试
const sampleData = {
  stats: {
    total_participants: 15,
    total_books: 25,
  },
  users: [
    {
      user_id: "user1",
      books: [
        {
          title: "深入理解计算机系统",
          category: ["计算机", "系统"],
          reading_status: "在读",
          progress: "45%",
        },
      ],
    },
    {
      user_id: "user2",
      books: [
        {
          title: "算法导论",
          category: ["计算机", "算法"],
          reading_status: "读完",
          progress: "100%",
        },
      ],
    },
  ],
}

// 阅读状态选项
const readingStatusOptions = ["未读", "在读", "读完", "暂停", "放弃"]

// 默认的空用户数据
const emptyUserData = {
  user_id: "",
  books: [
    {
      title: "",
      category: ["", ""],
      reading_status: "在读",
      progress: "0%",
    },
  ],
}

export default function ImportDataPage() {
  const [isImporting, setIsImporting] = useState(false)
  const [message, setMessage] = useState("")
  const [logs, setLogs] = useState<string[]>([])
  const [importData, setImportData] = useState<any>(null)
  const [isPreview, setIsPreview] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [dataSource, setDataSource] = useState<"file" | "sample" | "upload" | "manual">("sample")
  const [fileName, setFileName] = useState<string>("")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [user, setUser] = useState<any>(null)
  const router = useRouter()

  // 日期选择相关状态
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear())
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth()) // 默认为当前月份的上一个月
  const [selectedDay, setSelectedDay] = useState<number>(15) // 默认为月中日期

  // 手动录入相关状态
  const [manualUsers, setManualUsers] = useState<any[]>([{ ...emptyUserData }])
  const [manualStats, setManualStats] = useState({
    total_participants: 0,
    total_books: 0,
  })

  // 生成年份选项（当前年份和前后5年）
  const currentYear = new Date().getFullYear()
  const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i)

  // 月份名称
  const monthNames = [
    "一月",
    "二月",
    "三月",
    "四月",
    "五月",
    "六月",
    "七月",
    "八月",
    "九月",
    "十月",
    "十一月",
    "十二月",
  ]

  useEffect(() => {
    // 检查用户是否已登录
    const checkAuth = async () => {
      try {
        const supabase = createClient()

        const {
          data: { user },
        } = await supabase.auth.getUser()

        if (!user) {
          // 未登录，重定向到登录页面
          router.push("/login?redirectTo=/import-data")
          return
        }

        setUser(user)

        // 检测是否在预览模式
        const isPreviewMode =
          window.location.hostname.includes("vercel.app") ||
          window.location.hostname.includes("localhost") ||
          window.location.search.includes("preview=true")
        setIsPreview(isPreviewMode)

        // 默认使用示例数据
        setImportData(sampleData)

        // 设置默认日期为当前月份的上一个月
        const now = new Date()
        const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1
        const yearOfLastMonth = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear()

        setSelectedYear(yearOfLastMonth)
        setSelectedMonth(lastMonth + 1) // 月份从1开始
        setSelectedDay(15) // 默认为月中日期

        setIsLoading(false)
      } catch (error) {
        console.error("Auth check error:", error)
        // 在出错的情况下，也设置为非加载状态，避免无限加载
        setIsLoading(false)
        setMessage("登录验证出错，请刷新页面重试")
      }
    }

    checkAuth()
  }, [router])

  // 更新手动录入的统计数据
  useEffect(() => {
    if (dataSource === "manual") {
      const totalParticipants = manualUsers.filter((user) => user.user_id.trim() !== "").length
      const totalBooks = manualUsers.reduce((total, user) => {
        return total + user.books.filter((book) => book.title.trim() !== "").length
      }, 0)

      setManualStats({
        total_participants: totalParticipants,
        total_books: totalBooks,
      })

      // 更新导入数据
      setImportData({
        stats: {
          total_participants: totalParticipants,
          total_books: totalBooks,
        },
        users: manualUsers.filter((user) => user.user_id.trim() !== ""),
      })
    }
  }, [manualUsers, dataSource])

  // 其余代码保持不变...

  // 以下是原有代码的其余部分，保持不变
  const addLog = (log: string) => {
    setLogs((prev) => [...prev, log])
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (file.type !== "application/json" && !file.name.endsWith(".json")) {
      setMessage("请上传JSON格式的文件")
      return
    }

    setIsLoading(true)
    setFileName(file.name)
    setMessage(`正在读取文件: ${file.name}`)

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        const data = JSON.parse(content)
        setImportData(data)
        setDataSource("upload")
        setMessage(`文件 ${file.name} 已成功加载`)
        addLog(`成功从上传的文件加载数据: ${file.name}`)
      } catch (error) {
        console.error("Error parsing JSON:", error)
        setMessage(`解析JSON文件时出错: ${error.message}`)
        // 如果解析失败，回退到示例数据
        setImportData(sampleData)
        setDataSource("sample")
      } finally {
        setIsLoading(false)
      }
    }

    reader.onerror = () => {
      setMessage(`读取文件时出错`)
      setImportData(sampleData)
      setDataSource("sample")
      setIsLoading(false)
    }

    reader.readAsText(file)
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  // 手动录入相关函数
  const handleAddUser = () => {
    setManualUsers([...manualUsers, { ...emptyUserData }])
  }

  const handleRemoveUser = (index: number) => {
    const newUsers = [...manualUsers]
    newUsers.splice(index, 1)
    setManualUsers(newUsers.length > 0 ? newUsers : [{ ...emptyUserData }])
  }

  const handleAddBook = (userIndex: number) => {
    const newUsers = [...manualUsers]
    newUsers[userIndex].books.push({
      title: "",
      category: ["", ""],
      reading_status: "在读",
      progress: "0%",
    })
    setManualUsers(newUsers)
  }

  const handleRemoveBook = (userIndex: number, bookIndex: number) => {
    const newUsers = [...manualUsers]
    newUsers[userIndex].books.splice(bookIndex, 1)
    if (newUsers[userIndex].books.length === 0) {
      newUsers[userIndex].books.push({
        title: "",
        category: ["", ""],
        reading_status: "在读",
        progress: "0%",
      })
    }
    setManualUsers(newUsers)
  }

  const handleUserChange = (index: number, field: string, value: string) => {
    const newUsers = [...manualUsers]
    newUsers[index][field] = value
    setManualUsers(newUsers)
  }

  const handleBookChange = (userIndex: number, bookIndex: number, field: string, value: any) => {
    const newUsers = [...manualUsers]
    if (field === "mainCategory" || field === "subCategory") {
      const categoryIndex = field === "mainCategory" ? 0 : 1
      newUsers[userIndex].books[bookIndex].category[categoryIndex] = value
    } else {
      newUsers[userIndex].books[bookIndex][field] = value
    }
    setManualUsers(newUsers)
  }

  const handleSaveManualData = () => {
    // 过滤掉空用户和空书籍
    const filteredUsers = manualUsers
      .filter((user) => user.user_id.trim() !== "")
      .map((user) => ({
        ...user,
        books: user.books.filter((book) => book.title.trim() !== ""),
      }))
      .filter((user) => user.books.length > 0)

    if (filteredUsers.length === 0) {
      setMessage("请至少添加一个有效的用户和书籍")
      return
    }

    const manualData = {
      stats: {
        total_participants: filteredUsers.length,
        total_books: filteredUsers.reduce((total, user) => total + user.books.length, 0),
      },
      users: filteredUsers,
    }

    setImportData(manualData)
    setDataSource("manual")
    setMessage("手动录入数据已保存")
  }

  const handleImport = async () => {
    if (!importData) {
      setMessage("无数据可导入")
      return
    }

    setIsImporting(true)
    let sourceDescription = ""
    switch (dataSource) {
      case "file":
        sourceDescription = `${selectedYear}年${selectedMonth}月`
        break
      case "upload":
        sourceDescription = `上传的${fileName || "JSON文件"}`
        break
      case "sample":
        sourceDescription = "示例"
        break
      case "manual":
        sourceDescription = "手动录入"
        break
    }

    setMessage(`正在导入${sourceDescription}数据...`)
    setLogs([])

    try {
      // 创建 Supabase 客户端
      const supabase = createClient()

      addLog(`开始导入${sourceDescription}数据...`)
      addLog(
        `目标时间: ${selectedYear}年${monthNames[selectedMonth - 1]} (${new Date(selectedYear, selectedMonth - 1, 15).toISOString()})`,
      )

      // 设置时间标识 - 选择的年月
      const targetDate = new Date(selectedYear, selectedMonth - 1, selectedDay) // 使用选择的具体日期

      // 1. 导入用户数据
      addLog("导入用户数据...")
      for (const user of importData.users) {
        // 检查用户是否已存在
        const { data: existingUser, error: userCheckError } = await supabase
          .from("users")
          .select("id")
          .eq("user_id", user.user_id)
          .maybeSingle()

        if (userCheckError) {
          addLog(`Error checking user ${user.user_id}: ${userCheckError.message}`)
          continue
        }

        let userId
        if (existingUser) {
          userId = existingUser.id
          addLog(`用户 ${user.user_id} 已存在，ID: ${userId}`)
        } else {
          // 创建新用户
          const { data: userData, error } = await supabase
            .from("users")
            .insert({
              user_id: user.user_id,
              created_at: targetDate.toISOString(),
            })
            .select()
            .single()

          if (error) {
            addLog(`Error importing user ${user.user_id}: ${error.message}`)
            continue
          }

          userId = userData.id
          addLog(`用户 ${user.user_id} 导入成功，ID: ${userId}`)
        }

        // 2. 处理该用户的书籍和阅读记录
        for (const book of user.books) {
          if (!book.title) {
            addLog("跳过空标题书籍")
            continue
          }

          // 处理主分类
          let mainCategoryId = null
          if (book.category && book.category.length > 0) {
            const mainCategoryName = book.category[0]

            // 检查主分类是否存在
            const { data: mainCategoryData, error: mainCategoryError } = await supabase
              .from("main_categories")
              .select("id")
              .eq("name", mainCategoryName)
              .maybeSingle()

            if (mainCategoryError) {
              addLog(`Error checking main category ${mainCategoryName}: ${mainCategoryError.message}`)
              continue
            }

            if (mainCategoryData) {
              mainCategoryId = mainCategoryData.id
              addLog(`主分类 ${mainCategoryName} 已存在，ID: ${mainCategoryId}`)
            } else {
              // 创建新的主分类
              const { data: newMainCategory, error: newMainCategoryError } = await supabase
                .from("main_categories")
                .insert({ name: mainCategoryName })
                .select()
                .single()

              if (newMainCategoryError) {
                addLog(`Error creating main category ${mainCategoryName}: ${newMainCategoryError.message}`)
                continue
              }

              mainCategoryId = newMainCategory.id
              addLog(`主分类 ${mainCategoryName} 创建成功，ID: ${mainCategoryId}`)
            }
          }

          // 处理子分类
          let subCategoryId = null
          if (book.category && book.category.length > 1 && mainCategoryId) {
            const subCategoryName = book.category[1]

            // 检查子分类是否存在
            const { data: subCategoryData, error: subCategoryError } = await supabase
              .from("sub_categories")
              .select("id")
              .eq("name", subCategoryName)
              .eq("main_category_id", mainCategoryId)
              .maybeSingle()

            if (subCategoryError) {
              addLog(`Error checking sub category ${subCategoryName}: ${subCategoryError.message}`)
            } else if (subCategoryData) {
              subCategoryId = subCategoryData.id
              addLog(`子分类 ${subCategoryName} 已存在，ID: ${subCategoryId}`)
            } else {
              // 创建新的子分类
              const { data: newSubCategory, error: newSubCategoryError } = await supabase
                .from("sub_categories")
                .insert({
                  name: subCategoryName,
                  main_category_id: mainCategoryId,
                })
                .select()
                .single()

              if (newSubCategoryError) {
                addLog(`Error creating sub category ${subCategoryName}: ${newSubCategoryError.message}`)
              } else {
                subCategoryId = newSubCategory.id
                addLog(`子分类 ${subCategoryName} 创建成功，ID: ${subCategoryId}`)
              }
            }
          }

          // 检查书籍是否存在
          const { data: existingBook, error: bookCheckError } = await supabase
            .from("books")
            .select("id")
            .eq("title", book.title)
            .maybeSingle()

          if (bookCheckError) {
            addLog(`Error checking book ${book.title}: ${bookCheckError.message}`)
            continue
          }

          let bookId
          if (existingBook) {
            bookId = existingBook.id
            addLog(`书籍 ${book.title} 已存在，ID: ${bookId}`)
          } else {
            // 创建新书籍
            const { data: newBook, error: newBookError } = await supabase
              .from("books")
              .insert({
                title: book.title,
                main_category_id: mainCategoryId,
                sub_category_id: subCategoryId,
                created_at: targetDate.toISOString(),
              })
              .select()
              .single()

            if (newBookError) {
              addLog(`Error creating book ${book.title}: ${newBookError.message}`)
              continue
            }

            bookId = newBook.id
            addLog(`书籍 ${book.title} 创建成功，ID: ${bookId}`)
          }

          // 检查阅读记录是否存在
          const { data: existingRecord, error: recordCheckError } = await supabase
            .from("reading_records")
            .select("id")
            .eq("user_id", userId)
            .eq("book_id", bookId)
            .maybeSingle()

          if (recordCheckError) {
            addLog(`Error checking reading record: ${recordCheckError.message}`)
            continue
          }

          if (existingRecord) {
            addLog(`阅读记录已存在，ID: ${existingRecord.id}`)

            // 更新现有记录的时间戳
            const { error: updateError } = await supabase
              .from("reading_records")
              .update({
                reading_status: book.reading_status || "未知",
                progress: book.progress || "0%",
                updated_at: targetDate.toISOString(),
              })
              .eq("id", existingRecord.id)

            if (updateError) {
              addLog(`Error updating reading record: ${updateError.message}`)
            } else {
              addLog(`阅读记录更新成功，ID: ${existingRecord.id}`)
            }
          } else {
            // 创建阅读记录
            const { data: newRecord, error: recordError } = await supabase
              .from("reading_records")
              .insert({
                user_id: userId,
                book_id: bookId,
                reading_status: book.reading_status || "未知",
                progress: book.progress || "0%",
                created_at: targetDate.toISOString(),
                updated_at: targetDate.toISOString(),
              })
              .select()
              .single()

            if (recordError) {
              addLog(`Error creating reading record for ${user.user_id} - ${book.title}: ${recordError.message}`)
            } else {
              addLog(`阅读记录创建成功，ID: ${newRecord.id}`)
            }
          }
        }
      }

      // 3. 导入月度统计数据
      addLog("导入月度统计数据...")
      const completedBooks = importData.users.flatMap((u) => u.books).filter((b) => b.reading_status === "读完").length
      const activeReaders = importData.users.filter((u) => u.books.length > 0).length

      // 检查月度统计是否已存在
      const { data: existingStats, error: statsCheckError } = await supabase
        .from("monthly_stats")
        .select("id")
        .eq("year", selectedYear)
        .eq("month", selectedMonth)
        .maybeSingle()

      if (statsCheckError) {
        addLog(`Error checking monthly stats: ${statsCheckError.message}`)
      } else if (existingStats) {
        addLog(`月度统计已存在，ID: ${existingStats.id}，年份: ${selectedYear}，月份: ${selectedMonth}`)

        // 更新月度统计
        const { error: updateStatsError } = await supabase
          .from("monthly_stats")
          .update({
            total_participants: importData.stats.total_participants,
            total_books: importData.stats.total_books,
            active_readers: activeReaders,
            completed_books: completedBooks,
            // 移除 updated_at 字段
          })
          .eq("id", existingStats.id)

        if (updateStatsError) {
          addLog(`Error updating monthly stats: ${updateStatsError.message}`)
        } else {
          addLog(`月度统计更新成功，年份: ${selectedYear}，月份: ${selectedMonth}`)
        }
      } else {
        // 创建月度统计
        const { data: newStats, error: statsError } = await supabase
          .from("monthly_stats")
          .insert({
            year: selectedYear,
            month: selectedMonth,
            total_participants: importData.stats.total_participants,
            total_books: importData.stats.total_books,
            active_readers: activeReaders,
            completed_books: completedBooks,
            created_at: targetDate.toISOString(),
          })
          .select()
          .single()

        if (statsError) {
          addLog(`Error importing monthly stats: ${statsError.message}`)
        } else {
          addLog(`月度统计创建成功，ID: ${newStats.id}`)
        }
      }

      addLog(`${sourceDescription}数据导入完成!`)
      setMessage(`${sourceDescription}数据导入成功!`)
    } catch (error) {
      console.error("Import error:", error)
      setMessage(`导入出错: ${error.message}`)
    } finally {
      setIsImporting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 container mx-auto py-8">
          <Card>
            <CardHeader>
              <CardTitle>导入数据</CardTitle>
              <CardDescription>正在加载数据...</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-10">
        <Card>
          <CardHeader>
            <CardTitle>导入数据</CardTitle>
            <CardDescription>上传并导入读书群数据到数据库中 - 管理员: {user?.email}</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="upload" className="space-y-4">
              <TabsList>
                <TabsTrigger value="upload">上传数据</TabsTrigger>
                <TabsTrigger value="manual">手动录入</TabsTrigger>
                <TabsTrigger value="preview">数据预览</TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="space-y-4">
                {isPreview && (
                  <Alert variant="warning" className="bg-amber-50 text-amber-800 border-amber-200">
                    <AlertDescription>
                      您正在预览模式下查看此页面。在预览模式下，数据导入功能被禁用，但您可以查看页面布局和设计。
                    </AlertDescription>
                  </Alert>
                )}

                {/* 文件上传区域 */}
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:bg-gray-50 transition-colors cursor-pointer"
                  onClick={handleUploadClick}
                  onDragOver={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    e.currentTarget.classList.add("border-blue-400", "bg-blue-50")
                  }}
                  onDragEnter={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    e.currentTarget.classList.add("border-blue-400", "bg-blue-50")
                  }}
                  onDragLeave={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    e.currentTarget.classList.remove("border-blue-400", "bg-blue-50")
                  }}
                  onDrop={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    e.currentTarget.classList.remove("border-blue-400", "bg-blue-50")

                    const files = e.dataTransfer.files
                    if (files && files.length > 0) {
                      const file = files[0]
                      if (file.type === "application/json" || file.name.endsWith(".json")) {
                        // 使用相同的处理函数处理拖放的文件
                        const event = { target: { files: [file] } } as React.ChangeEvent<HTMLInputElement>
                        handleFileChange(event)
                      } else {
                        setMessage("请上传JSON格式的文件")
                      }
                    }
                  }}
                >
                  <input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".json" className="hidden" />
                  <FileUp className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    {dataSource === "upload" ? "更换文件" : "选择JSON文件"}
                  </h3>
                  <p className="mt-1 text-xs text-gray-500">
                    {dataSource === "upload" ? `当前文件: ${fileName}` : "点击选择或拖放JSON文件到此处"}
                  </p>
                </div>

                {/* 日期选择区域 - 直接集成到上传数据页面 */}
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-3">选择数据月份</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">年份</label>
                      <Select
                        value={selectedYear.toString()}
                        onValueChange={(value) => setSelectedYear(Number.parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择年份" />
                        </SelectTrigger>
                        <SelectContent>
                          {yearOptions.map((year) => (
                            <SelectItem key={year} value={year.toString()}>
                              {year}年
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">月份</label>
                      <Select
                        value={selectedMonth.toString()}
                        onValueChange={(value) => setSelectedMonth(Number.parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择月份" />
                        </SelectTrigger>
                        <SelectContent>
                          {monthNames.map((name, index) => (
                            <SelectItem key={index} value={(index + 1).toString()}>
                              {name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">日期 (created_at)</label>
                      <Select
                        value={selectedDay.toString()}
                        onValueChange={(value) => setSelectedDay(Number.parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择日期" />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                            <SelectItem key={day} value={day.toString()}>
                              {day}日
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    数据将被标记为 {selectedYear}年{monthNames[selectedMonth - 1]}，记录创建日期为 {selectedYear}年
                    {selectedMonth}月{selectedDay}日
                  </p>
                </div>

                {/* 导入信息摘要 */}
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-2">导入信息:</h3>
                  <ul className="list-disc pl-5 text-sm">
                    <li>
                      目标时间: {selectedYear}年{monthNames[selectedMonth - 1]}
                    </li>
                    <li>
                      记录创建日期: {selectedYear}年{selectedMonth}月{selectedDay}日
                    </li>
                    <li>
                      数据来源:{" "}
                      {dataSource === "file"
                        ? "预设文件"
                        : dataSource === "upload"
                          ? `上传的文件 (${fileName})`
                          : dataSource === "manual"
                            ? "手动录入"
                            : "示例数据"}
                    </li>
                  </ul>
                </div>
              </TabsContent>

              <TabsContent value="manual" className="space-y-4">
                {isPreview && (
                  <Alert variant="warning" className="bg-amber-50 text-amber-800 border-amber-200">
                    <AlertDescription>
                      您正在预览模式下查看此页面。在预览模式下，数据导入功能被禁用，但您可以查看页面布局和设计。
                    </AlertDescription>
                  </Alert>
                )}

                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-3">手动录入数据</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    在此页面手动录入用户和书籍数据，适用于少量数据的快速添加。
                  </p>

                  {/* 用户列表 */}
                  <div className="space-y-6">
                    {manualUsers.map((user, userIndex) => (
                      <div key={userIndex} className="border border-gray-200 rounded-md p-4 bg-white">
                        <div className="flex justify-between items-center mb-3">
                          <h4 className="font-medium">用户 #{userIndex + 1}</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveUser(userIndex)}
                            disabled={manualUsers.length === 1}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>

                        {/* 用户ID */}
                        <div className="mb-4">
                          <Label htmlFor={`user-id-${userIndex}`}>用户ID</Label>
                          <Input
                            id={`user-id-${userIndex}`}
                            value={user.user_id}
                            onChange={(e) => handleUserChange(userIndex, "user_id", e.target.value)}
                            placeholder="例如: user123"
                            className="mt-1"
                          />
                        </div>

                        {/* 书籍列表 */}
                        <div className="space-y-4">
                          <h5 className="font-medium text-sm">书籍列表</h5>
                          {user.books.map((book, bookIndex) => (
                            <div key={bookIndex} className="border-t border-gray-100 pt-3">
                              <div className="flex justify-between items-center mb-2">
                                <h6 className="text-sm font-medium">书籍 #{bookIndex + 1}</h6>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveBook(userIndex, bookIndex)}
                                  disabled={user.books.length === 1}
                                >
                                  <Trash2 className="h-3 w-3 text-red-500" />
                                </Button>
                              </div>

                              {/* 书籍标题 */}
                              <div className="mb-2">
                                <Label htmlFor={`book-title-${userIndex}-${bookIndex}`} className="text-xs">
                                  书籍标题
                                </Label>
                                <Input
                                  id={`book-title-${userIndex}-${bookIndex}`}
                                  value={book.title}
                                  onChange={(e) => handleBookChange(userIndex, bookIndex, "title", e.target.value)}
                                  placeholder="例如: 深入理解计算机系统"
                                  className="mt-1"
                                />
                              </div>

                              {/* 主分类和子分类 */}
                              <div className="grid grid-cols-2 gap-2 mb-2">
                                <div>
                                  <Label htmlFor={`main-category-${userIndex}-${bookIndex}`} className="text-xs">
                                    主分类
                                  </Label>
                                  <Input
                                    id={`main-category-${userIndex}-${bookIndex}`}
                                    value={book.category[0]}
                                    onChange={(e) =>
                                      handleBookChange(userIndex, bookIndex, "mainCategory", e.target.value)
                                    }
                                    placeholder="例如: 计算机"
                                    className="mt-1"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`sub-category-${userIndex}-${bookIndex}`} className="text-xs">
                                    子分类
                                  </Label>
                                  <Input
                                    id={`sub-category-${userIndex}-${bookIndex}`}
                                    value={book.category[1]}
                                    onChange={(e) =>
                                      handleBookChange(userIndex, bookIndex, "subCategory", e.target.value)
                                    }
                                    placeholder="例如: 系统"
                                    className="mt-1"
                                  />
                                </div>
                              </div>

                              {/* 阅读状态和进度 */}
                              <div className="grid grid-cols-2 gap-2">
                                <div>
                                  <Label htmlFor={`reading-status-${userIndex}-${bookIndex}`} className="text-xs">
                                    阅读状态
                                  </Label>
                                  <Select
                                    value={book.reading_status}
                                    onValueChange={(value) =>
                                      handleBookChange(userIndex, bookIndex, "reading_status", value)
                                    }
                                  >
                                    <SelectTrigger id={`reading-status-${userIndex}-${bookIndex}`} className="mt-1">
                                      <SelectValue placeholder="选择阅读状态" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {readingStatusOptions.map((status) => (
                                        <SelectItem key={status} value={status}>
                                          {status}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div>
                                  <Label htmlFor={`progress-${userIndex}-${bookIndex}`} className="text-xs">
                                    进度
                                  </Label>
                                  <Input
                                    id={`progress-${userIndex}-${bookIndex}`}
                                    value={book.progress}
                                    onChange={(e) => handleBookChange(userIndex, bookIndex, "progress", e.target.value)}
                                    placeholder="例如: 45%"
                                    className="mt-1"
                                  />
                                </div>
                              </div>
                            </div>
                          ))}

                          {/* 添加书籍按钮 */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleAddBook(userIndex)}
                            className="w-full mt-2"
                          >
                            <Plus className="h-4 w-4 mr-1" /> 添加书籍
                          </Button>
                        </div>
                      </div>
                    ))}

                    {/* 添加用户按钮 */}
                    <Button variant="outline" onClick={handleAddUser} className="w-full">
                      <Plus className="h-4 w-4 mr-1" /> 添加用户
                    </Button>
                  </div>

                  {/* 保存手动录入数据按钮 */}
                  <Button
                    onClick={handleSaveManualData}
                    className="w-full mt-4 flex items-center justify-center gap-2"
                    disabled={isPreview}
                  >
                    <Save className="h-4 w-4" />
                    <span>保存手动录入数据</span>
                  </Button>

                  {/* 手动录入统计信息 */}
                  {dataSource === "manual" && (
                    <div className="mt-4 p-3 bg-green-50 text-green-700 rounded-md">
                      <p>
                        已保存手动录入数据：{manualStats.total_participants} 位用户，{manualStats.total_books} 本书籍
                      </p>
                    </div>
                  )}
                </div>

                {/* 日期选择区域 - 与上传数据页面相同 */}
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-3">选择数据月份</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">年份</label>
                      <Select
                        value={selectedYear.toString()}
                        onValueChange={(value) => setSelectedYear(Number.parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择年份" />
                        </SelectTrigger>
                        <SelectContent>
                          {yearOptions.map((year) => (
                            <SelectItem key={year} value={year.toString()}>
                              {year}年
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">月份</label>
                      <Select
                        value={selectedMonth.toString()}
                        onValueChange={(value) => setSelectedMonth(Number.parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择月份" />
                        </SelectTrigger>
                        <SelectContent>
                          {monthNames.map((name, index) => (
                            <SelectItem key={index} value={(index + 1).toString()}>
                              {name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">日期 (created_at)</label>
                      <Select
                        value={selectedDay.toString()}
                        onValueChange={(value) => setSelectedDay(Number.parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择日期" />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                            <SelectItem key={day} value={day.toString()}>
                              {day}日
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    数据将被标记为 {selectedYear}年{monthNames[selectedMonth - 1]}，记录创建日期为 {selectedYear}年
                    {selectedMonth}月{selectedDay}日
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="preview" className="space-y-4">
                {importData ? (
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="font-medium mb-2">数据概览:</h3>
                    <ul className="list-disc pl-5 text-sm">
                      <li>总参与人数: {importData.stats.total_participants}人</li>
                      <li>总书籍数量: {importData.stats.total_books}本</li>
                      <li>用户数量: {importData.users.length}人</li>
                      <li>书籍记录: {importData.users.reduce((acc, user) => acc + user.books.length, 0)}条</li>
                      <li>
                        时间标识: {selectedYear}年{monthNames[selectedMonth - 1]}
                      </li>
                      <li>
                        记录创建日期: {selectedYear}年{selectedMonth}月{selectedDay}日
                      </li>
                      <li>
                        数据来源:{" "}
                        {dataSource === "file"
                          ? "预设文件"
                          : dataSource === "upload"
                            ? `上传的文件 (${fileName})`
                            : dataSource === "manual"
                              ? "手动录入"
                              : "示例数据"}
                      </li>
                    </ul>

                    <div className="mt-4">
                      <h4 className="font-medium mb-1">用户数据预览:</h4>
                      <div className="max-h-60 overflow-y-auto bg-white p-2 rounded border text-xs">
                        <pre>{JSON.stringify(importData.users.slice(0, 2), null, 2)}</pre>
                        {importData.users.length > 2 && <div className="text-center mt-2">...</div>}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-red-50 p-4 rounded-md text-red-700">无法加载数据</div>
                )}
              </TabsContent>
            </Tabs>

            <div className="mt-6 space-y-4">
              <Button
                onClick={handleImport}
                disabled={isImporting || isPreview || !importData}
                className="w-full flex items-center justify-center gap-2"
              >
                {isImporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>导入中...</span>
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4" />
                    <span>导入数据到数据库</span>
                  </>
                )}
              </Button>

              {message && (
                <div
                  className={`p-3 rounded-md ${
                    message.includes("成功")
                      ? "bg-green-50 text-green-700"
                      : message.includes("出错")
                        ? "bg-red-50 text-red-700"
                        : "bg-amber-50 text-amber-700"
                  }`}
                >
                  {message}
                </div>
              )}

              {logs.length > 0 && (
                <div className="mt-4">
                  <h3 className="font-medium mb-2">导入日志:</h3>
                  <div className="bg-gray-100 p-3 rounded-md max-h-64 overflow-y-auto font-mono text-xs">
                    {logs.map((log, index) => (
                      <div key={index} className="mb-1">
                        {log}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
