"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { createClient } from "@supabase/supabase-js"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Upload, FileUp } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Header } from "@/components/header"

// 示例数据，用于预览和测试
const sampleData = {
  stats: {
    total_participants: 15,
    total_books: 25,
  },
  users: [
    {
      user_id: "user1",
      books: [
        {
          title: "深入理解计算机系统",
          category: ["计算机", "系统"],
          reading_status: "在读",
          progress: "45%",
        },
      ],
    },
    {
      user_id: "user2",
      books: [
        {
          title: "算法导论",
          category: ["计算机", "算法"],
          reading_status: "读完",
          progress: "100%",
        },
      ],
    },
  ],
}

export default function ImportDataPage() {
  const [isImporting, setIsImporting] = useState(false)
  const [message, setMessage] = useState("")
  const [logs, setLogs] = useState<string[]>([])
  const [importData, setImportData] = useState<any>(null)
  const [isPreview, setIsPreview] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [dataSource, setDataSource] = useState<"file" | "sample" | "upload">("sample")
  const [fileName, setFileName] = useState<string>("")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [user, setUser] = useState<any>(null)
  const router = useRouter()

  // 日期选择相关状态
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear())
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth()) // 默认为当前月份的上一个月

  // 生成年份选项（当前年份和前后5年）
  const currentYear = new Date().getFullYear()
  const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i)

  // 月份名称
  const monthNames = [
    "一月",
    "二月",
    "三月",
    "四月",
    "五月",
    "六月",
    "七月",
    "八月",
    "九月",
    "十月",
    "十一月",
    "十二月",
  ]

  useEffect(() => {
    // 检查用户是否已登录
    const checkAuth = async () => {
      try {
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

        if (!supabaseUrl || !supabaseKey) {
          throw new Error("Supabase环境变量未设置")
        }

        const supabase = createClient(supabaseUrl, supabaseKey)

        const {
          data: { user },
        } = await supabase.auth.getUser()

        if (!user) {
          // 未登录，重定向到登录页面
          router.push("/login?redirectTo=/import-data")
          return
        }

        setUser(user)

        // 检测是否在预览模式
        const isPreviewMode =
          window.location.hostname.includes("vercel.app") || window.location.search.includes("preview=true")
        setIsPreview(isPreviewMode)

        // 默认使用示例数据
        setImportData(sampleData)

        // 设置默认日期为当前月份的上一个月
        const now = new Date()
        const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1
        const yearOfLastMonth = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear()

        setSelectedYear(yearOfLastMonth)
        setSelectedMonth(lastMonth + 1) // 月份从1开始

        setIsLoading(false)
      } catch (error) {
        console.error("Auth check error:", error)
        router.push("/login?redirectTo=/import-data")
      }
    }

    checkAuth()
  }, [router])

  const addLog = (log: string) => {
    setLogs((prev) => [...prev, log])
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsLoading(true)
    setFileName(file.name)
    setMessage(`正在读取文件: ${file.name}`)

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        const data = JSON.parse(content)
        setImportData(data)
        setDataSource("upload")
        setMessage(`文件 ${file.name} 已成功加载`)
        addLog(`成功从上传的文件加载数据: ${file.name}`)
      } catch (error) {
        console.error("Error parsing JSON:", error)
        setMessage(`解析JSON文件时出错: ${error.message}`)
        // 如果解析失败，回退到示例数据
        setImportData(sampleData)
        setDataSource("sample")
      } finally {
        setIsLoading(false)
      }
    }

    reader.onerror = () => {
      setMessage(`读取文件时出错`)
      setImportData(sampleData)
      setDataSource("sample")
      setIsLoading(false)
    }

    reader.readAsText(file)
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleImport = async () => {
    if (!importData) {
      setMessage("无数据可导入")
      return
    }

    setIsImporting(true)
    let sourceDescription = ""
    switch (dataSource) {
      case "file":
        sourceDescription = `${selectedYear}年${selectedMonth}月`
        break
      case "upload":
        sourceDescription = `上传的${fileName || "JSON文件"}`
        break
      case "sample":
        sourceDescription = "示例"
        break
    }

    setMessage(`正在导入${sourceDescription}数据...`)
    setLogs([])

    try {
      // 创建 Supabase 客户端
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

      if (!supabaseUrl || !supabaseKey) {
        throw new Error("Supabase环境变量未设置")
      }

      const supabase = createClient(supabaseUrl, supabaseKey)

      addLog(`开始导入${sourceDescription}数据...`)
      addLog(
        `目标时间: ${selectedYear}年${monthNames[selectedMonth - 1]} (${new Date(selectedYear, selectedMonth - 1, 15).toISOString()})`,
      )

      // 设置时间标识 - 选择的年月
      const targetDate = new Date(selectedYear, selectedMonth - 1, 15) // 月份中间日期作为代表

      // 1. 导入用户数据
      addLog("导入用户数据...")
      for (const user of importData.users) {
        // 检查用户是否已存在
        const { data: existingUser, error: userCheckError } = await supabase
          .from("users")
          .select("id")
          .eq("user_id", user.user_id)
          .maybeSingle()

        if (userCheckError) {
          addLog(`Error checking user ${user.user_id}: ${userCheckError.message}`)
          continue
        }

        let userId
        if (existingUser) {
          userId = existingUser.id
          addLog(`用户 ${user.user_id} 已存在，ID: ${userId}`)
        } else {
          // 创建新用户
          const { data: userData, error } = await supabase
            .from("users")
            .insert({
              user_id: user.user_id,
              created_at: targetDate.toISOString(),
            })
            .select()
            .single()

          if (error) {
            addLog(`Error importing user ${user.user_id}: ${error.message}`)
            continue
          }

          userId = userData.id
          addLog(`用户 ${user.user_id} 导入成功，ID: ${userId}`)
        }

        // 2. 处理该用户的书籍和阅读记录
        for (const book of user.books) {
          if (!book.title) {
            addLog("跳过空标题书籍")
            continue
          }

          // 处理主分类
          let mainCategoryId = null
          if (book.category && book.category.length > 0) {
            const mainCategoryName = book.category[0]

            // 检查主分类是否存在
            const { data: mainCategoryData, error: mainCategoryError } = await supabase
              .from("main_categories")
              .select("id")
              .eq("name", mainCategoryName)
              .maybeSingle()

            if (mainCategoryError) {
              addLog(`Error checking main category ${mainCategoryName}: ${mainCategoryError.message}`)
              continue
            }

            if (mainCategoryData) {
              mainCategoryId = mainCategoryData.id
              addLog(`主分类 ${mainCategoryName} 已存在，ID: ${mainCategoryId}`)
            } else {
              // 创建新的主分类
              const { data: newMainCategory, error: newMainCategoryError } = await supabase
                .from("main_categories")
                .insert({ name: mainCategoryName })
                .select()
                .single()

              if (newMainCategoryError) {
                addLog(`Error creating main category ${mainCategoryName}: ${newMainCategoryError.message}`)
                continue
              }

              mainCategoryId = newMainCategory.id
              addLog(`主分类 ${mainCategoryName} 创建成功，ID: ${mainCategoryId}`)
            }
          }

          // 处理子分类
          let subCategoryId = null
          if (book.category && book.category.length > 1 && mainCategoryId) {
            const subCategoryName = book.category[1]

            // 检查子分类是否存在
            const { data: subCategoryData, error: subCategoryError } = await supabase
              .from("sub_categories")
              .select("id")
              .eq("name", subCategoryName)
              .eq("main_category_id", mainCategoryId)
              .maybeSingle()

            if (subCategoryError) {
              addLog(`Error checking sub category ${subCategoryName}: ${subCategoryError.message}`)
            } else if (subCategoryData) {
              subCategoryId = subCategoryData.id
              addLog(`子分类 ${subCategoryName} 已存在，ID: ${subCategoryId}`)
            } else {
              // 创建新的子分类
              const { data: newSubCategory, error: newSubCategoryError } = await supabase
                .from("sub_categories")
                .insert({
                  name: subCategoryName,
                  main_category_id: mainCategoryId,
                })
                .select()
                .single()

              if (newSubCategoryError) {
                addLog(`Error creating sub category ${subCategoryName}: ${newSubCategoryError.message}`)
              } else {
                subCategoryId = newSubCategory.id
                addLog(`子分类 ${subCategoryName} 创建成功，ID: ${subCategoryId}`)
              }
            }
          }

          // 检查书籍是否存在
          const { data: existingBook, error: bookCheckError } = await supabase
            .from("books")
            .select("id")
            .eq("title", book.title)
            .maybeSingle()

          if (bookCheckError) {
            addLog(`Error checking book ${book.title}: ${bookCheckError.message}`)
            continue
          }

          let bookId
          if (existingBook) {
            bookId = existingBook.id
            addLog(`书籍 ${book.title} 已存在，ID: ${bookId}`)
          } else {
            // 创建新书籍
            const { data: newBook, error: newBookError } = await supabase
              .from("books")
              .insert({
                title: book.title,
                main_category_id: mainCategoryId,
                sub_category_id: subCategoryId,
                created_at: targetDate.toISOString(),
              })
              .select()
              .single()

            if (newBookError) {
              addLog(`Error creating book ${book.title}: ${newBookError.message}`)
              continue
            }

            bookId = newBook.id
            addLog(`书籍 ${book.title} 创建成功，ID: ${bookId}`)
          }

          // 检查阅读记录是否存在
          const { data: existingRecord, error: recordCheckError } = await supabase
            .from("reading_records")
            .select("id")
            .eq("user_id", userId)
            .eq("book_id", bookId)
            .maybeSingle()

          if (recordCheckError) {
            addLog(`Error checking reading record: ${recordCheckError.message}`)
            continue
          }

          if (existingRecord) {
            addLog(`阅读记录已存在，ID: ${existingRecord.id}`)

            // 更新现有记录的时间戳
            const { error: updateError } = await supabase
              .from("reading_records")
              .update({
                reading_status: book.reading_status || "未知",
                progress: book.progress || "0%",
                updated_at: targetDate.toISOString(),
              })
              .eq("id", existingRecord.id)

            if (updateError) {
              addLog(`Error updating reading record: ${updateError.message}`)
            } else {
              addLog(`阅读记录更新成功，ID: ${existingRecord.id}`)
            }
          } else {
            // 创建阅读记录
            const { data: newRecord, error: recordError } = await supabase
              .from("reading_records")
              .insert({
                user_id: userId,
                book_id: bookId,
                reading_status: book.reading_status || "未知",
                progress: book.progress || "0%",
                created_at: targetDate.toISOString(),
                updated_at: targetDate.toISOString(),
              })
              .select()
              .single()

            if (recordError) {
              addLog(`Error creating reading record for ${user.user_id} - ${book.title}: ${recordError.message}`)
            } else {
              addLog(`阅读记录创建成功，ID: ${newRecord.id}`)
            }
          }
        }
      }

      // 3. 导入月度统计数据
      addLog("导入月度统计数据...")
      const completedBooks = importData.users.flatMap((u) => u.books).filter((b) => b.reading_status === "读完").length
      const activeReaders = importData.users.filter((u) => u.books.length > 0).length

      // 检查月度统计是否已存在
      const { data: existingStats, error: statsCheckError } = await supabase
        .from("monthly_stats")
        .select("id")
        .eq("year", selectedYear)
        .eq("month", selectedMonth)
        .maybeSingle()

      if (statsCheckError) {
        addLog(`Error checking monthly stats: ${statsCheckError.message}`)
      } else if (existingStats) {
        addLog(`月度统计已存在，ID: ${existingStats.id}，年份: ${selectedYear}，月份: ${selectedMonth}`)

        // 更新月度统计
        const { error: updateStatsError } = await supabase
          .from("monthly_stats")
          .update({
            total_participants: importData.stats.total_participants,
            total_books: importData.stats.total_books,
            active_readers: activeReaders,
            completed_books: completedBooks,
            // 移除 updated_at 字段
          })
          .eq("id", existingStats.id)

        if (updateStatsError) {
          addLog(`Error updating monthly stats: ${updateStatsError.message}`)
        } else {
          addLog(`月度统计更新成功，年份: ${selectedYear}，月份: ${selectedMonth}`)
        }
      } else {
        // 创建月度统计
        const { data: newStats, error: statsError } = await supabase
          .from("monthly_stats")
          .insert({
            year: selectedYear,
            month: selectedMonth,
            total_participants: importData.stats.total_participants,
            total_books: importData.stats.total_books,
            active_readers: activeReaders,
            completed_books: completedBooks,
            created_at: targetDate.toISOString(),
          })
          .select()
          .single()

        if (statsError) {
          addLog(`Error importing monthly stats: ${statsError.message}`)
        } else {
          addLog(`月度统计创建成功，ID: ${newStats.id}`)
        }
      }

      addLog(`${sourceDescription}数据导入完成!`)
      setMessage(`${sourceDescription}数据导入成功!`)
    } catch (error) {
      console.error("Import error:", error)
      setMessage(`导入出错: ${error.message}`)
    } finally {
      setIsImporting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 container mx-auto py-8">
          <Card>
            <CardHeader>
              <CardTitle>导入数据</CardTitle>
              <CardDescription>正在加载数据...</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-10">
        <Card>
          <CardHeader>
            <CardTitle>导入数据</CardTitle>
            <CardDescription>上传并导入读书群数据到数据库中 - 管理员: {user?.email}</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="upload" className="space-y-4">
              <TabsList>
                <TabsTrigger value="upload">上传数据</TabsTrigger>
                <TabsTrigger value="preview">数据预览</TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="space-y-4">
                {isPreview && (
                  <Alert variant="warning" className="bg-amber-50 text-amber-800 border-amber-200">
                    <AlertDescription>
                      您正在预览模式下查看此页面。在预览模式下，数据导入功能被禁用，但您可以查看页面布局和设计。
                    </AlertDescription>
                  </Alert>
                )}

                {/* 文件上传区域 */}
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:bg-gray-50 transition-colors cursor-pointer"
                  onClick={handleUploadClick}
                >
                  <input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".json" className="hidden" />
                  <FileUp className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    {dataSource === "upload" ? "更换文件" : "选择JSON文件"}
                  </h3>
                  <p className="mt-1 text-xs text-gray-500">
                    {dataSource === "upload" ? `当前文件: ${fileName}` : "点击选择或拖放JSON文件到此处"}
                  </p>
                </div>

                {/* 日期选择区域 - 直接集成到上传数据页面 */}
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-3">选择数据月份</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">年份</label>
                      <Select
                        value={selectedYear.toString()}
                        onValueChange={(value) => setSelectedYear(Number.parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择年份" />
                        </SelectTrigger>
                        <SelectContent>
                          {yearOptions.map((year) => (
                            <SelectItem key={year} value={year.toString()}>
                              {year}年
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">月份</label>
                      <Select
                        value={selectedMonth.toString()}
                        onValueChange={(value) => setSelectedMonth(Number.parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择月份" />
                        </SelectTrigger>
                        <SelectContent>
                          {monthNames.map((name, index) => (
                            <SelectItem key={index} value={(index + 1).toString()}>
                              {name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    数据将被标记为 {selectedYear}年{monthNames[selectedMonth - 1]}
                  </p>
                </div>

                {/* 导入信息摘要 */}
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-2">导入信息:</h3>
                  <ul className="list-disc pl-5 text-sm">
                    <li>
                      目标时间: {selectedYear}年{monthNames[selectedMonth - 1]}
                    </li>
                    <li>
                      数据来源:{" "}
                      {dataSource === "file"
                        ? "预设文件"
                        : dataSource === "upload"
                          ? `上传的文件 (${fileName})`
                          : "示例数据"}
                    </li>
                  </ul>
                </div>
              </TabsContent>

              <TabsContent value="preview" className="space-y-4">
                {importData ? (
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="font-medium mb-2">数据概览:</h3>
                    <ul className="list-disc pl-5 text-sm">
                      <li>总参与人数: {importData.stats.total_participants}人</li>
                      <li>总书籍数量: {importData.stats.total_books}本</li>
                      <li>用户数量: {importData.users.length}人</li>
                      <li>书籍记录: {importData.users.reduce((acc, user) => acc + user.books.length, 0)}条</li>
                      <li>
                        时间标识: {selectedYear}年{monthNames[selectedMonth - 1]}
                      </li>
                    </ul>

                    <div className="mt-4">
                      <h4 className="font-medium mb-1">用户数据预览:</h4>
                      <div className="max-h-60 overflow-y-auto bg-white p-2 rounded border text-xs">
                        <pre>{JSON.stringify(importData.users.slice(0, 2), null, 2)}</pre>
                        {importData.users.length > 2 && <div className="text-center mt-2">...</div>}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-red-50 p-4 rounded-md text-red-700">无法加载数据</div>
                )}
              </TabsContent>
            </Tabs>

            <div className="mt-6 space-y-4">
              <Button
                onClick={handleImport}
                disabled={isImporting || isPreview || !importData}
                className="w-full flex items-center justify-center gap-2"
              >
                {isImporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>导入中...</span>
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4" />
                    <span>导入数据到数据库</span>
                  </>
                )}
              </Button>

              {message && (
                <div
                  className={`p-3 rounded-md ${
                    message.includes("成功")
                      ? "bg-green-50 text-green-700"
                      : message.includes("出错")
                        ? "bg-red-50 text-red-700"
                        : "bg-amber-50 text-amber-700"
                  }`}
                >
                  {message}
                </div>
              )}

              {logs.length > 0 && (
                <div className="mt-4">
                  <h3 className="font-medium mb-2">导入日志:</h3>
                  <div className="bg-gray-100 p-3 rounded-md max-h-64 overflow-y-auto font-mono text-xs">
                    {logs.map((log, index) => (
                      <div key={index} className="mb-1">
                        {log}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
