import { Header } from "@/components/header"
import { createClient } from "@/lib/supabase"
import { YearSelector } from "@/components/year-selector"
import { YearlyReadingTrend } from "@/components/charts/yearly-reading-trend"
import { CategoryDistribution } from "@/components/charts/category-distribution"
import { ReadingStatusChart } from "@/components/charts/reading-status-chart"
import { PopularBooksChart } from "@/components/charts/popular-books-chart"
import { MonthlyComparisonChart } from "@/components/charts/monthly-comparison-chart"

export default async function DashboardPage({
  searchParams,
}: {
  searchParams: { year?: string }
}) {
  const supabase = createClient()

  // 获取所有有数据的年份
  const { data: yearData } = await supabase.from("monthly_stats").select("year").order("year")

  const availableYears = Array.from(new Set(yearData?.map((item) => item.year) || [])).filter(Boolean) as number[]

  // 如果没有数据，默认显示当前年份
  if (availableYears.length === 0) {
    availableYears.push(new Date().getFullYear())
  }

  // 获取当前选择的年份（从URL参数或默认为最新年份）
  let selectedYear: number

  if (searchParams.year && !isNaN(Number(searchParams.year))) {
    selectedYear = Number(searchParams.year)
  } else {
    // 默认选择最新的年份
    selectedYear = Math.max(...availableYears)
  }

  // 获取前一年（如果存在）
  const previousYear = availableYears.includes(selectedYear - 1) ? selectedYear - 1 : undefined

  // 获取当前年份的月度数据
  const { data: currentYearMonthlyData } = await supabase
    .from("monthly_stats")
    .select("month, active_readers, completed_books")
    .eq("year", selectedYear)
    .order("month")

  // 获取前一年的月度数据（如果存在）
  let previousYearMonthlyData = undefined
  if (previousYear) {
    const { data } = await supabase
      .from("monthly_stats")
      .select("month, active_readers, completed_books")
      .eq("year", previousYear)
      .order("month")
    previousYearMonthlyData = data
  }

  // 获取分类分布数据 - 修复查询
  const startDate = new Date(selectedYear, 0, 1).toISOString()
  const endDate = new Date(selectedYear + 1, 0, 1).toISOString()

  const { data: categoryData } = await supabase
    .from("books")
    .select(`
      id,
      main_category_id,
      main_categories (
        id,
        name
      )
    `)
    .gte("created_at", startDate)
    .lt("created_at", endDate)

  // 手动计算分类统计
  const categoryMap = new Map()
  categoryData?.forEach((book) => {
    const categoryName = book.main_categories?.name || "未分类"
    if (!categoryMap.has(categoryName)) {
      categoryMap.set(categoryName, 0)
    }
    categoryMap.set(categoryName, categoryMap.get(categoryName) + 1)
  })

  const categoryDistribution = Array.from(categoryMap.entries())
    .map(([name, value]) => ({ name, value }))
    .filter((item) => item.name !== "未分类" || item.value > 0)

  // 获取阅读状态分布数据 - 修复查询
  const { data: readingRecords } = await supabase
    .from("reading_records")
    .select("reading_status")
    .gte("created_at", startDate)
    .lt("created_at", endDate)

  // 手动计算阅读状态统计
  const statusMap = new Map()
  readingRecords?.forEach((record) => {
    const status = record.reading_status || "未知"
    if (!statusMap.has(status)) {
      statusMap.set(status, 0)
    }
    statusMap.set(status, statusMap.get(status) + 1)
  })

  const statusDistribution = Array.from(statusMap.entries()).map(([name, value]) => ({ name, value }))

  // 获取热门书籍数据 - 修复查询
  const { data: bookRecords } = await supabase
    .from("reading_records")
    .select(`
      book_id,
      books (
        id,
        title
      )
    `)
    .gte("created_at", startDate)
    .lt("created_at", endDate)

  // 手动计算热门书籍
  const bookMap = new Map()
  bookRecords?.forEach((record) => {
    if (!record.books) return
    const bookTitle = record.books.title
    if (!bookMap.has(bookTitle)) {
      bookMap.set(bookTitle, 0)
    }
    bookMap.set(bookTitle, bookMap.get(bookTitle) + 1)
  })

  const popularBooks = Array.from(bookMap.entries())
    .map(([title, count]) => ({ title, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10)

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">数据分析仪表盘</h1>
          <YearSelector availableYears={availableYears} currentYear={selectedYear} />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 年度阅读趋势图表 */}
          <YearlyReadingTrend data={currentYearMonthlyData || []} year={selectedYear} />

          {/* 月度阅读对比图表 */}
          <MonthlyComparisonChart
            currentYearData={currentYearMonthlyData || []}
            previousYearData={previousYearMonthlyData}
            currentYear={selectedYear}
            previousYear={previousYear}
          />

          {/* 分类分布图表 */}
          <CategoryDistribution
            data={categoryDistribution}
            title={`${selectedYear}年分类分布`}
            description="阅读书籍的分类分布情况"
          />

          {/* 阅读状态分布图表 */}
          <ReadingStatusChart
            data={statusDistribution}
            title={`${selectedYear}年阅读状态`}
            description="不同阅读状态的书籍数量"
          />

          {/* 热门书籍排行图表 */}
          <PopularBooksChart data={popularBooks} title={`${selectedYear}年热门书籍`} description="阅读人数最多的书籍" />
        </div>
      </main>
      <footer className="border-t py-4 text-center text-sm text-gray-500">
        读书群数据分析 © {new Date().getFullYear()}
      </footer>
    </div>
  )
}
