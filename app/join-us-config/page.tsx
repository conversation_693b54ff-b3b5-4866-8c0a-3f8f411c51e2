"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { getSystemConfig, updateSystemConfig } from "@/lib/config"

export default function JoinUsConfigPage() {
  const [joinUsText, setJoinUsText] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<string | null>(null)

  // 加载数据
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const text = await getSystemConfig("join_us_text")
        setJoinUsText(text || "")
      } catch (err) {
        console.error("Error loading join us text:", err)
        setError("加载配置失败")
      } finally {
        setIsLoading(false)
      }
    }

    loadConfig()
  }, [])

  // 处理配置更新
  const handleUpdate = async () => {
    setMessage(null)
    setError(null)

    try {
      const success = await updateSystemConfig("join_us_text", joinUsText)

      if (success) {
        setMessage("加入我们文本已成功更新")
      } else {
        setError("更新配置失败")
      }
    } catch (err) {
      console.error("Error updating config:", err)
      setError("更新过程中发生错误")
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 container mx-auto py-8">
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-8 px-4">
        <Card>
          <CardHeader>
            <CardTitle>编辑"加入我们"文本</CardTitle>
            <CardDescription>修改点击"加入我们"按钮后显示的文本内容</CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {message && (
              <Alert className="mb-4 bg-green-50 text-green-700 border-green-200">
                <AlertDescription>{message}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground mb-2">
                  在下面编辑文本内容。您可以使用换行来格式化文本，使其在对话框中更易读。
                </p>
                <Textarea
                  value={joinUsText}
                  onChange={(e) => setJoinUsText(e.target.value)}
                  rows={10}
                  className="font-mono"
                />
              </div>
              <div className="pt-2">
                <Button onClick={handleUpdate}>更新文本</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
