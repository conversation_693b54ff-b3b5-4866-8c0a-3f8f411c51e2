"use server"

import { createClient } from "@/lib/supabase/server"

export async function fetchReadingRecords(params: {
  year?: number
  month?: number
  userId?: string
  searchTerm?: string
}) {
  try {
    const { year, month, userId, searchTerm } = params
    const supabase = createClient()

    // Build the base query
    let query = supabase.from("reading_records").select(`
      id,
      book_id,
      user_id,
      reading_status,
      progress,
      created_at,
      updated_at
    `)

    // Apply filters
    if (year) {
      const startDate = new Date(`${year}-01-01T00:00:00Z`)
      const endDate = new Date(`${year + 1}-01-01T00:00:00Z`)
      query = query.gte("created_at", startDate.toISOString()).lt("created_at", endDate.toISOString())
    }

    if (year && month) {
      const startDate = new Date(`${year}-${month.toString().padStart(2, "0")}-01T00:00:00Z`)
      const nextMonth = month === 12 ? 1 : month + 1
      const nextMonthYear = month === 12 ? year + 1 : year
      const endDate = new Date(`${nextMonthYear}-${nextMonth.toString().padStart(2, "0")}-01T00:00:00Z`)

      query = query.gte("created_at", startDate.toISOString()).lt("created_at", endDate.toISOString())
    }

    if (userId && userId !== "all") {
      query = query.eq("user_id", Number.parseInt(userId))
    }

    // Execute the query
    const { data: recordsData, error: recordsError } = await query

    if (recordsError) {
      console.error("Error fetching reading records:", recordsError)
      throw new Error(recordsError.message)
    }

    // Get book data
    const bookIds = recordsData?.map((record) => record.book_id).filter(Boolean) || []
    let booksData = []

    if (bookIds.length > 0) {
      const { data: books, error: booksError } = await supabase
        .from("books")
        .select(`
          id, 
          title, 
          main_category_id, 
          sub_category_id
        `)
        .in("id", bookIds)

      if (booksError) {
        console.error("Error fetching books:", booksError)
      } else {
        booksData = books || []
      }
    }

    // Get category data
    const mainCategoryIds = booksData.map((book) => book.main_category_id).filter(Boolean)
    const subCategoryIds = booksData.map((book) => book.sub_category_id).filter(Boolean)

    let mainCategoriesData = []
    let subCategoriesData = []

    if (mainCategoryIds.length > 0) {
      const { data: mainCategories, error: mainCategoriesError } = await supabase
        .from("main_categories")
        .select("id, name")
        .in("id", mainCategoryIds)

      if (mainCategoriesError) {
        console.error("Error fetching main categories:", mainCategoriesError)
      } else {
        mainCategoriesData = mainCategories || []
      }
    }

    if (subCategoryIds.length > 0) {
      const { data: subCategories, error: subCategoriesError } = await supabase
        .from("sub_categories")
        .select("id, name, main_category_id")
        .in("id", subCategoryIds)

      if (subCategoriesError) {
        console.error("Error fetching sub categories:", subCategoriesError)
      } else {
        subCategoriesData = subCategories || []
      }
    }

    // Get user data
    const userIds = recordsData?.map((record) => record.user_id).filter(Boolean) || []
    let usersData = []

    if (userIds.length > 0) {
      const { data: users, error: usersError } = await supabase.from("users").select("id, user_id").in("id", userIds)

      if (usersError) {
        console.error("Error fetching users:", usersError)
      } else {
        usersData = users || []
      }
    }

    // Combine data
    const enrichedRecords =
      recordsData?.map((record) => {
        const book = booksData.find((b) => b.id === record.book_id) || null
        let mainCategory = null
        let subCategory = null

        if (book) {
          mainCategory = mainCategoriesData.find((c) => c.id === book.main_category_id) || null
          subCategory = subCategoriesData.find((c) => c.id === book.sub_category_id) || null
        }

        const user = usersData.find((u) => u.id === record.user_id) || null

        return {
          id: record.id,
          book: book
            ? {
                id: book.id,
                title: book.title,
                main_category: mainCategory,
                sub_category: subCategory,
              }
            : null,
          user: user,
          reading_status: record.reading_status,
          progress: record.progress,
          created_at: record.created_at,
          updated_at: record.updated_at,
        }
      }) || []

    // Filter by search term if provided
    let filteredData = enrichedRecords
    if (searchTerm && searchTerm.trim() !== "") {
      const term = searchTerm.toLowerCase()
      filteredData = enrichedRecords.filter((record) => {
        return (
          record.book?.title?.toLowerCase().includes(term) ||
          record.book?.main_category?.name?.toLowerCase().includes(term) ||
          record.book?.sub_category?.name?.toLowerCase().includes(term) ||
          record.user?.user_id?.toLowerCase().includes(term)
        )
      })
    }

    // Get available years
    const { data: yearsData, error: yearsError } = await supabase
      .from("reading_records")
      .select("created_at")
      .order("created_at", { ascending: false })

    if (yearsError) {
      console.error("Error fetching years:", yearsError)
      throw new Error(yearsError.message)
    }

    // Extract years
    const years = Array.from(
      new Set(
        (yearsData || [])
          .filter((record) => record && record.created_at)
          .map((record) => new Date(record.created_at).getFullYear()),
      ),
    ).sort((a, b) => b - a)

    // Get all users
    const { data: allUsers, error: allUsersError } = await supabase
      .from("users")
      .select("id, user_id")
      .order("user_id", { ascending: true })

    if (allUsersError) {
      console.error("Error fetching all users:", allUsersError)
      throw new Error(allUsersError.message)
    }

    return {
      records: filteredData,
      years: years,
      users: allUsers || [],
    }
  } catch (error) {
    console.error("Error in fetchReadingRecords:", error)
    throw error
  }
}
