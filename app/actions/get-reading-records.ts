"use server"

import { createClient } from "@/lib/supabase/server"

export async function getReadingRecords(params: {
  year?: number
  month?: number
  userId?: string
  searchTerm?: string
}) {
  try {
    const { year, month, userId, searchTerm } = params
    const supabase = createClient()

    // 构建包含所有关联数据的查询
    let query = supabase.from("reading_records").select(`
      id,
      reading_status,
      progress,
      created_at,
      updated_at,
      users (
        id,
        user_id
      ),
      books (
        id,
        title,
        main_categories (
          id,
          name
        ),
        sub_categories (
          id,
          name
        )
      )
    `)

    // 应用过滤器
    if (year) {
      const startDate = new Date(`${year}-01-01T00:00:00Z`)
      const endDate = new Date(`${year + 1}-01-01T00:00:00Z`)
      query = query.gte("created_at", startDate.toISOString()).lt("created_at", endDate.toISOString())
    }

    if (year && month) {
      const startDate = new Date(`${year}-${month.toString().padStart(2, "0")}-01T00:00:00Z`)
      const nextMonth = month === 12 ? 1 : month + 1
      const nextMonthYear = month === 12 ? year + 1 : year
      const endDate = new Date(`${nextMonthYear}-${nextMonth.toString().padStart(2, "0")}-01T00:00:00Z`)

      query = query.gte("created_at", startDate.toISOString()).lt("created_at", endDate.toISOString())
    }

    if (userId && userId !== "all") {
      query = query.eq("user_id", Number.parseInt(userId))
    }

    // 执行查询
    const { data: recordsData, error: recordsError } = await query

    if (recordsError) {
      console.error("Error fetching reading records:", recordsError)
      throw new Error(recordsError.message)
    }

    // 过滤搜索词
    let filteredData = recordsData || []
    if (searchTerm && searchTerm.trim() !== "") {
      const term = searchTerm.toLowerCase()
      filteredData = filteredData.filter((record) => {
        return (
          record.books?.title?.toLowerCase().includes(term) ||
          record.books?.main_categories?.name?.toLowerCase().includes(term) ||
          record.books?.sub_categories?.name?.toLowerCase().includes(term) ||
          record.users?.user_id?.toLowerCase().includes(term)
        )
      })
    }

    // 获取可用年份
    const { data: yearsData, error: yearsError } = await supabase
      .from("reading_records")
      .select("created_at")
      .order("created_at", { ascending: false })

    if (yearsError) {
      console.error("Error fetching years:", yearsError)
      throw new Error(yearsError.message)
    }

    // 提取年份
    const years = Array.from(
      new Set(
        (yearsData || [])
          .filter((record) => record && record.created_at)
          .map((record) => new Date(record.created_at).getFullYear()),
      ),
    ).sort((a, b) => b - a)

    // 获取所有用户
    const { data: allUsers, error: allUsersError } = await supabase
      .from("users")
      .select("id, user_id")
      .order("user_id", { ascending: true })

    if (allUsersError) {
      console.error("Error fetching all users:", allUsersError)
      throw new Error(allUsersError.message)
    }

    return {
      records: filteredData,
      years: years,
      users: allUsers || [],
    }
  } catch (error) {
    console.error("Error in getReadingRecords:", error)
    throw error
  }
}
