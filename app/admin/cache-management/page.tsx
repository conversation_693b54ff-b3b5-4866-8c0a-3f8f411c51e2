"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, Database, Trash2, RefreshCw } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"

export default function CacheManagementPage() {
  const [cacheStats, setCacheStats] = useState<{ size: number; keys: string[] } | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isClearing, setIsClearing] = useState(false)

  // 获取缓存统计信息
  const fetchCacheStats = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/cache")
      if (!response.ok) {
        throw new Error("Failed to fetch cache stats")
      }
      const data = await response.json()
      setCacheStats(data)
    } catch (error) {
      console.error("Error fetching cache stats:", error)
      toast({
        title: "错误",
        description: "获取缓存统计信息失败",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 清除所有缓存
  const clearAllCache = async () => {
    setIsClearing(true)
    try {
      const response = await fetch("/api/cache", { method: "DELETE" })
      if (!response.ok) {
        throw new Error("Failed to clear cache")
      }
      await fetchCacheStats()
      toast({
        title: "成功",
        description: "所有缓存已清除",
      })
    } catch (error) {
      console.error("Error clearing cache:", error)
      toast({
        title: "错误",
        description: "清除缓存失败",
        variant: "destructive",
      })
    } finally {
      setIsClearing(false)
    }
  }

  // 清除特定缓存
  const clearCacheItem = async (key: string) => {
    try {
      const response = await fetch(`/api/cache?key=${encodeURIComponent(key)}`, { method: "DELETE" })
      if (!response.ok) {
        throw new Error(`Failed to clear cache for key: ${key}`)
      }
      await fetchCacheStats()
      toast({
        title: "成功",
        description: `缓存项 "${key}" 已清除`,
      })
    } catch (error) {
      console.error("Error clearing cache item:", error)
      toast({
        title: "错误",
        description: `清除缓存项失败: ${key}`,
        variant: "destructive",
      })
    }
  }

  // 初始加载
  useEffect(() => {
    fetchCacheStats()
  }, [])

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <Toaster />
      <main className="flex-1 container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">缓存管理</h1>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={fetchCacheStats}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
              刷新
            </Button>
            <Button
              variant="destructive"
              onClick={clearAllCache}
              disabled={isLoading || isClearing || !cacheStats?.size}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              清除所有缓存
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              缓存统计
            </CardTitle>
            <CardDescription>当前缓存项数量: {isLoading ? "加载中..." : cacheStats?.size || 0}</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : !cacheStats?.keys.length ? (
              <div className="text-center py-8 text-muted-foreground">缓存为空</div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>缓存键</TableHead>
                      <TableHead className="w-24">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {cacheStats.keys.map((key) => (
                      <TableRow key={key}>
                        <TableCell className="font-mono text-sm">{key}</TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm" onClick={() => clearCacheItem(key)} className="h-8 w-8 p-0">
                            <Trash2 className="h-4 w-4 text-red-500" />
                            <span className="sr-only">删除</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
