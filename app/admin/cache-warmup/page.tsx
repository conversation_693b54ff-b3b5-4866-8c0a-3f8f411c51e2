"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2, Flame, RefreshCw, CheckCircle, XCircle, Clock } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// 预热状态类型
type WarmupStatus = {
  isWarming: boolean
  lastWarmedAt: string | null
  completedItems: string[]
  failedItems: string[]
  inProgress: string[]
}

// 预热配置类型
type WarmupConfig = {
  enabled: boolean
  months: number
  years: number[]
  retryDelay: number
  maxRetries: number
  debug: boolean
}

export default function CacheWarmupPage() {
  const [status, setStatus] = useState<WarmupStatus | null>(null)
  const [config, setConfig] = useState<WarmupConfig | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isStarting, setIsStarting] = useState(false)

  // 编辑配置状态
  const [editConfig, setEditConfig] = useState<WarmupConfig | null>(null)
  const [isEditing, setIsEditing] = useState(false)

  // 获取预热状态
  const fetchStatus = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/cache/warmup")
      if (!response.ok) {
        throw new Error("Failed to fetch warmup status")
      }
      const data = await response.json()
      setStatus(data.status)
      setConfig(data.config)

      // 初始化编辑配置
      if (!editConfig) {
        setEditConfig(data.config)
      }
    } catch (error) {
      console.error("Error fetching warmup status:", error)
      toast({
        title: "错误",
        description: "获取预热状态失败",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 启动预热
  const startWarmup = async (force = false) => {
    setIsStarting(true)
    try {
      const response = await fetch("/api/cache/warmup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          force,
          config: isEditing ? editConfig : undefined,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to start warmup")
      }

      const data = await response.json()
      setStatus(data.status)

      if (data.success) {
        toast({
          title: "成功",
          description: "缓存预热已启动",
        })

        // 如果正在编辑配置，退出编辑模式
        if (isEditing) {
          setIsEditing(false)
          setConfig(editConfig)
        }

        // 定期刷新状态，直到预热完成
        const checkInterval = setInterval(async () => {
          const statusResponse = await fetch("/api/cache/warmup")
          const statusData = await statusResponse.json()
          setStatus(statusData.status)

          // 如果预热完成，停止检查
          if (!statusData.status.isWarming) {
            clearInterval(checkInterval)
            toast({
              title: "完成",
              description: "缓存预热已完成",
            })
          }
        }, 2000)
      } else {
        toast({
          title: "提示",
          description: data.message,
        })
      }
    } catch (error) {
      console.error("Error starting warmup:", error)
      toast({
        title: "错误",
        description: "启动预热失败",
        variant: "destructive",
      })
    } finally {
      setIsStarting(false)
    }
  }

  // 处理配置变更
  const handleConfigChange = (key: keyof WarmupConfig, value: any) => {
    if (!editConfig) return

    setEditConfig({
      ...editConfig,
      [key]: value,
    })
  }

  // 处理年份变更
  const handleYearChange = (year: number, checked: boolean) => {
    if (!editConfig) return

    const years = [...editConfig.years]

    if (checked && !years.includes(year)) {
      years.push(year)
    } else if (!checked && years.includes(year)) {
      const index = years.indexOf(year)
      years.splice(index, 1)
    }

    setEditConfig({
      ...editConfig,
      years,
    })
  }

  // 取消编辑
  const cancelEdit = () => {
    setIsEditing(false)
    setEditConfig(config)
  }

  // 初始加载
  useEffect(() => {
    fetchStatus()

    // 定期刷新状态
    const interval = setInterval(fetchStatus, 10000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <Toaster />
      <main className="flex-1 container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">缓存预热管理</h1>
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchStatus} disabled={isLoading} className="flex items-center gap-2">
              <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
              刷新
            </Button>
            <Button
              onClick={() => startWarmup(true)}
              disabled={isLoading || isStarting}
              className="flex items-center gap-2"
            >
              <Flame className="h-4 w-4" />
              {isStarting ? "启动中..." : "启动预热"}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 预热状态卡片 */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Flame className="h-5 w-5" />
                预热状态
              </CardTitle>
              <CardDescription>
                {status?.isWarming
                  ? "预热正在进行中..."
                  : status?.lastWarmedAt
                    ? `上次预热完成时间: ${new Date(status.lastWarmedAt).toLocaleString()}`
                    : "尚未进行预热"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                </div>
              ) : !status ? (
                <div className="text-center py-8 text-muted-foreground">无法获取预热状态</div>
              ) : (
                <Tabs defaultValue="summary">
                  <TabsList className="mb-4">
                    <TabsTrigger value="summary">摘要</TabsTrigger>
                    <TabsTrigger value="completed">已完成 ({status.completedItems.length})</TabsTrigger>
                    <TabsTrigger value="failed">失败 ({status.failedItems.length})</TabsTrigger>
                    {status.inProgress.length > 0 && (
                      <TabsTrigger value="inProgress">进行中 ({status.inProgress.length})</TabsTrigger>
                    )}
                  </TabsList>

                  <TabsContent value="summary">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="pt-6">
                          <div className="flex flex-col items-center">
                            <CheckCircle className="h-8 w-8 text-green-500 mb-2" />
                            <div className="text-2xl font-bold">{status.completedItems.length}</div>
                            <div className="text-sm text-muted-foreground">已完成项目</div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="pt-6">
                          <div className="flex flex-col items-center">
                            <XCircle className="h-8 w-8 text-red-500 mb-2" />
                            <div className="text-2xl font-bold">{status.failedItems.length}</div>
                            <div className="text-sm text-muted-foreground">失败项目</div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="pt-6">
                          <div className="flex flex-col items-center">
                            <Clock className="h-8 w-8 text-blue-500 mb-2" />
                            <div className="text-2xl font-bold">{status.inProgress.length}</div>
                            <div className="text-sm text-muted-foreground">进行中项目</div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="mt-6">
                      <div className="text-sm font-medium mb-2">预热进度</div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-blue-600 h-2.5 rounded-full"
                          style={{
                            width: `${
                              status.isWarming
                                ? Math.round(
                                    (status.completedItems.length /
                                      (status.completedItems.length +
                                        status.inProgress.length +
                                        status.failedItems.length)) *
                                      100,
                                  )
                                : 100
                            }%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="completed">
                    {status.completedItems.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">暂无已完成项目</div>
                    ) : (
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>缓存键</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {status.completedItems.map((key) => (
                              <TableRow key={key}>
                                <TableCell className="font-mono text-sm">{key}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="failed">
                    {status.failedItems.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">暂无失败项目</div>
                    ) : (
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>缓存键</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {status.failedItems.map((key) => (
                              <TableRow key={key}>
                                <TableCell className="font-mono text-sm">{key}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="inProgress">
                    {status.inProgress.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">暂无进行中项目</div>
                    ) : (
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>缓存键</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {status.inProgress.map((key) => (
                              <TableRow key={key}>
                                <TableCell className="font-mono text-sm">{key}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
          </Card>

          {/* 预热配置卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                  <circle cx="12" cy="12" r="3" />
                </svg>
                预热配置
              </CardTitle>
              <CardDescription>配置缓存预热的行为</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                </div>
              ) : !config ? (
                <div className="text-center py-8 text-muted-foreground">无法获取预热配置</div>
              ) : (
                <div className="space-y-4">
                  {isEditing ? (
                    <>
                      {/* 编辑模式 */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="enabled">启用预热</Label>
                          <Switch
                            id="enabled"
                            checked={editConfig?.enabled}
                            onCheckedChange={(checked) => handleConfigChange("enabled", checked)}
                          />
                        </div>

                        <div className="space-y-1">
                          <Label htmlFor="months">预热最近几个月</Label>
                          <Input
                            id="months"
                            type="number"
                            min="1"
                            max="12"
                            value={editConfig?.months}
                            onChange={(e) => handleConfigChange("months", Number.parseInt(e.target.value))}
                          />
                          <p className="text-xs text-muted-foreground">设置为0表示预热所有月份</p>
                        </div>

                        <div className="space-y-1">
                          <Label>预热年份</Label>
                          <div className="grid grid-cols-2 gap-2">
                            {Array.from({ length: 3 }, (_, i) => new Date().getFullYear() - i).map((year) => (
                              <div key={year} className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  id={`year-${year}`}
                                  checked={editConfig?.years.includes(year)}
                                  onChange={(e) => handleYearChange(year, e.target.checked)}
                                  className="h-4 w-4 rounded border-gray-300"
                                />
                                <label htmlFor={`year-${year}`} className="text-sm">
                                  {year}年
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="space-y-1">
                          <Label htmlFor="retryDelay">重试延迟 (毫秒)</Label>
                          <Input
                            id="retryDelay"
                            type="number"
                            min="1000"
                            step="1000"
                            value={editConfig?.retryDelay}
                            onChange={(e) => handleConfigChange("retryDelay", Number.parseInt(e.target.value))}
                          />
                        </div>

                        <div className="space-y-1">
                          <Label htmlFor="maxRetries">最大重试次数</Label>
                          <Input
                            id="maxRetries"
                            type="number"
                            min="0"
                            max="10"
                            value={editConfig?.maxRetries}
                            onChange={(e) => handleConfigChange("maxRetries", Number.parseInt(e.target.value))}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <Label htmlFor="debug">调试模式</Label>
                          <Switch
                            id="debug"
                            checked={editConfig?.debug}
                            onCheckedChange={(checked) => handleConfigChange("debug", checked)}
                          />
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      {/* 查看模式 */}
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">启用预热:</span>
                          <span className="text-sm">{config.enabled ? "是" : "否"}</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-sm font-medium">预热最近几个月:</span>
                          <span className="text-sm">{config.months}</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-sm font-medium">预热年份:</span>
                          <span className="text-sm">{config.years.sort().join(", ")}</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-sm font-medium">重试延迟:</span>
                          <span className="text-sm">{config.retryDelay}ms</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-sm font-medium">最大重试次数:</span>
                          <span className="text-sm">{config.maxRetries}</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-sm font-medium">调试模式:</span>
                          <span className="text-sm">{config.debug ? "开启" : "关闭"}</span>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              {isEditing ? (
                <>
                  <Button variant="outline" onClick={cancelEdit}>
                    取消
                  </Button>
                  <Button onClick={() => startWarmup(false)}>保存并应用</Button>
                </>
              ) : (
                <Button onClick={() => setIsEditing(true)} disabled={isLoading}>
                  编辑配置
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      </main>
    </div>
  )
}
