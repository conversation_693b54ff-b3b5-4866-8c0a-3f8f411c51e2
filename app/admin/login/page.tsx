"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Lock, Info } from "lucide-react"

export default function AdminLoginPage() {
  const [email, setEmail] = useState("<EMAIL>")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [message, setMessage] = useState<string | null>(null)
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirectTo = searchParams.get("redirectTo") || "/admin"

  // 检测是否在预览模式
  useEffect(() => {
    const checkPreviewMode = () => {
      const isPreview =
        window.location.hostname.includes("vercel.app") ||
        window.location.hostname.includes("localhost") ||
        window.location.search.includes("preview=true") ||
        window.location.hostname.includes("vusercontent.net")

      setIsPreviewMode(isPreview)

      if (isPreview) {
        setMessage("预览模式下，请使用以下测试账号登录：\nemail: <EMAIL>\npassword: password123")
        // 预填充测试账号
        setPassword("password123")
        // 注意：不再覆盖邮箱，保留默认值 <EMAIL>
      }
    }

    checkPreviewMode()
  }, [])

  // 修改登录逻辑，使用Supabase客户端进行登录，而不是手动处理预览模式

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // 检查是否在预览模式
      if (isPreviewMode) {
        // 设置模拟登录状态
        const user = { id: "mock-user-id", email, role: "admin" }
        const session = {
          access_token: "mock-token",
          expires_at: Date.now() + 24 * 60 * 60 * 1000,
          user,
        }

        localStorage.setItem("preview_auth", JSON.stringify({ user, session }))

        // 延迟一下，模拟网络请求
        await new Promise((resolve) => setTimeout(resolve, 500))

        // 登录成功，重定向到管理面板
        router.push(redirectTo)
        return
      }

      // 非预览模式下的正常登录逻辑
      const { createClient } = await import("@/lib/supabase/client")
      const supabase = createClient()

      const { error: loginError } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (loginError) {
        throw loginError
      }

      // 登录成功，重定向到之前尝试访问的页面或管理员面板
      router.push(redirectTo)
      router.refresh()
    } catch (err) {
      console.error("Login error:", err)
      setError(err.message || "登录失败，请检查您的凭据")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">管理员登录</CardTitle>
          <CardDescription>请输入您的管理员凭据以访问管理功能</CardDescription>
        </CardHeader>
        <CardContent>
          {isPreviewMode && (
            <Alert className="mb-4 bg-blue-50 border-blue-200">
              <Info className="h-4 w-4 text-blue-600 mr-2" />
              <AlertDescription className="text-blue-700 whitespace-pre-line">{message}</AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">邮箱</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <Button className="w-full" type="submit" disabled={loading}>
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  <span>登录中...</span>
                </div>
              ) : (
                <div className="flex items-center">
                  <Lock className="h-4 w-4 mr-2" />
                  <span>登录</span>
                </div>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
