import { MonthlyReportUploader } from "@/components/monthly-report-uploader"

export default function MonthlyReportsPage() {
  // 计算默认月份（当前月份减1）
  const currentDate = new Date()
  const currentMonth = currentDate.getMonth() + 1 // JavaScript月份从0开始
  const previousMonth = currentMonth === 1 ? 12 : currentMonth - 1

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">月度报告管理 - {previousMonth}月</h1>
      <p className="text-muted-foreground mb-8">
        在这里上传和管理Markdown格式的月度阅读报告，这些报告将显示在相应月份的详情页面。
      </p>

      <MonthlyReportUploader />
    </div>
  )
}
