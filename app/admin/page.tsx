"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Upload, Database, LogOut, FileText, Settings } from "lucide-react"
import { createClient } from "@/lib/supabase/client"

export default function AdminPage() {
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createClient()
        const { data } = await supabase.auth.getUser()

        if (!data.user) {
          // 未登录，重定向到登录页面
          router.push("/login")
          return
        }

        setUser(data.user)
      } catch (error) {
        console.error("Auth check error:", error)
        router.push("/login")
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [router])

  const handleLogout = async () => {
    try {
      setLoading(true)
      const supabase = createClient()

      // 调用Supabase的登出方法
      await supabase.auth.signOut()

      // 手动清除cookie和localStorage
      if (typeof window !== "undefined") {
        // 清除auth cookie
        document.cookie = "sb-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax"

        // 清除localStorage中的会话数据
        localStorage.removeItem("sb-auth-token")
        localStorage.removeItem("supabase.auth.token")
      }

      // 重定向到登录页面
      router.push("/login")
    } catch (error) {
      console.error("Logout error:", error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="flex-1 container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">管理员面板</h1>
          <p className="text-muted-foreground">欢迎回来，{user?.email || "管理员"}</p>
        </div>
        <Button variant="outline" onClick={handleLogout} className="flex items-center gap-2">
          <LogOut className="h-4 w-4" />
          退出登录
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5 text-blue-600" />
              数据导入
            </CardTitle>
            <CardDescription>上传和导入读书群数据</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">上传JSON格式的读书群数据，并将其导入到数据库中。</p>
            <Button asChild className="w-full">
              <Link href="/import-data">进入数据导入</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-amber-600" />
              月度报告管理
            </CardTitle>
            <CardDescription>上传和管理月度报告</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">
              上传Markdown格式的月度阅读报告，并将其显示在月度详情页面。
            </p>
            <Button asChild className="w-full">
              <Link href="/admin/routes/monthly-reports">上传月度报告</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-green-600" />
              数据检查
            </CardTitle>
            <CardDescription>检查和管理数据库记录</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">
              查看、检查和管理数据库中的记录，包括月度统计和阅读记录。
            </p>
            <Button asChild variant="outline" className="w-full">
              <Link href="/data-inspector">进入数据检查</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-indigo-600" />
              缓存管理
            </CardTitle>
            <CardDescription>管理系统缓存</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">查看和管理系统缓存，清除过期缓存，提高系统性能。</p>
            <Button asChild variant="outline" className="w-full">
              <Link href="/admin/cache-management">缓存管理</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-purple-600" />
              网站设置
            </CardTitle>
            <CardDescription>管理网站参数和配置</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">
              管理网站的相关设置，如读书群总人数、网站标题和其他系统参数。
            </p>
            <Button asChild variant="outline" className="w-full">
              <Link href="/system-config">系统配置</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
