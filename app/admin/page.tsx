"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Upload, Database, LogOut, FileText, Wrench, BarChart2 } from "lucide-react"

export default function AdminPage() {
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 检查是否在预览模式
        const isPreviewMode =
          window.location.hostname.includes("vercel.app") ||
          window.location.hostname.includes("localhost") ||
          window.location.hostname.includes("vusercontent.net") ||
          window.location.search.includes("preview=true")

        if (isPreviewMode) {
          // 在预览模式下，检查localStorage中是否有模拟登录状态
          const previewAuth = localStorage.getItem("preview_auth")
          if (previewAuth) {
            const { user } = JSON.parse(previewAuth)
            setUser(user)
            setLoading(false)
            return
          } else {
            // 如果没有模拟登录状态，自动创建一个
            const mockUser = { id: "mock-user-id", email: "<EMAIL>", role: "admin" }
            const mockSession = {
              access_token: "mock-token",
              expires_at: Date.now() + 24 * 60 * 60 * 1000,
              user: mockUser,
            }

            localStorage.setItem("preview_auth", JSON.stringify({ user: mockUser, session: mockSession }))
            setUser(mockUser)
            setLoading(false)
            return
          }
        }

        // 非预览模式下的正常身份验证逻辑
        const { createClient } = await import("@supabase/supabase-js")
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

        if (!supabaseUrl || !supabaseKey) {
          throw new Error("Supabase环境变量未设置")
        }

        const supabase = createClient(supabaseUrl, supabaseKey)

        const {
          data: { user },
        } = await supabase.auth.getUser()

        if (!user) {
          // 未登录，重定向到登录页面
          router.push("/login")
          return
        }

        setUser(user)
      } catch (error) {
        console.error("Auth check error:", error)
        router.push("/login")
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [router])

  const handleLogout = async () => {
    try {
      // 检查是否在预览模式
      const isPreviewMode =
        window.location.hostname.includes("vercel.app") ||
        window.location.hostname.includes("localhost") ||
        window.location.hostname.includes("vusercontent.net") ||
        window.location.search.includes("preview=true")

      if (isPreviewMode) {
        // 在预览模式下，清除localStorage中的模拟登录状态
        localStorage.removeItem("preview_auth")
        router.push("/login")
        return
      }

      // 非预览模式下的正常登出逻辑
      const { createClient } = await import("@supabase/supabase-js")
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      const supabase = createClient(supabaseUrl, supabaseKey)

      await supabase.auth.signOut()
      router.push("/login")
    } catch (error) {
      console.error("Logout error:", error)
    }
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="flex-1 container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">管理员面板</h1>
          <p className="text-muted-foreground">欢迎回来，{user?.email || "管理员"}</p>
        </div>
        <Button variant="outline" onClick={handleLogout} className="flex items-center gap-2">
          <LogOut className="h-4 w-4" />
          退出登录
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5 text-blue-600" />
              数据导入
            </CardTitle>
            <CardDescription>上传和导入读书群数据</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">上传JSON格式的读书群数据，并将其导入到数据库中。</p>
            <Button asChild className="w-full">
              <Link href="/import-data">进入数据导入</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-amber-600" />
              月度报告管理
            </CardTitle>
            <CardDescription>上传和管理月度报告</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">
              上传Markdown格式的月度阅读报告，并将其显示在月度详情页面。
            </p>
            <Button asChild className="w-full">
              <Link href="/admin/upload-report">上传月度报告</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-green-600" />
              数据检查
            </CardTitle>
            <CardDescription>检查和管理数据库记录</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">
              查看、检查和管理数据库中的记录，包括月度统计和阅读记录。
            </p>
            <Button asChild variant="outline" className="w-full">
              <Link href="/data-inspector">进入数据检查</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart2 className="h-5 w-5 text-purple-600" />
              数据分析
            </CardTitle>
            <CardDescription>查看数据分析仪表盘</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">查看读书群数据的分析图表和统计信息。</p>
            <Button asChild variant="outline" className="w-full">
              <Link href="/dashboard">查看仪表盘</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-indigo-600" />
              缓存管理
            </CardTitle>
            <CardDescription>管理系统缓存</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">查看和管理系统缓存，清除过期缓存，提高系统性��。</p>
            <Button asChild variant="outline" className="w-full">
              <Link href="/admin/cache-management">缓存管理</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5 text-orange-600" />
              数据诊断
            </CardTitle>
            <CardDescription>诊断数据问题</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">
              运行诊断工具，检查数据完整性和可用性，排查数据加载问题。
            </p>
            <Button asChild variant="outline" className="w-full">
              <Link href="/admin/data-diagnostics">数据诊断</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
