"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, Database, RefreshCw, AlertCircle, CheckCircle } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { createClient } from "@supabase/supabase-js"

export default function DataDiagnosticsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [diagnosticResults, setDiagnosticResults] = useState<any>(null)
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear())
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1)
  const [availableYears, setAvailableYears] = useState<number[]>([])
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false)

  // 月份名称
  const monthNames = [
    "一月",
    "二月",
    "三月",
    "四月",
    "五月",
    "六月",
    "七月",
    "八月",
    "九月",
    "十月",
    "十一月",
    "十二月",
  ]

  // 初始化
  useEffect(() => {
    const fetchAvailableYears = async () => {
      try {
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        const supabase = createClient(supabaseUrl, supabaseKey)

        const { data, error } = await supabase.from("monthly_stats").select("year").order("year", { ascending: false })

        if (error) throw error

        // 提取不重复的年份
        const years = [...new Set(data?.map((item) => item.year) || [])]
        setAvailableYears(years.length > 0 ? years : [new Date().getFullYear()])
      } catch (error) {
        console.error("Error fetching available years:", error)
        toast({
          title: "错误",
          description: "获取可用年份失败",
          variant: "destructive",
        })
        setAvailableYears([new Date().getFullYear()])
      } finally {
        setIsLoading(false)
      }
    }

    fetchAvailableYears()
  }, [])

  // 运行诊断
  const runDiagnostics = async () => {
    setIsRunningDiagnostics(true)
    setDiagnosticResults(null)

    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      const supabase = createClient(supabaseUrl, supabaseKey)

      // 获取指定年月的数据
      const startDate = new Date(selectedYear, selectedMonth - 1, 1).toISOString()
      const endDate = new Date(selectedYear, selectedMonth, 0).toISOString() // 当月最后一天

      // 1. 检查月度统计数据
      const { data: monthlyStats, error: monthlyStatsError } = await supabase
        .from("monthly_stats")
        .select("*")
        .eq("year", selectedYear)
        .eq("month", selectedMonth)
        .single()

      // 2. 检查阅读记录数据
      const { data: readingRecords, error: readingRecordsError } = await supabase
        .from("reading_records")
        .select("id")
        .gte("created_at", startDate)
        .lte("created_at", endDate)
        .order("id")

      // 3. 检查用户数据
      const { data: users, error: usersError } = await supabase
        .from("reading_records")
        .select(`
          user_id,
          users (
            id,
            user_id
          )
        `)
        .gte("created_at", startDate)
        .lte("created_at", endDate)
        .order("user_id")

      // 4. 检查书籍类别数据
      const { data: categories, error: categoriesError } = await supabase
        .from("reading_records")
        .select(`
          books (
            main_category_id,
            main_categories (
              id,
              name
            )
          )
        `)
        .gte("created_at", startDate)
        .lte("created_at", endDate)

      // 5. 检查桑基图API
      let sankeyApiResponse = null
      let sankeyApiError = null
      try {
        const response = await fetch(`/api/sankey-data/${selectedYear}/${selectedMonth}`)
        sankeyApiResponse = await response.json()
      } catch (error) {
        sankeyApiError = error
      }

      // 汇总诊断结果
      const results = {
        timestamp: new Date().toISOString(),
        year: selectedYear,
        month: selectedMonth,
        monthlyStats: {
          exists: !!monthlyStats,
          error: monthlyStatsError ? monthlyStatsError.message : null,
          data: monthlyStats,
        },
        readingRecords: {
          count: readingRecords?.length || 0,
          error: readingRecordsError ? readingRecordsError.message : null,
        },
        users: {
          count: users?.length || 0,
          error: usersError ? usersError.message : null,
        },
        categories: {
          count: categories?.filter((c) => c.books?.main_categories).length || 0,
          error: categoriesError ? categoriesError.message : null,
        },
        sankeyApi: {
          success: !sankeyApiError && sankeyApiResponse,
          error: sankeyApiError ? (sankeyApiError instanceof Error ? sankeyApiError.message : "Unknown error") : null,
          nodes: sankeyApiResponse?.nodes?.length || 0,
          links: sankeyApiResponse?.links?.length || 0,
          response: sankeyApiResponse,
        },
      }

      setDiagnosticResults(results)
      toast({
        title: "诊断完成",
        description: `${selectedYear}年${monthNames[selectedMonth - 1]}的数据诊断已完成`,
      })
    } catch (error) {
      console.error("Error running diagnostics:", error)
      toast({
        title: "诊断失败",
        description: error instanceof Error ? error.message : "运行诊断时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsRunningDiagnostics(false)
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <Toaster />
      <main className="flex-1 container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">数据诊断工具</h1>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>运行诊断</CardTitle>
            <CardDescription>选择年份和月份，检查数据完整性和可用性</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              <div className="w-full md:w-1/3">
                <Select
                  value={selectedYear.toString()}
                  onValueChange={(value) => setSelectedYear(Number.parseInt(value))}
                  disabled={isRunningDiagnostics}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择年份" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableYears.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}年
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full md:w-1/3">
                <Select
                  value={selectedMonth.toString()}
                  onValueChange={(value) => setSelectedMonth(Number.parseInt(value))}
                  disabled={isRunningDiagnostics}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择月份" />
                  </SelectTrigger>
                  <SelectContent>
                    {monthNames.map((name, index) => (
                      <SelectItem key={index} value={(index + 1).toString()}>
                        {name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full md:w-1/3">
                <Button onClick={runDiagnostics} disabled={isRunningDiagnostics} className="w-full">
                  {isRunningDiagnostics ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      正在诊断...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      运行诊断
                    </>
                  )}
                </Button>
              </div>
            </div>

            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : diagnosticResults ? (
              <div className="space-y-6">
                <div className="text-sm text-muted-foreground">
                  诊断时间: {new Date(diagnosticResults.timestamp).toLocaleString()}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* 月度统计检查 */}
                  <Card className={diagnosticResults.monthlyStats.exists ? "border-green-200" : "border-amber-200"}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-base">月度统计</CardTitle>
                        {diagnosticResults.monthlyStats.exists ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-amber-500" />
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm">
                        {diagnosticResults.monthlyStats.exists ? (
                          <p className="text-green-600">数据存在</p>
                        ) : (
                          <p className="text-amber-600">数据不存在</p>
                        )}
                        {diagnosticResults.monthlyStats.error && (
                          <p className="text-red-500 mt-2">{diagnosticResults.monthlyStats.error}</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* 阅读记录检查 */}
                  <Card
                    className={diagnosticResults.readingRecords.count > 0 ? "border-green-200" : "border-amber-200"}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-base">阅读记录</CardTitle>
                        {diagnosticResults.readingRecords.count > 0 ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-amber-500" />
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm">
                        <p className={diagnosticResults.readingRecords.count > 0 ? "text-green-600" : "text-amber-600"}>
                          记录数量: {diagnosticResults.readingRecords.count}
                        </p>
                        {diagnosticResults.readingRecords.error && (
                          <p className="text-red-500 mt-2">{diagnosticResults.readingRecords.error}</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* 用户数据检查 */}
                  <Card className={diagnosticResults.users.count > 0 ? "border-green-200" : "border-amber-200"}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-base">用户数据</CardTitle>
                        {diagnosticResults.users.count > 0 ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-amber-500" />
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm">
                        <p className={diagnosticResults.users.count > 0 ? "text-green-600" : "text-amber-600"}>
                          用户数量: {diagnosticResults.users.count}
                        </p>
                        {diagnosticResults.users.error && (
                          <p className="text-red-500 mt-2">{diagnosticResults.users.error}</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* 类别数据检查 */}
                  <Card className={diagnosticResults.categories.count > 0 ? "border-green-200" : "border-amber-200"}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-base">类别数据</CardTitle>
                        {diagnosticResults.categories.count > 0 ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-amber-500" />
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm">
                        <p className={diagnosticResults.categories.count > 0 ? "text-green-600" : "text-amber-600"}>
                          类别数量: {diagnosticResults.categories.count}
                        </p>
                        {diagnosticResults.categories.error && (
                          <p className="text-red-500 mt-2">{diagnosticResults.categories.error}</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* 桑基图API检查 */}
                <Card className={diagnosticResults.sankeyApi.success ? "border-green-200" : "border-red-200"}>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle>桑基图API</CardTitle>
                      {diagnosticResults.sankeyApi.success ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                    <CardDescription>
                      {diagnosticResults.sankeyApi.success
                        ? `API返回了${diagnosticResults.sankeyApi.nodes}个节点和${diagnosticResults.sankeyApi.links}个链接`
                        : "API返回错误"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {diagnosticResults.sankeyApi.error ? (
                      <div className="bg-red-50 p-4 rounded-md text-red-700 mb-4">
                        <p className="font-medium">错误信息:</p>
                        <p className="mt-1">{diagnosticResults.sankeyApi.error}</p>
                      </div>
                    ) : null}

                    {diagnosticResults.sankeyApi.nodes === 0 && diagnosticResults.sankeyApi.success ? (
                      <div className="bg-amber-50 p-4 rounded-md text-amber-700 mb-4">
                        <p className="font-medium">警告:</p>
                        <p className="mt-1">API返回了空数据（没有节点和链接）</p>
                      </div>
                    ) : null}

                    <div className="mt-4">
                      <Button
                        variant="outline"
                        onClick={() => {
                          console.log("Sankey API Response:", diagnosticResults.sankeyApi.response)
                          toast({
                            title: "API响应已记录",
                            description: "详细信息已输出到控制台",
                          })
                        }}
                      >
                        <Database className="h-4 w-4 mr-2" />
                        查看完整API响应
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* 诊断结论 */}
                <Card>
                  <CardHeader>
                    <CardTitle>诊断结论</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      // 检查是否有数据
                      const hasData = diagnosticResults.readingRecords.count > 0

                      // 检查API是否成功但没有返回节点
                      const apiSuccessButEmpty =
                        diagnosticResults.sankeyApi.success && diagnosticResults.sankeyApi.nodes === 0

                      // 检查API是否失败
                      const apiFailed = !diagnosticResults.sankeyApi.success

                      if (!hasData) {
                        return (
                          <div className="bg-amber-50 p-4 rounded-md text-amber-700">
                            <p className="font-medium">没有找到数据</p>
                            <p className="mt-2">
                              {selectedYear}年{monthNames[selectedMonth - 1]}没有阅读记录数据。
                              这是正常的，如果这个月份确实没有数据。
                            </p>
                          </div>
                        )
                      } else if (apiSuccessButEmpty) {
                        return (
                          <div className="bg-amber-50 p-4 rounded-md text-amber-700">
                            <p className="font-medium">数据格式问题</p>
                            <p className="mt-2">
                              有阅读记录数据，但API无法生成有效的桑基图数据。
                              这可能是因为数据格式不正确或者缺少必要的关联。
                            </p>
                          </div>
                        )
                      } else if (apiFailed) {
                        return (
                          <div className="bg-red-50 p-4 rounded-md text-red-700">
                            <p className="font-medium">API错误</p>
                            <p className="mt-2">桑基图API返回错误。请检查API日志和错误信息。</p>
                          </div>
                        )
                      } else {
                        return (
                          <div className="bg-green-50 p-4 rounded-md text-green-700">
                            <p className="font-medium">数据正常</p>
                            <p className="mt-2">
                              {selectedYear}年{monthNames[selectedMonth - 1]}的数据完整且可用。 桑基图API返回了
                              {diagnosticResults.sankeyApi.nodes}个节点和
                              {diagnosticResults.sankeyApi.links}个链接。
                            </p>
                          </div>
                        )
                      }
                    })()}
                  </CardContent>
                </Card>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">选择年份和月份，然后点击"运行诊断"按钮</div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
