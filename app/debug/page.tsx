"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Info, AlertTriangle } from "lucide-react"

export default function DebugPage() {
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [authStatus, setAuthStatus] = useState<string>("检查中...")
  const router = useRouter()

  useEffect(() => {
    // 检查是否在预览模式
    const isPreview =
      window.location.hostname.includes("vercel.app") ||
      window.location.hostname.includes("localhost") ||
      window.location.hostname.includes("vusercontent.net") ||
      window.location.search.includes("preview=true")

    setIsPreviewMode(isPreview)

    // 检查是否有预览模式的身份验证
    const previewAuth = localStorage.getItem("preview_auth")
    if (previewAuth) {
      setAuthStatus("已登录（预览模式）")
    } else {
      setAuthStatus("未登录")
    }
  }, [])

  const setupPreviewAuth = () => {
    // 设置预览模式的身份验证
    const user = { id: "mock-user-id", email: "<EMAIL>", role: "admin" }
    const session = {
      access_token: "mock-token",
      expires_at: Date.now() + 24 * 60 * 60 * 1000,
      user,
    }

    localStorage.setItem("preview_auth", JSON.stringify({ user, session }))
    setAuthStatus("已登录（预览模式）")
  }

  const clearPreviewAuth = () => {
    // 清除预览模式的身份验证
    localStorage.removeItem("preview_auth")
    setAuthStatus("未登录")
  }

  const goToAdmin = () => {
    // 跳转到管理面板
    router.push("/admin")
  }

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>调试页面</CardTitle>
          <CardDescription>用于调试预览模式下的身份验证问题</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert className={isPreviewMode ? "bg-green-50 border-green-200" : "bg-yellow-50 border-yellow-200"}>
            {isPreviewMode ? (
              <Info className="h-4 w-4 text-green-600 mr-2" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
            )}
            <AlertDescription className={isPreviewMode ? "text-green-700" : "text-yellow-700"}>
              {isPreviewMode ? "当前处于预览模式" : "当前不在预览模式"}
            </AlertDescription>
          </Alert>

          <div className="p-4 border rounded-md">
            <p className="font-medium mb-2">身份验证状态：</p>
            <p className={authStatus.includes("已登录") ? "text-green-600" : "text-red-600"}>{authStatus}</p>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <Button onClick={setupPreviewAuth} className="w-full">
              设置预览模式身份验证
            </Button>
            <Button onClick={clearPreviewAuth} variant="outline" className="w-full">
              清除预览模式身份验证
            </Button>
            <Button onClick={goToAdmin} className="w-full bg-blue-600 hover:bg-blue-700">
              前往管理面板
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
