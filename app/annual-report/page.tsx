"use client"

import { useState, useEffect } from "react"
import { createClient } from "@supabase/supabase-js"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Header } from "@/components/header"

export default function AnnualReportPage() {
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear())
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [yearlyData, setYearlyData] = useState<any>(null)
  const [availableYears, setAvailableYears] = useState<number[]>([])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

        if (!supabaseUrl || !supabaseKey) {
          throw new Error("Supabase环境变量未设置")
        }

        const supabase = createClient(supabaseUrl, supabaseKey)

        // 获取可用的年份
        const { data: yearsData, error: yearsError } = await supabase
          .from("monthly_stats")
          .select("year")
          .order("year", { ascending: false })

        if (yearsError) throw new Error(`获取年份数据失败: ${yearsError.message}`)

        // 提取不重复的年份
        const years = [...new Set(yearsData?.map((item) => item.year) || [])]
        setAvailableYears(years.length > 0 ? years : [new Date().getFullYear()])

        // 如果没有选择年份或选择的年份不在可用年份中，则使用最新的年份
        if (!selectedYear || !years.includes(selectedYear)) {
          setSelectedYear(years[0] || new Date().getFullYear())
        }

        // 获取选定年份的数据
        const { data: yearData, error: yearDataError } = await supabase
          .from("monthly_stats")
          .select("*")
          .eq("year", selectedYear)
          .order("month", { ascending: true })

        if (yearDataError) throw new Error(`获取年度数据失败: ${yearDataError.message}`)
        setYearlyData(yearData || [])
      } catch (err) {
        console.error("Error fetching data:", err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [selectedYear])

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">年度报告</h1>
          <div className="w-40">
            <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(Number.parseInt(value))}>
              <SelectTrigger>
                <SelectValue placeholder="选择年份" />
              </SelectTrigger>
              <SelectContent>
                {availableYears.map((year) => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}年
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 text-red-700 p-4 rounded-md mb-6">加载数据时出错: {error}</div>
        ) : (
          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>{selectedYear}年度阅读概览</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-center justify-center bg-gray-50 rounded-md">
                  <p className="text-muted-foreground">年度报告开发中...</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{selectedYear}年度月度数据</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-center justify-center bg-gray-50 rounded-md">
                  <p className="text-muted-foreground">月度数据图表开发中...</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </main>
    </div>
  )
}
