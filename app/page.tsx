import { Header } from "@/components/header"
import { YearlyStats } from "@/components/yearly-stats"
import { SimpleRelationshipChart } from "@/components/charts/simple-relationship-chart"
import { CategoryPieChart } from "@/components/charts/category-pie-chart"
import { CategoryUserDistributionChart } from "@/components/charts/category-user-distribution-chart"
import { ReaderLeaderboard } from "@/components/reader-leaderboard"
import { MonthSelector } from "@/components/month-selector"
import { JoinUsDialog } from "@/components/join-us-dialog"
import Link from "next/link"
import { Trophy, Calendar, Search, BookOpen, Share2, ExternalLink } from "lucide-react"
import { createClient } from "@/lib/supabase/server"

// 获取年度排行榜数据
async function getYearlyLeaderboardData(year: number) {
  try {
    // 创建 Supabase 客户端
    const supabase = createClient()

    // 设置年度日期范围
    const startDate = new Date(year, 0, 1).toISOString() // 1月1日
    const endDate = new Date(year, 11, 31, 23, 59, 59).toISOString() // 12月31日

    console.log(`获取${year}年排行榜数据，时间范围: ${startDate} 到 ${endDate}`)

    // 获取指定年份的阅读记录
    const { data: readingRecords, error: recordsError } = await supabase
      .from("reading_records")
      .select(`
        id,
        user_id,
        book_id,
        reading_status,
        progress,
        users (id, user_id),
        books (
          id,
          title,
          main_category_id,
          main_categories (id, name)
        )
      `)
      .gte("created_at", startDate)
      .lte("created_at", endDate)

    if (recordsError) {
      console.error("Error fetching reading records:", recordsError)
      return []
    }

    if (!readingRecords || readingRecords.length === 0) {
      console.log(`${year}年没有阅读记录数据`)
      return []
    }

    console.log(`获取到 ${readingRecords.length} 条阅读记录`)

    // 使用与月度页面相同的逻辑计算用户阅读排行
    // 使用 Map 来确保每个用户的每本书只计算一次
    const userBookMap = new Map()

    // 首先，为每个用户创建一个唯一书籍ID的集合
    readingRecords.forEach((record) => {
      const userId = record.user_id
      const userName = record.users?.user_id || `用户${userId}`
      const bookId = record.book_id
      const isCompleted = record.reading_status === "读完"

      if (!userBookMap.has(userName)) {
        userBookMap.set(userName, {
          name: userName,
          avatar: userName.charAt(0),
          books: new Set(),
          completedBooks: new Set(),
        })
      }

      const userStats = userBookMap.get(userName)
      userStats.books.add(bookId)
      if (isCompleted) {
        userStats.completedBooks.add(bookId)
      }
    })

    // 然后，将集合大小转换为计数
    const userReadingMap = new Map()
    userBookMap.forEach((stats, userName) => {
      userReadingMap.set(userName, {
        name: stats.name,
        avatar: stats.avatar,
        totalBooks: stats.books.size,
        completedBooks: stats.completedBooks.size,
      })
    })

    // 转换用户阅读排行为数组格式
    const topReaders = Array.from(userReadingMap.values())
      .map((user) => ({
        ...user,
        completionRate: user.totalBooks > 0 ? Number(((user.completedBooks / user.totalBooks) * 100).toFixed(1)) : 0,
      }))
      .sort((a, b) => b.totalBooks - a.totalBooks)
      .map((user, index) => ({
        ...user,
        rank: index + 1,
      }))

    return topReaders
  } catch (error) {
    console.error("Error in getYearlyLeaderboardData:", error)
    return []
  }
}

// 获取年度类别分布数据
async function getYearlyCategoryData(year: number) {
  try {
    // 创建 Supabase 客户端
    const supabase = createClient()

    // 设置年度日期范围
    const startDate = new Date(year, 0, 1).toISOString() // 1月1日
    const endDate = new Date(year, 11, 31, 23, 59, 59).toISOString() // 12月31日

    console.log(`获取${year}年类别分布数据，时间范围: ${startDate} 到 ${endDate}`)

    // 获取指定年份的阅读记录
    const { data: readingRecords, error: recordsError } = await supabase
      .from("reading_records")
      .select(`
        id,
        user_id,
        book_id,
        reading_status,
        progress,
        users (id, user_id),
        books (
          id,
          title,
          main_category_id,
          main_categories (id, name)
        )
      `)
      .gte("created_at", startDate)
      .lte("created_at", endDate)

    if (recordsError) {
      console.error("Error fetching reading records:", recordsError)
      return { categoryDistribution: [], categoryUserDistribution: [] }
    }

    if (!readingRecords || readingRecords.length === 0) {
      console.log(`${year}年没有阅读记录数据`)
      return { categoryDistribution: [], categoryUserDistribution: [] }
    }

    console.log(`获取到 ${readingRecords.length} 条阅读记录`)

    // 计算总书籍数
    const totalBooks = readingRecords.length

    // 3. 计算类别分布
    const categoryMap = new Map()
    const categoryUserMap = new Map()

    readingRecords.forEach((record) => {
      if (record.books && record.books.main_categories) {
        const categoryId = record.books.main_category_id
        const categoryName = record.books.main_categories.name
        const userId = record.user_id
        const userName = record.users?.user_id || `用户${userId}`

        // 更新类别统计
        if (categoryMap.has(categoryName)) {
          categoryMap.set(categoryName, categoryMap.get(categoryName) + 1)
        } else {
          categoryMap.set(categoryName, 1)
        }

        // 更新类别-用户分布
        if (!categoryUserMap.has(categoryName)) {
          categoryUserMap.set(categoryName, new Map())
        }

        const userMap = categoryUserMap.get(categoryName)
        if (userMap.has(userName)) {
          userMap.set(userName, userMap.get(userName) + 1)
        } else {
          userMap.set(userName, 1)
        }
      }
    })

    // 预定义一组丰富多彩的颜色
    const colorPalette = [
      "#8884d8",
      "#82ca9d",
      "#ffc658",
      "#0088FE",
      "#FF8042",
      "#FFBB28",
      "#00C49F",
      "#a4de6c",
      "#d0ed57",
      "#83a6ed",
      "#8dd1e1",
      "#6b486b",
      "#a05d56",
      "#d0743c",
      "#ff8c00",
      "#98abc5",
      "#7b6888",
      "#6b486b",
      "#a05d56",
      "#d0743c",
    ]

    // 添加丰富的类别颜色映射
    const categoryColorMap: Record<string, string> = {
      文学类: "#FF6384",
      哲学类: "#36A2EB",
      社会科学类: "#FFCE56",
      历史类: "#4BC0C0",
      科学技术类: "#9966FF",
      其他: "#C9CBCF",
      文学: "#FF6384",
      小说: "#FF6384",
      历史: "#4BC0C0",
      科学: "#9966FF",
      哲学: "#36A2EB",
      心理学: "#FFBB28",
      经济: "#FFCE56",
      管理: "#00C49F",
      艺术: "#FF6384",
      社会科学: "#FFCE56",
      政治: "#FFCE56",
      传记: "#4BC0C0",
      技术: "#9966FF",
      教育: "#36A2EB",
      健康: "#00C49F",
      旅行: "#4BC0C0",
      科幻: "#9966FF",
      奇幻: "#FF6384",
      悬疑: "#FFBB28",
      儿童: "#00C49F",
      漫画: "#FF6384",
      诗歌: "#FF6384",
      散文: "#FF6384",
      杂志: "#C9CBCF",
      计算机: "#9966FF",
      科技: "#9966FF",
      宗教与哲学: "#36A2EB",
      个人发展: "#36A2EB",
    }

    // 根据类别名称获取颜色
    function getCategoryColor(category: string): string {
      // 如果类别在映射中存在，直接返回对应颜色
      if (categoryColorMap[category]) {
        return categoryColorMap[category]
      }

      // 否则根据类别名称生成哈希值，选择一个颜色
      let hash = 0
      for (let i = 0; i < category.length; i++) {
        hash = category.charCodeAt(i) + ((hash << 5) - hash)
      }

      // 使用哈希值选择颜色
      const index = Math.abs(hash) % colorPalette.length
      return colorPalette[index]
    }

    // 转换类别分布为数组格式，使用新的颜色分配函数
    const categoryDistribution = Array.from(categoryMap.entries()).map(([name, value]) => {
      const percentage = (((value as number) / totalBooks) * 100).toFixed(1) + "%"
      return {
        name,
        value,
        percentage,
        color: getCategoryColor(name), // 使用新的颜色分配函数
      }
    })

    // 转换类别-用户分布为数组格式，使用新的颜色分配函数
    const categoryUserDistribution = Array.from(categoryUserMap.entries()).map(([category, userMap]) => {
      const users = Array.from(userMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([name, count], index) => ({
          id: `user${index + 1}`,
          name,
          count,
        }))

      return {
        name: category,
        color: getCategoryColor(category), // 使用新的颜色分配函数
        users,
        totalBooks: categoryMap.get(category),
      }
    })

    // 按书籍数量排序
    categoryUserDistribution.sort((a, b) => b.totalBooks - a.totalBooks)

    return { categoryDistribution, categoryUserDistribution }
  } catch (error) {
    console.error("Error in getYearlyCategoryData:", error)
    return { categoryDistribution: [], categoryUserDistribution: [] }
  }
}

export default async function HomePage() {
  // 获取当前日期
  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  const currentMonth = currentDate.getMonth() + 1 // JavaScript 月份从 0 开始

  // 计算默认显示的月份（当前月份的前一个月）
  let defaultYear = currentYear
  let defaultMonth = currentMonth - 1

  // 如果是1月，则默认显示上一年的12月
  if (defaultMonth === 0) {
    defaultYear = currentYear - 1
    defaultMonth = 12
  }

  // 可用的年份和月份数据
  const availableYears = [2025, 2024, 2023]
  const availableMonths = [
    { year: 2025, months: Array.from({ length: 12 }, (_, i) => i + 1) }, // 1-12月
    { year: 2024, months: Array.from({ length: 12 }, (_, i) => i + 1) }, // 1-12月
    { year: 2023, months: Array.from({ length: 12 }, (_, i) => i + 1) }, // 1-12月
  ]

  // 从数据库获取年度排行榜数据
  const leaderboardData = await getYearlyLeaderboardData(currentYear)

  // 从数据库获取年度类别分布数据
  const { categoryDistribution, categoryUserDistribution } = await getYearlyCategoryData(currentYear)





  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-4 px-4 md:py-8 md:px-0">
        {/* 欢迎信息 */}
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">欢迎使用读书群数据分析系统</h1>
            <p className="text-sm md:text-base text-muted-foreground">
              汇集了读书群的各项数据统计和分析，帮助您了解群内阅读情况和趋势。
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <JoinUsDialog />
          </div>
        </div>

        {/* 月度数据快速导航、阅读数据查询和资源分享 */}
        <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-3">
          {/* 月度数据快速导航 */}
          <div className="p-4 bg-gray-50 border rounded-lg">
            <div>
              <h2 className="text-lg md:text-xl font-bold flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-rose-600" />
                月度分析报告
              </h2>
              <p className="text-xs md:text-sm text-muted-foreground mt-1">
                选择年份和月份，快速查看详细的月度阅读数据分析
              </p>
            </div>

            <div className="mt-4">
              <MonthSelector
                availableYears={availableYears}
                availableMonths={availableMonths}
                defaultYear={defaultYear}
                defaultMonth={defaultMonth}
              />
            </div>

            <p className="text-xs md:text-sm text-muted-foreground mt-4">
              月度数据包含详细的阅读统计、用户活跃度分析、类别分布和完整的月度报告。
            </p>
          </div>

          {/* 阅读数据查询 */}
          <div className="p-4 bg-gray-50 border rounded-lg">
            <div>
              <h2 className="text-lg md:text-xl font-bold flex items-center">
                <Search className="h-5 w-5 mr-2 text-blue-600" />
                阅读数据查询
              </h2>
              <p className="text-xs md:text-sm text-muted-foreground mt-1">按时间和用户筛选，查询详细的阅读记录</p>
            </div>

            {/* 添加与左侧卡片相同的空间 - 下拉选择器高度 + 上下间距 */}
            <div className="mt-4 h-[48px]"></div>

            <Link href="/reading-query" className="block">
              <div className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-center transition-colors">
                <BookOpen className="h-4 w-4 inline-block mr-2" />
                进入查询页面
              </div>
            </Link>

            <p className="text-xs md:text-sm text-muted-foreground mt-4">
              通过筛选条件，查询特定时间段内特定用户的阅读书籍记录，支持按年份、月份和用户进行多维度筛选。
            </p>
          </div>

          {/* 资源分享 */}
          <div className="p-4 bg-gray-50 border rounded-lg">
            <div>
              <h2 className="text-lg md:text-xl font-bold flex items-center">
                <Share2 className="h-5 w-5 mr-2 text-green-600" />
                资源分享
              </h2>
              <p className="text-xs md:text-sm text-muted-foreground mt-1">探索阅读路书，获取精选阅读资源和知识图谱</p>
            </div>

            {/* 添加与其他卡片相同的空间 */}
            <div className="mt-4 h-[48px]"></div>

            <Link href="https://readmap.inklinks.io/" target="_blank" rel="noopener noreferrer" className="block">
              <div className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md text-center transition-colors">
                <ExternalLink className="h-4 w-4 inline-block mr-2" />
                访问阅读路书
              </div>
            </Link>

            <p className="text-xs md:text-sm text-muted-foreground mt-4">
              阅读路书提供科学化的阅读指南、知识图谱和优质阅读资源，帮助您更高效地规划阅读路径。
            </p>
          </div>
        </div>

        {/* 年度数据 */}
        <div className="mb-8">
          <h2 className="text-xl md:text-2xl font-bold mb-4">年度数据</h2>
          <YearlyStats year={currentYear} />
        </div>

        {/* 1. 阅读趋势图表 - 替换为月度页面中的用户与类别关系流图 */}
        <div className="mb-8">
          <h2 className="text-xl md:text-2xl font-bold mb-4">阅读趋势</h2>
          <div className="grid grid-cols-1 gap-6">
            <SimpleRelationshipChart
              title="用户与类别关系流图"
              subtitle="展示用户阅读不同类别书籍的历史累计流量分布"
              year={defaultYear}
              month={defaultMonth}
            />
          </div>
        </div>

        {/* 2. 图书类别分析 - 修改为上下布局 */}
        <div className="mb-8">
          <h2 className="text-xl md:text-2xl font-bold mb-4">图书类别分析</h2>
          <div className="space-y-6">
            {/* 图书类别分布 */}
            <div className="w-full">
              <CategoryPieChart
                data={categoryDistribution}
                title="图书类别分布"
                subtitle={`${currentYear}年按类别统计的书籍数量`}
                height={450}
              />
            </div>

            {/* 图书类别用户分布 */}
            <div className="w-full">
              <CategoryUserDistributionChart
                data={categoryUserDistribution}
                title="图书类别用户分布"
                subtitle={`${currentYear}年各类别中的阅读用户分布`}
              />
            </div>
          </div>
        </div>

        {/* 3. 阅读排行榜 */}
        <div className="mb-8">
          <h2 className="text-xl md:text-2xl font-bold mb-4 flex items-center">
            <Trophy className="h-5 w-5 md:h-6 md:w-6 mr-2 text-amber-500" />
            阅读排行榜
          </h2>
          <ReaderLeaderboard
            readers={leaderboardData}
            title={`${currentYear}年度阅读之星`}
            subtitle="阅读量最多的成员"
            showCount={20}
            pageSize={20}
          />
        </div>
      </main>
    </div>
  )
}
