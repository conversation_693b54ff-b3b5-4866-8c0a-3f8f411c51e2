import { Header } from "@/components/header"
import { YearlyStats } from "@/components/yearly-stats"
import { SimpleRelationshipChart } from "@/components/charts/simple-relationship-chart"
import { CategoryPieChart } from "@/components/charts/category-pie-chart"
import { CategoryUserDistributionChart } from "@/components/charts/category-user-distribution-chart"
import { ReaderLeaderboard } from "@/components/reader-leaderboard"
import { MonthSelector } from "@/components/month-selector"
import { JoinUsDialog } from "@/components/join-us-dialog"
import Link from "next/link"
import { Trophy, Calendar, Search, BookOpen, Share2, ExternalLink } from "lucide-react"

export default async function HomePage() {
  // 获取当前年份
  const currentYear = new Date().getFullYear()

  // 图书类别分布数据
  const categoryData = [
    { name: "文学类", value: 62, percentage: "48.8%", color: "#FF6384" },
    { name: "哲学类", value: 23, percentage: "18.1%", color: "#36A2EB" },
    { name: "社会科学类", value: 12, percentage: "9.4%", color: "#FFCE56" },
    { name: "历史类", value: 4, percentage: "3.1%", color: "#4BC0C0" },
    { name: "科学技术类", value: 4, percentage: "3.1%", color: "#9966FF" },
    { name: "其他", value: 22, percentage: "17.3%", color: "#C9CBCF" },
  ]

  // 类别用户分布数据
  const categoryUserData = [
    {
      name: "文学类",
      color: "#FF6384",
      users: [
        { id: "user1", name: "小菜鸡", count: 18 },
        { id: "user2", name: "Chris.W", count: 10 },
        { id: "user3", name: "娃娃", count: 7 },
        { id: "user4", name: "梦田", count: 6 },
        { id: "user5", name: "杜佳霖", count: 5 },
        { id: "user6", name: "李明", count: 4 },
        { id: "user7", name: "王晓", count: 3 },
        { id: "user8", name: "张伟", count: 3 },
        { id: "user9", name: "刘芳", count: 2 },
        { id: "user10", name: "陈晨", count: 2 },
        { id: "user11", name: "赵云", count: 1 },
        { id: "user12", name: "黄河", count: 1 },
      ],
      totalBooks: 62,
    },
    {
      name: "哲学类",
      color: "#36A2EB",
      users: [
        { id: "user1", name: "小菜鸡", count: 8 },
        { id: "user4", name: "梦田", count: 5 },
        { id: "user2", name: "Chris.W", count: 4 },
        { id: "user8", name: "张伟", count: 3 },
        { id: "user7", name: "王晓", count: 2 },
        { id: "user13", name: "周周", count: 1 },
      ],
      totalBooks: 23,
    },
    {
      name: "社会科学类",
      color: "#FFCE56",
      users: [
        { id: "user1", name: "小菜鸡", count: 5 },
        { id: "user6", name: "李明", count: 3 },
        { id: "user5", name: "杜佳霖", count: 2 },
        { id: "user9", name: "刘芳", count: 1 },
        { id: "user10", name: "陈晨", count: 1 },
      ],
      totalBooks: 12,
    },
    {
      name: "历史类",
      color: "#4BC0C0",
      users: [
        { id: "user2", name: "Chris.W", count: 2 },
        { id: "user1", name: "小菜鸡", count: 1 },
        { id: "user13", name: "周周", count: 1 },
      ],
      totalBooks: 4,
    },
    {
      name: "科学技术类",
      color: "#9966FF",
      users: [
        { id: "user1", name: "小菜鸡", count: 2 },
        { id: "user2", name: "Chris.W", count: 1 },
        { id: "user11", name: "赵云", count: 1 },
      ],
      totalBooks: 4,
    },
    {
      name: "其他",
      color: "#C9CBCF",
      users: [
        { id: "user1", name: "小菜鸡", count: 11 },
        { id: "user2", name: "Chris.W", count: 2 },
        { id: "user3", name: "娃娃", count: 2 },
        { id: "user5", name: "杜佳霖", count: 2 },
        { id: "user6", name: "李明", count: 2 },
        { id: "user7", name: "王晓", count: 1 },
        { id: "user8", name: "张伟", count: 1 },
        { id: "user9", name: "刘芳", count: 1 },
      ],
      totalBooks: 22,
    },
  ]

  // 阅读排行榜数据
  const leaderboardData = [
    {
      rank: 1,
      name: "小菜鸡",
      avatar: "小",
      totalBooks: 45,
      completedBooks: 21,
      completionRate: 46.7,
    },
    {
      rank: 2,
      name: "Chris.W",
      avatar: "C",
      totalBooks: 19,
      completedBooks: 12,
      completionRate: 63.2,
    },
    {
      rank: 3,
      name: "娃娃",
      avatar: "娃",
      totalBooks: 9,
      completedBooks: 0,
      completionRate: 0,
    },
    {
      rank: 4,
      name: "梦田",
      avatar: "梦",
      totalBooks: 8,
      completedBooks: 0,
      completionRate: 0,
    },
    {
      rank: 5,
      name: "杜佳霖",
      avatar: "杜",
      totalBooks: 8,
      completedBooks: 0,
      completionRate: 0,
    },
    {
      rank: 6,
      name: "李明",
      avatar: "李",
      totalBooks: 7,
      completedBooks: 3,
      completionRate: 42.9,
    },
    {
      rank: 7,
      name: "王晓",
      avatar: "王",
      totalBooks: 7,
      completedBooks: 2,
      completionRate: 28.6,
    },
    {
      rank: 8,
      name: "张伟",
      avatar: "张",
      totalBooks: 6,
      completedBooks: 4,
      completionRate: 66.7,
    },
    {
      rank: 9,
      name: "刘芳",
      avatar: "刘",
      totalBooks: 5,
      completedBooks: 3,
      completionRate: 60.0,
    },
    {
      rank: 10,
      name: "陈晨",
      avatar: "陈",
      totalBooks: 5,
      completedBooks: 1,
      completionRate: 20.0,
    },
    {
      rank: 11,
      name: "赵云",
      avatar: "赵",
      totalBooks: 4,
      completedBooks: 2,
      completionRate: 50.0,
    },
    {
      rank: 12,
      name: "黄河",
      avatar: "黄",
      totalBooks: 4,
      completedBooks: 0,
      completionRate: 0,
    },
    {
      rank: 13,
      name: "周周",
      avatar: "周",
      totalBooks: 3,
      completedBooks: 3,
      completionRate: 100.0,
    },
    {
      rank: 14,
      name: "吴越",
      avatar: "吴",
      totalBooks: 3,
      completedBooks: 1,
      completionRate: 33.3,
    },
    {
      rank: 15,
      name: "郑和",
      avatar: "郑",
      totalBooks: 2,
      completedBooks: 2,
      completionRate: 100.0,
    },
  ]

  // 可用的年份和月份数据
  const availableYears = [2025, 2024, 2023]
  const availableMonths = [
    { year: 2025, months: [1, 2, 3] },
    { year: 2024, months: [10, 11, 12] },
    { year: 2023, months: [9, 10, 11, 12] },
  ]

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-4 px-4 md:py-8 md:px-0">
        {/* 欢迎信息 */}
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">欢迎使用读书群数据分析系统</h1>
            <p className="text-sm md:text-base text-muted-foreground">
              汇集了读书群的各项数据统计和分析，帮助您了解群内阅读情况和趋势。
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <JoinUsDialog />
          </div>
        </div>

        {/* 月度数据快速导航、阅读数据查询和资源分享 */}
        <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-3">
          {/* 月度数据快速导航 */}
          <div className="p-4 bg-gray-50 border rounded-lg">
            <div>
              <h2 className="text-lg md:text-xl font-bold flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-rose-600" />
                月度数据快速导航
              </h2>
              <p className="text-xs md:text-sm text-muted-foreground mt-1">
                选择年份和月份，快速查看详细的月度阅读数据分析
              </p>
            </div>

            <div className="mt-4">
              <MonthSelector
                availableYears={availableYears}
                availableMonths={availableMonths}
                defaultYear={2025}
                defaultMonth={1}
              />
            </div>

            <p className="text-xs md:text-sm text-muted-foreground mt-4">
              月度数据包含详细的阅读统计、用户活跃度分析、类别分布和完整的月度报告。
            </p>
          </div>

          {/* 阅读数据查询 */}
          <div className="p-4 bg-gray-50 border rounded-lg">
            <div>
              <h2 className="text-lg md:text-xl font-bold flex items-center">
                <Search className="h-5 w-5 mr-2 text-blue-600" />
                阅读数据查询
              </h2>
              <p className="text-xs md:text-sm text-muted-foreground mt-1">按时间和用户筛选，查询详细的阅读记录</p>
            </div>

            {/* 添加与左侧卡片相同的空间 - 下拉选择器高度 + 上下间距 */}
            <div className="mt-4 h-[48px]"></div>

            <Link href="/reading-query" className="block">
              <div className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-center transition-colors">
                <BookOpen className="h-4 w-4 inline-block mr-2" />
                进入查询页面
              </div>
            </Link>

            <p className="text-xs md:text-sm text-muted-foreground mt-4">
              通过筛选条件，查询特定时间段内特定用户的阅读书籍记录，支持按年份、月份和用户进行多维度筛选。
            </p>
          </div>

          {/* 资源分享 */}
          <div className="p-4 bg-gray-50 border rounded-lg">
            <div>
              <h2 className="text-lg md:text-xl font-bold flex items-center">
                <Share2 className="h-5 w-5 mr-2 text-green-600" />
                资源分享
              </h2>
              <p className="text-xs md:text-sm text-muted-foreground mt-1">探索阅读路书，获取精选阅读资源和知识图谱</p>
            </div>

            {/* 添加与其他卡片相同的空间 */}
            <div className="mt-4 h-[48px]"></div>

            <Link href="https://readmap.inklinks.io/" target="_blank" rel="noopener noreferrer" className="block">
              <div className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md text-center transition-colors">
                <ExternalLink className="h-4 w-4 inline-block mr-2" />
                访问阅读路书
              </div>
            </Link>

            <p className="text-xs md:text-sm text-muted-foreground mt-4">
              阅读路书提供科学化的阅读指南、知识图谱和优质阅读资源，帮助您更高效地规划阅读路径。
            </p>
          </div>
        </div>

        {/* 年度数据 */}
        <div className="mb-8">
          <h2 className="text-xl md:text-2xl font-bold mb-4">年度数据</h2>
          <YearlyStats year={currentYear} />
        </div>

        {/* 1. 阅读趋势图表 */}
        <div className="mb-8">
          <h2 className="text-xl md:text-2xl font-bold mb-4">阅读趋势</h2>
          <div className="grid grid-cols-1 gap-6">
            <SimpleRelationshipChart title="用户与类别关系流图" subtitle="展示用户阅读不同类别书籍的历史累计流量分布" />
          </div>
        </div>

        {/* 2. 图书类别分布 */}
        <div className="mb-8">
          <h2 className="text-xl md:text-2xl font-bold mb-4">图书类别分析</h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <CategoryPieChart data={categoryData} title="图书类别分布" subtitle="按类别统计的书籍数量" height={350} />
            <CategoryUserDistributionChart
              data={categoryUserData}
              title="图书类别用户分布"
              subtitle="各类别中的阅读用户分布"
            />
          </div>
        </div>

        {/* 3. 阅读排行榜 */}
        <div className="mb-8">
          <h2 className="text-xl md:text-2xl font-bold mb-4 flex items-center">
            <Trophy className="h-5 w-5 md:h-6 md:w-6 mr-2 text-amber-500" />
            阅读排行榜
          </h2>
          <ReaderLeaderboard readers={leaderboardData} title="年度阅读之星" subtitle="阅读量最多的成员" />
        </div>
      </main>
    </div>
  )
}
