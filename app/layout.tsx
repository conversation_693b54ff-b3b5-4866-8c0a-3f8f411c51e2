import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { MobileBottomNav } from "@/components/mobile-bottom-nav"
import { Footer } from "@/components/footer"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "书读经典读书群 - 数据分析系统",
  description: "读书群数据分析与统计系统",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={`${inter.className} pb-[72px] md:pb-0 flex flex-col min-h-screen`}>
        <div className="flex-grow">{children}</div>
        <Footer />
        <MobileBottomNav />
      </body>
    </html>
  )
}
