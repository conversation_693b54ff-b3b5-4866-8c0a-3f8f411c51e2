import { Header } from "@/components/header"
import { MonthlyReportViewer } from "@/components/monthly-report-viewer"
import { Card, CardContent } from "@/components/ui/card"
import { CategoryPieChart } from "@/components/charts/category-pie-chart"
import { ReaderLeaderboard } from "@/components/reader-leaderboard"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { BookOpen, Users, BookMarked, BarChart2, FileText, Trophy, Loader2, ChevronLeft } from "lucide-react"
import { notFound } from "next/navigation"
import { SimpleRelationshipChart } from "@/components/charts/simple-relationship-chart"
import { CategoryUserDistributionChart } from "@/components/charts/category-user-distribution-chart"
import { createClient } from "@/lib/supabase/server"
import Link from "next/link"
import { MonthSelector } from "@/components/month-selector"

// 月份名称
const monthNames = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]

// 将现有的 categoryColorMap 替换为以下内容，与主页保持一致

// 添加丰富的类别颜色映射
const categoryColorMap: Record<string, string> = {
  文学类: "#FF6384",
  哲学类: "#36A2EB",
  社会科学类: "#FFCE56",
  历史类: "#4BC0C0",
  科学技术类: "#9966FF",
  其他: "#C9CBCF",
  文学: "#FF6384",
  小说: "#FF6384",
  历史: "#4BC0C0",
  科学: "#9966FF",
  哲学: "#36A2EB",
  心理学: "#FFBB28",
  经济: "#FFCE56",
  管理: "#00C49F",
  艺术: "#FF6384",
  社会科学: "#FFCE56",
  政治: "#FFCE56",
  传记: "#4BC0C0",
  技术: "#9966FF",
  教育: "#36A2EB",
  健康: "#00C49F",
  旅行: "#4BC0C0",
  科幻: "#9966FF",
  奇幻: "#FF6384",
  悬疑: "#FFBB28",
  儿童: "#00C49F",
  漫画: "#FF6384",
  诗歌: "#FF6384",
  散文: "#FF6384",
  杂志: "#C9CBCF",
  计算机: "#9966FF",
  科技: "#9966FF",
  宗教与哲学: "#36A2EB",
  个人发展: "#36A2EB",
}

// 预定义一组丰富多彩的颜色
const colorPalette = [
  "#8884d8",
  "#82ca9d",
  "#ffc658",
  "#0088FE",
  "#FF8042",
  "#FFBB28",
  "#00C49F",
  "#a4de6c",
  "#d0ed57",
  "#83a6ed",
  "#8dd1e1",
  "#6b486b",
  "#a05d56",
  "#d0743c",
  "#ff8c00",
  "#98abc5",
  "#7b6888",
  "#6b486b",
  "#a05d56",
  "#d0743c",
]

// 根据类别名称获取颜色
function getCategoryColor(category: string): string {
  // 如果类别在映射中存在，直接返回对应颜色
  if (categoryColorMap[category]) {
    return categoryColorMap[category]
  }

  // 否则根据类别名称生成哈希值，选择一个颜色
  let hash = 0
  for (let i = 0; i < category.length; i++) {
    hash = category.charCodeAt(i) + ((hash << 5) - hash)
  }

  // 使用哈希值选择颜色
  const index = Math.abs(hash) % colorPalette.length
  return colorPalette[index]
}

export default async function MonthlyReportPage({
  params,
  searchParams,
}: {
  params: { year: string; month: string }
  searchParams: { returnYear?: string }
}) {
  const year = Number.parseInt(params.year)
  const month = Number.parseInt(params.month)
  const returnYear = searchParams.returnYear || params.year

  if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
    return notFound()
  }

  const monthName = monthNames[month - 1]

  // 创建 Supabase 客户端
  const supabase = createClient()

  // 获取指定年月的数据
  const startDate = new Date(year, month - 1, 1).toISOString()
  const endDate = new Date(year, month, 0).toISOString() // 当月最后一天

  console.log(`查询时间范围: ${startDate} 到 ${endDate}`)

  // 1. 获取基本统计数据 - 使用更精确的查询
  const { data: readingRecords, error: recordsError } = await supabase
    .from("reading_records")
    .select(`
      id,
      user_id,
      book_id,
      reading_status,
      progress,
      created_at,
      users (id, user_id),
      books (
        id, 
        title,
        main_category_id,
        main_categories (id, name)
      )
    `)
    .gte("created_at", startDate)
    .lte("created_at", endDate)

  if (recordsError) {
    console.error("Error fetching reading records:", recordsError)
    // 如果出错，返回错误信息
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 container mx-auto py-8">
          <div className="flex items-center mb-8">
            <Link
              href="/"
              className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors flex items-center justify-center"
              aria-label="返回主页"
            >
              <ChevronLeft className="h-5 w-5 text-gray-600" />
            </Link>
            <h1 className="text-2xl font-bold flex items-center">
              <BookOpen className="h-6 w-6 mr-2 text-rose-600" />
              {year}年{monthName}阅读数据分析
            </h1>
          </div>
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Loader2 className="h-12 w-12 text-muted-foreground mb-4 animate-spin" />
                <h3 className="text-xl font-medium text-red-500 mb-2">获取数据时出错</h3>
                <p className="text-muted-foreground mb-6">{recordsError.message}</p>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  // 如果没有数据，显示空状态
  if (!readingRecords || readingRecords.length === 0) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 container mx-auto py-8">
          <div className="flex items-center mb-8">
            <Link
              href="/"
              className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors flex items-center justify-center"
              aria-label="返回主页"
            >
              <ChevronLeft className="h-5 w-5 text-gray-600" />
            </Link>
            <h1 className="text-2xl font-bold flex items-center">
              <BookOpen className="h-6 w-6 mr-2 text-rose-600" />
              {year}年{monthName}阅读数据分析
            </h1>
          </div>
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <BookOpen className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium mb-2">暂无数据</h3>
                <p className="text-muted-foreground mb-6">该月份尚无阅读记录数据</p>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  console.log(`获取到 ${readingRecords.length} 条阅读记录`)

  // 2. 处理数据，计算统计信息

  // 从系统配置中获取总群成员数
  const { data: configData, error: configError } = await supabase
    .from("system_config")
    .select("value")
    .eq("key", "total_group_members")
    .single()

  // 计算总群成员数 - 如果配置中有值则使用配置值，否则使用默认值或从用户表计算
  let totalGroupMembers = 0

  if (configError || !configData) {
    console.warn("无法从系统配置获取总群成员数，将使用用户表计算或默认值")
    // 获取所有用户作为备选方案
    const { data: allUsers } = await supabase.from("users").select("id, user_id")
    totalGroupMembers = allUsers?.length || 182 // 使用默认值182作为后备
  } else {
    // 使用系统配置中的值
    totalGroupMembers = Number.parseInt(configData.value, 10) || 182
  }

  // 计算参与用户数
  const uniqueUserIds = new Set(readingRecords.map((record) => record.user_id))
  const totalParticipants = uniqueUserIds.size

  // 计算书籍总数和完成数
  const totalBooks = readingRecords.length
  const completedBooks = readingRecords.filter((record) => record.reading_status === "读完").length
  const completionRate = totalBooks > 0 ? ((completedBooks / totalBooks) * 100).toFixed(1) : "0.0"

  // 计算活跃读者数（有阅读记录的用户）
  const activeReaders = uniqueUserIds.size

  // 计算上个月的年份和月份
  const prevMonth = month === 1 ? 12 : month - 1
  const prevYear = month === 1 ? year - 1 : year
  const prevMonthStartDate = new Date(prevYear, prevMonth - 1, 1).toISOString()
  const prevMonthEndDate = new Date(prevYear, prevMonth, 0).toISOString() // 上个月最后一天

  // 获取上个月的数据
  const { data: prevMonthRecords, error: prevMonthError } = await supabase
    .from("reading_records")
    .select(`
    id,
    user_id,
    book_id,
    reading_status,
    progress,
    users (id, user_id),
    books (
      id, 
      title,
      main_category_id,
      main_categories (id, name)
    )
  `)
    .gte("created_at", prevMonthStartDate)
    .lte("created_at", prevMonthEndDate)

  // 如果获取上个月数据出错，记录错误但不中断当前月份的显示
  if (prevMonthError) {
    console.error("Error fetching previous month's data:", prevMonthError)
  }

  // 计算上个月的统计数据
  let prevMonthStats = {
    totalParticipants: 0,
    totalBooks: 0,
    completedBooks: 0,
    activeReaders: 0,
  }

  if (prevMonthRecords && prevMonthRecords.length > 0) {
    const prevUniqueUserIds = new Set(prevMonthRecords.map((record) => record.user_id))
    prevMonthStats = {
      totalParticipants: prevUniqueUserIds.size,
      totalBooks: prevMonthRecords.length,
      completedBooks: prevMonthRecords.filter((record) => record.reading_status === "读完").length,
      activeReaders: prevUniqueUserIds.size,
    }
  }

  // 计算增长数据
  const growthData = {
    totalParticipants: {
      absolute: totalParticipants - prevMonthStats.totalParticipants,
      percentage:
        prevMonthStats.totalParticipants > 0
          ? (((totalParticipants - prevMonthStats.totalParticipants) / prevMonthStats.totalParticipants) * 100).toFixed(
              1,
            )
          : 100,
    },
    totalBooks: {
      absolute: totalBooks - prevMonthStats.totalBooks,
      percentage:
        prevMonthStats.totalBooks > 0
          ? (((totalBooks - prevMonthStats.totalBooks) / prevMonthStats.totalBooks) * 100).toFixed(1)
          : 100,
    },
    completedBooks: {
      absolute: completedBooks - prevMonthStats.completedBooks,
      percentage:
        prevMonthStats.completedBooks > 0
          ? (((completedBooks - prevMonthStats.completedBooks) / prevMonthStats.completedBooks) * 100).toFixed(1)
          : 100,
    },
    activeReaders: {
      absolute: activeReaders - prevMonthStats.activeReaders,
      percentage:
        prevMonthStats.activeReaders > 0
          ? (((activeReaders - prevMonthStats.activeReaders) / prevMonthStats.activeReaders) * 100).toFixed(1)
          : 100,
    },
  }

  // 3. 计算类别分布
  const categoryMap = new Map()
  const categoryUserMap = new Map()

  readingRecords.forEach((record) => {
    if (record.books && record.books.main_categories) {
      const categoryId = record.books.main_category_id
      const categoryName = record.books.main_categories.name
      const userId = record.user_id
      const userName = record.users?.user_id || `用户${userId}`

      // 更新类别统计
      if (categoryMap.has(categoryName)) {
        categoryMap.set(categoryName, categoryMap.get(categoryName) + 1)
      } else {
        categoryMap.set(categoryName, 1)
      }

      // 更新类别-用户分布
      if (!categoryUserMap.has(categoryName)) {
        categoryUserMap.set(categoryName, new Map())
      }

      const userMap = categoryUserMap.get(categoryName)
      if (userMap.has(userName)) {
        userMap.set(userName, userMap.get(userName) + 1)
      } else {
        userMap.set(userName, 1)
      }
    }
  })

  // 转换类别分布为数组格式，使用新的颜色分配函数
  const categoryDistribution = Array.from(categoryMap.entries()).map(([name, value]) => {
    const percentage = (((value as number) / totalBooks) * 100).toFixed(1) + "%"
    return {
      name,
      value,
      percentage,
      color: getCategoryColor(name), // 使用新的颜色分配函数
    }
  })

  // 转换类别-用户分布为数组格式，使用新的颜色分配函数
  const categoryUserDistribution = Array.from(categoryUserMap.entries()).map(([category, userMap]) => {
    const users = Array.from(userMap.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([name, count], index) => ({
        id: `user${index + 1}`,
        name,
        count,
      }))

    return {
      name: category,
      color: getCategoryColor(category), // 使用新的颜色分配函数
      users,
      totalBooks: categoryMap.get(category),
    }
  })

  // 4. 计算用户阅读排行 - 修复计算逻辑
  // 使用 Map 来确保每个用户的每本书只计算一次
  const userBookMap = new Map()

  // 首先，为每个用户创建一个唯一书籍ID的集合
  readingRecords.forEach((record) => {
    const userId = record.user_id
    const userName = record.users?.user_id || `用户${userId}`
    const bookId = record.book_id
    const isCompleted = record.reading_status === "读完"

    if (!userBookMap.has(userName)) {
      userBookMap.set(userName, {
        name: userName,
        avatar: userName.charAt(0),
        books: new Set(),
        completedBooks: new Set(),
      })
    }

    const userStats = userBookMap.get(userName)
    userStats.books.add(bookId)
    if (isCompleted) {
      userStats.completedBooks.add(bookId)
    }
  })

  // 然后，将集合大小转换为计数
  const userReadingMap = new Map()
  userBookMap.forEach((stats, userName) => {
    userReadingMap.set(userName, {
      name: stats.name,
      avatar: stats.avatar,
      totalBooks: stats.books.size,
      completedBooks: stats.completedBooks.size,
    })
  })

  console.log("用户阅读统计:")
  userReadingMap.forEach((stats, userName) => {
    console.log(`${userName}: 总书籍 ${stats.totalBooks}, 已完成 ${stats.completedBooks}`)
  })

  // 转换用户阅读排行为数组格式
  const topReaders = Array.from(userReadingMap.values())
    .map((user) => ({
      ...user,
      completionRate: user.totalBooks > 0 ? ((user.completedBooks / user.totalBooks) * 100).toFixed(1) : "0.0",
    }))
    .sort((a, b) => b.totalBooks - a.totalBooks)
    .map((user, index) => ({
      ...user,
      rank: index + 1,
    }))

  // 5. 构建月度数据对象
  const monthlyData = {
    year,
    month,
    stats: {
      totalParticipants,
      totalBooks,
      completedBooks,
      completionRate,
      activeReaders,
      totalGroupMembers,
      growth: growthData, // 添加增长数据
    },
    categoryDistribution,
    categoryUserDistribution,
    completionStatus: {
      completed: completedBooks,
      completedPercentage: Number.parseFloat(completionRate),
      inProgress: totalBooks - completedBooks,
      inProgressPercentage: 100 - Number.parseFloat(completionRate),
    },
    topReaders,
  }

  // 为月份选择器准备数据
  const availableYears = [2025, 2024, 2023]
  const availableMonths = [
    { year: 2025, months: Array.from({ length: 12 }, (_, i) => i + 1) }, // 1-12月
    { year: 2024, months: Array.from({ length: 12 }, (_, i) => i + 1) }, // 1-12月
    { year: 2023, months: Array.from({ length: 12 }, (_, i) => i + 1) }, // 1-12月
  ]

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-8 gap-4">
          <div className="flex items-center">
            <Link
              href="/"
              className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors flex items-center justify-center"
              aria-label="返回主页"
            >
              <ChevronLeft className="h-5 w-5 text-gray-600" />
            </Link>
            <h1 className="text-2xl font-bold flex items-center">
              <BookOpen className="h-6 w-6 mr-2 text-rose-600" />
              {year}年{monthName}阅读数据分析
            </h1>
          </div>

          {/* 添加月份选择器 */}
          <div className="w-full md:w-auto">
            <Card className="border shadow-sm">
              <CardContent className="p-4">
                <MonthSelector
                  availableYears={availableYears}
                  availableMonths={availableMonths}
                  defaultYear={year}
                  defaultMonth={month}
                />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 顶部统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium">总参与人数</p>
                  <h3 className="text-3xl font-bold mt-2">{monthlyData.stats.totalParticipants}</h3>
                  <div className="flex items-center mt-1">
                    <p className="text-xs text-muted-foreground">
                      {monthlyData.stats.totalParticipants}/{monthlyData.stats.totalGroupMembers} 群成员参与
                    </p>
                    {monthlyData.stats.growth.totalParticipants.absolute !== 0 && (
                      <div
                        className={`ml-2 flex items-center text-xs ${monthlyData.stats.growth.totalParticipants.absolute > 0 ? "text-green-500" : "text-red-500"}`}
                      >
                        {monthlyData.stats.growth.totalParticipants.absolute > 0 ? (
                          <span className="mr-1">↑</span>
                        ) : (
                          <span className="mr-1">↓</span>
                        )}
                        {Math.abs(monthlyData.stats.growth.totalParticipants.absolute)} (
                        {Math.abs(Number(monthlyData.stats.growth.totalParticipants.percentage))}%)
                      </div>
                    )}
                  </div>
                </div>
                <Users className="h-5 w-5 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium">总书籍数量</p>
                  <h3 className="text-3xl font-bold mt-2">{monthlyData.stats.totalBooks}</h3>
                  <div className="flex items-center mt-1">
                    <p className="text-xs text-muted-foreground">本月阅读书籍</p>
                    {monthlyData.stats.growth.totalBooks.absolute !== 0 && (
                      <div
                        className={`ml-2 flex items-center text-xs ${monthlyData.stats.growth.totalBooks.absolute > 0 ? "text-green-500" : "text-red-500"}`}
                      >
                        {monthlyData.stats.growth.totalBooks.absolute > 0 ? (
                          <span className="mr-1">↑</span>
                        ) : (
                          <span className="mr-1">↓</span>
                        )}
                        {Math.abs(monthlyData.stats.growth.totalBooks.absolute)} (
                        {Math.abs(Number(monthlyData.stats.growth.totalBooks.percentage))}%)
                      </div>
                    )}
                  </div>
                </div>
                <BookOpen className="h-5 w-5 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium">已读完书籍</p>
                  <h3 className="text-3xl font-bold mt-2">{monthlyData.stats.completedBooks}</h3>
                  <div className="flex items-center mt-1">
                    <p className="text-xs text-muted-foreground">占总书籍{monthlyData.stats.completionRate}%</p>
                    {monthlyData.stats.growth.completedBooks.absolute !== 0 && (
                      <div
                        className={`ml-2 flex items-center text-xs ${monthlyData.stats.growth.completedBooks.absolute > 0 ? "text-green-500" : "text-red-500"}`}
                      >
                        {monthlyData.stats.growth.completedBooks.absolute > 0 ? (
                          <span className="mr-1">↑</span>
                        ) : (
                          <span className="mr-1">↓</span>
                        )}
                        {Math.abs(monthlyData.stats.growth.completedBooks.absolute)} (
                        {Math.abs(Number(monthlyData.stats.growth.completedBooks.percentage))}%)
                      </div>
                    )}
                  </div>
                </div>
                <BookMarked className="h-5 w-5 text-amber-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium">活跃读者</p>
                  <h3 className="text-3xl font-bold mt-2">{monthlyData.stats.activeReaders}</h3>
                  <div className="flex items-center mt-1">
                    <p className="text-xs text-muted-foreground">有阅读记录的成员</p>
                    {monthlyData.stats.growth.activeReaders.absolute !== 0 && (
                      <div
                        className={`ml-2 flex items-center text-xs ${monthlyData.stats.growth.activeReaders.absolute > 0 ? "text-green-500" : "text-red-500"}`}
                      >
                        {monthlyData.stats.growth.activeReaders.absolute > 0 ? (
                          <span className="mr-1">↑</span>
                        ) : (
                          <span className="mr-1">↓</span>
                        )}
                        {Math.abs(monthlyData.stats.growth.activeReaders.absolute)} (
                        {Math.abs(Number(monthlyData.stats.growth.activeReaders.percentage))}%)
                      </div>
                    )}
                  </div>
                </div>
                <Users className="h-5 w-5 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 选项卡 */}
        <Tabs defaultValue="overview" className="mb-8">
          <TabsList className="w-full flex justify-between">
            <TabsTrigger value="overview" className="flex items-center gap-2 flex-1">
              <BarChart2 className="h-4 w-4" />
              数据概览
            </TabsTrigger>
            <TabsTrigger value="report" className="flex items-center gap-2 flex-1">
              <FileText className="h-4 w-4" />
              完整报告
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <div className="space-y-8">
              {/* 桑基图 - 用户与类别关系 */}
              <div className="w-full">
                <SimpleRelationshipChart
                  title="用户与类别关系流图"
                  subtitle="展示用户阅读不同类别书籍的流量分布"
                  year={year}
                  month={month}
                />
              </div>

              {/* 放大的图书类别分布 */}
              <div className="space-y-6">
                <div className="w-full">
                  <CategoryPieChart
                    data={monthlyData.categoryDistribution}
                    title="图书类别分布"
                    subtitle="按类别统计的书籍数量"
                    height={450} // 增加高度以更好地利用整个宽度
                  />
                </div>
                <div className="w-full">
                  <CategoryUserDistributionChart data={monthlyData.categoryUserDistribution} />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="report" className="mt-6">
            <MonthlyReportViewer year={year} month={month} />
          </TabsContent>
        </Tabs>

        {/* 阅读排行榜 - 作为独立面板放在页面底部 */}
        <div className="mt-10">
          <h2 className="text-xl font-bold mb-4 flex items-center">
            <Trophy className="h-5 w-5 mr-2 text-amber-500" />
            阅读排行榜
          </h2>
          <ReaderLeaderboard
            readers={monthlyData.topReaders}
            title="本月阅读之星"
            subtitle="阅读量最多的成员"
            showCount={20}
            pageSize={20}
          />
        </div>
      </main>
      <footer className="border-t py-4 text-center text-sm text-gray-500">
        读书群数据分析 © {new Date().getFullYear()}
      </footer>
    </div>
  )
}
