import { Head<PERSON> } from "@/components/header"
import { MonthlyReportViewer } from "@/components/monthly-report-viewer"
import { Card, CardContent } from "@/components/ui/card"
import { CategoryPieChart } from "@/components/charts/category-pie-chart"
import { ReaderLeaderboard } from "@/components/reader-leaderboard"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { BookOpen, Users, BookMarked, BarChart2, FileText, Trophy } from "lucide-react"
import { notFound } from "next/navigation"
// 删除 CategoryUserDistribution 组件的导入
// import { CategoryUserDistribution } from "@/components/charts/category-user-distribution"
import { SimpleRelationshipChart } from "@/components/charts/simple-relationship-chart"
// 在导入部分添加新组件
import { CategoryUserDistributionChart } from "@/components/charts/category-user-distribution-chart"

// 月份名称
const monthNames = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]

// 模拟数据 - 在实际应用中，这些数据应该从数据库获取
const mockMonthlyData = {
  year: 2025,
  month: 1,
  stats: {
    totalParticipants: 34,
    totalBooks: 127,
    completedBooks: 39,
    completionRate: 30.7,
    activeReaders: 17,
    totalGroupMembers: 182, // 添加群成员总数
  },
  categoryDistribution: [
    { name: "文学类", value: 62, percentage: "48.8%", color: "#FF6384" },
    { name: "哲学类", value: 23, percentage: "18.1%", color: "#36A2EB" },
    { name: "社会科学类", value: 12, percentage: "9.4%", color: "#FFCE56" },
    { name: "历史类", value: 4, percentage: "3.1%", color: "#4BC0C0" },
    { name: "科学技术类", value: 4, percentage: "3.1%", color: "#9966FF" },
    { name: "其他", value: 22, percentage: "17.3%", color: "#C9CBCF" },
  ],
  // 添加用户在各类别中的分布数据
  categoryUserDistribution: [
    {
      category: "文学类",
      color: "#FF6384",
      users: [
        { id: "小菜鸡", count: 18 },
        { id: "Chris.W", count: 10 },
        { id: "娃娃", count: 7 },
        { id: "梦田", count: 6 },
        { id: "杜佳霖", count: 5 },
      ],
    },
    {
      category: "哲学类",
      color: "#36A2EB",
      users: [
        { id: "小菜鸡", count: 8 },
        { id: "梦田", count: 5 },
        { id: "Chris.W", count: 4 },
        { id: "张伟", count: 3 },
        { id: "王晓", count: 2 },
      ],
    },
    {
      category: "社会科学类",
      color: "#FFCE56",
      users: [
        { id: "小菜鸡", count: 5 },
        { id: "李明", count: 3 },
        { id: "杜佳霖", count: 2 },
        { id: "刘芳", count: 1 },
        { id: "陈晨", count: 1 },
      ],
    },
    {
      category: "历史类",
      color: "#4BC0C0",
      users: [
        { id: "Chris.W", count: 2 },
        { id: "小菜鸡", count: 1 },
        { id: "周周", count: 1 },
      ],
    },
    {
      category: "科学技术类",
      color: "#9966FF",
      users: [
        { id: "小菜鸡", count: 2 },
        { id: "Chris.W", count: 1 },
        { id: "赵云", count: 1 },
      ],
    },
    {
      category: "其他",
      color: "#C9CBCF",
      users: [
        { id: "小菜鸡", count: 11 },
        { id: "Chris.W", count: 2 },
        { id: "娃娃", count: 2 },
        { id: "杜佳霖", count: 2 },
        { id: "李明", count: 2 },
        { id: "王晓", count: 1 },
        { id: "张伟", count: 1 },
        { id: "刘芳", count: 1 },
      ],
    },
  ],
  completionStatus: {
    completed: 39,
    completedPercentage: 30.7,
    inProgress: 88,
    inProgressPercentage: 69.3,
  },
  topReaders: [
    {
      rank: 1,
      name: "小菜鸡",
      avatar: "小",
      totalBooks: 45,
      completedBooks: 21,
      completionRate: 46.7,
    },
    {
      rank: 2,
      name: "Chris.W",
      avatar: "C",
      totalBooks: 19,
      completedBooks: 12,
      completionRate: 63.2,
    },
    {
      rank: 3,
      name: "娃娃",
      avatar: "娃",
      totalBooks: 9,
      completedBooks: 0,
      completionRate: 0,
    },
    {
      rank: 4,
      name: "梦田",
      avatar: "梦",
      totalBooks: 8,
      completedBooks: 0,
      completionRate: 0,
    },
    {
      rank: 5,
      name: "杜佳霖",
      avatar: "杜",
      totalBooks: 8,
      completedBooks: 0,
      completionRate: 0,
    },
    {
      rank: 6,
      name: "李明",
      avatar: "李",
      totalBooks: 7,
      completedBooks: 3,
      completionRate: 42.9,
    },
    {
      rank: 7,
      name: "王晓",
      avatar: "王",
      totalBooks: 7,
      completedBooks: 2,
      completionRate: 28.6,
    },
    {
      rank: 8,
      name: "张伟",
      avatar: "张",
      totalBooks: 6,
      completedBooks: 4,
      completionRate: 66.7,
    },
    {
      rank: 9,
      name: "刘芳",
      avatar: "刘",
      totalBooks: 5,
      completedBooks: 3,
      completionRate: 60.0,
    },
    {
      rank: 10,
      name: "陈晨",
      avatar: "陈",
      totalBooks: 5,
      completedBooks: 1,
      completionRate: 20.0,
    },
    {
      rank: 11,
      name: "赵云",
      avatar: "赵",
      totalBooks: 4,
      completedBooks: 2,
      completionRate: 50.0,
    },
    {
      rank: 12,
      name: "黄河",
      avatar: "黄",
      totalBooks: 4,
      completedBooks: 0,
      completionRate: 0,
    },
    {
      rank: 13,
      name: "周周",
      avatar: "周",
      totalBooks: 3,
      completedBooks: 3,
      completionRate: 100.0,
    },
    {
      rank: 14,
      name: "吴越",
      avatar: "吴",
      totalBooks: 3,
      completedBooks: 1,
      completionRate: 33.3,
    },
    {
      rank: 15,
      name: "郑和",
      avatar: "郑",
      totalBooks: 2,
      completedBooks: 2,
      completionRate: 100.0,
    },
  ],
}

export default async function MonthlyReportPage({
  params,
  searchParams,
}: {
  params: { year: string; month: string }
  searchParams: { returnYear?: string }
}) {
  const year = Number.parseInt(params.year)
  const month = Number.parseInt(params.month)
  const returnYear = searchParams.returnYear || params.year

  if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
    return notFound()
  }

  const monthName = monthNames[month - 1]

  // 在实际应用中，这里应该从数据库获取数据
  const monthlyData = mockMonthlyData

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-8">
        <div className="flex items-center mb-8">
          <h1 className="text-2xl font-bold flex items-center">
            <BookOpen className="h-6 w-6 mr-2 text-rose-600" />
            {year}年{monthName}阅读数据分析
          </h1>
        </div>

        {/* 顶部统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium">总参与人数</p>
                  <h3 className="text-3xl font-bold mt-2">{monthlyData.stats.totalParticipants}</h3>
                  <p className="text-xs text-muted-foreground mt-1">
                    {monthlyData.stats.totalParticipants}/{monthlyData.stats.totalGroupMembers} 群成员参与
                  </p>
                </div>
                <Users className="h-5 w-5 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium">总书籍数量</p>
                  <h3 className="text-3xl font-bold mt-2">{monthlyData.stats.totalBooks}</h3>
                  <p className="text-xs text-muted-foreground mt-1">本月阅读书籍</p>
                </div>
                <BookOpen className="h-5 w-5 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium">已读完书籍</p>
                  <h3 className="text-3xl font-bold mt-2">{monthlyData.stats.completedBooks}</h3>
                  <p className="text-xs text-muted-foreground mt-1">占总书籍{monthlyData.stats.completionRate}%</p>
                </div>
                <BookMarked className="h-5 w-5 text-amber-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium">活跃读者</p>
                  <h3 className="text-3xl font-bold mt-2">{monthlyData.stats.activeReaders}</h3>
                  <p className="text-xs text-muted-foreground mt-1">有阅读记录的成员</p>
                </div>
                <Users className="h-5 w-5 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 选项卡 */}
        <Tabs defaultValue="overview" className="mb-8">
          <TabsList className="w-full flex justify-between">
            <TabsTrigger value="overview" className="flex items-center gap-2 flex-1">
              <BarChart2 className="h-4 w-4" />
              数据概览
            </TabsTrigger>
            <TabsTrigger value="report" className="flex items-center gap-2 flex-1">
              <FileText className="h-4 w-4" />
              完整报告
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <div className="space-y-8">
              {/* 桑基图 - 用户与类别关系 */}
              <div className="w-full">
                <SimpleRelationshipChart
                  title="用户与类别关系流图"
                  subtitle="展示用户阅读不同类别书籍的流量分布"
                  year={year}
                  month={month}
                />
              </div>

              {/* 放大的图书类别分布 */}
              <div className="space-y-6">
                <div className="w-full">
                  <CategoryPieChart
                    data={monthlyData.categoryDistribution}
                    title="图书类别分布"
                    subtitle="按类别统计的书籍数量"
                    height={450} // 增加高度以更好地利用整个宽度
                  />
                </div>
                <div className="w-full">
                  <CategoryUserDistributionChart
                    data={[
                      {
                        name: "文学类",
                        color: "#FF6384",
                        users: [
                          { id: "user1", name: "小菜鸡", count: 18 },
                          { id: "user2", name: "Chris.W", count: 10 },
                          { id: "user3", name: "娃娃", count: 7 },
                          { id: "user4", name: "梦田", count: 6 },
                          { id: "user5", name: "杜佳霖", count: 5 },
                          { id: "user6", name: "李明", count: 4 },
                          { id: "user7", name: "王晓", count: 3 },
                          { id: "user8", name: "张伟", count: 3 },
                          { id: "user9", name: "刘芳", count: 2 },
                          { id: "user10", name: "陈晨", count: 2 },
                          { id: "user11", name: "赵云", count: 1 },
                          { id: "user12", name: "黄河", count: 1 },
                        ],
                        totalBooks: 62,
                      },
                      {
                        name: "哲学类",
                        color: "#36A2EB",
                        users: [
                          { id: "user1", name: "小菜鸡", count: 8 },
                          { id: "user4", name: "梦田", count: 5 },
                          { id: "user2", name: "Chris.W", count: 4 },
                          { id: "user8", name: "张伟", count: 3 },
                          { id: "user7", name: "王晓", count: 2 },
                          { id: "user13", name: "周周", count: 1 },
                        ],
                        totalBooks: 23,
                      },
                      {
                        name: "社会科学类",
                        color: "#FFCE56",
                        users: [
                          { id: "user1", name: "小菜鸡", count: 5 },
                          { id: "user6", name: "李明", count: 3 },
                          { id: "user5", name: "杜佳霖", count: 2 },
                          { id: "user9", name: "刘芳", count: 1 },
                          { id: "user10", name: "陈晨", count: 1 },
                        ],
                        totalBooks: 12,
                      },
                      {
                        name: "历史类",
                        color: "#4BC0C0",
                        users: [
                          { id: "user2", name: "Chris.W", count: 2 },
                          { id: "user1", name: "小菜鸡", count: 1 },
                          { id: "user13", name: "周周", count: 1 },
                        ],
                        totalBooks: 4,
                      },
                      {
                        name: "科学技术类",
                        color: "#9966FF",
                        users: [
                          { id: "user1", name: "小菜鸡", count: 2 },
                          { id: "user2", name: "Chris.W", count: 1 },
                          { id: "user11", name: "赵云", count: 1 },
                        ],
                        totalBooks: 4,
                      },
                      {
                        name: "其他",
                        color: "#C9CBCF",
                        users: [
                          { id: "user1", name: "小菜鸡", count: 11 },
                          { id: "user2", name: "Chris.W", count: 2 },
                          { id: "user3", name: "娃娃", count: 2 },
                          { id: "user5", name: "杜佳霖", count: 2 },
                          { id: "user6", name: "李明", count: 2 },
                          { id: "user7", name: "王晓", count: 1 },
                          { id: "user8", name: "张伟", count: 1 },
                          { id: "user9", name: "刘芳", count: 1 },
                        ],
                        totalBooks: 22,
                      },
                    ]}
                  />
                </div>
              </div>

              {/* 删除 CategoryUserDistribution 组件 */}
            </div>
          </TabsContent>

          <TabsContent value="report" className="mt-6">
            <MonthlyReportViewer year={year} month={month} />
          </TabsContent>
        </Tabs>

        {/* 阅读排行榜 - 作为独立面板放在页面底部 */}
        <div className="mt-10">
          <h2 className="text-xl font-bold mb-4 flex items-center">
            <Trophy className="h-5 w-5 mr-2 text-amber-500" />
            阅读排行榜
          </h2>
          <ReaderLeaderboard
            readers={monthlyData.topReaders}
            title="本月阅读之星"
            subtitle="阅读量最多的成员"
            showCount={15}
            pageSize={15}
          />
        </div>
      </main>
      <footer className="border-t py-4 text-center text-sm text-gray-500">
        读书群数据分析 © {new Date().getFullYear()}
      </footer>
    </div>
  )
}
