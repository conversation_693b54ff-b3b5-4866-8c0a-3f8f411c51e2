"use client"

import { useState, useEffect } from "react"
import { createClient } from "@supabase/supabase-js"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Header } from "@/components/header"
import { Trash2, RefreshCw, AlertTriangle } from "lucide-react"

// Mock data for preview mode
const MOCK_MONTHLY_STATS = [
  {
    id: 1,
    year: 2025,
    month: 1,
    total_participants: 15,
    total_books: 25,
    active_readers: 12,
    completed_books: 8,
    created_at: "2025-01-15T00:00:00.000Z",
  },
  {
    id: 2,
    year: 2025,
    month: 2,
    total_participants: 18,
    total_books: 30,
    active_readers: 15,
    completed_books: 10,
    created_at: "2025-02-15T00:00:00.000Z",
  },
]

const MOCK_READING_RECORDS = [
  {
    id: 1,
    created_at: "2025-01-10T00:00:00.000Z",
    reading_status: "读完",
    progress: "100%",
    users: { user_id: "user1" },
    books: {
      title: "深入理解计算机系统",
      main_categories: { name: "计算机" },
    },
  },
  {
    id: 2,
    created_at: "2025-01-15T00:00:00.000Z",
    reading_status: "在读",
    progress: "45%",
    users: { user_id: "user2" },
    books: {
      title: "算法导论",
      main_categories: { name: "计算机" },
    },
  },
  {
    id: 3,
    created_at: "2025-02-05T00:00:00.000Z",
    reading_status: "计划读",
    progress: "0%",
    users: { user_id: "user3" },
    books: {
      title: "人类简史",
      main_categories: { name: "历史" },
    },
  },
]

export default function DataInspectorPage() {
  const [monthlyStats, setMonthlyStats] = useState<any[]>([])
  const [readingRecords, setReadingRecords] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deleteConfirm, setDeleteConfirm] = useState<number | null>(null)
  const [message, setMessage] = useState<string | null>(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)

  // 修改预览模式检测逻辑，使其更严格
  useEffect(() => {
    const checkPreviewMode = () => {
      // 更严格的预览模式检测
      // 只有在明确的预览环境中才设置为预览模式
      const isPreview =
        window.location.hostname.includes("vercel.app") ||
        window.location.hostname.includes("localhost") ||
        window.location.hostname.includes("vusercontent.net") ||
        window.location.hostname.includes("127.0.0.1") ||
        window.location.search.includes("preview=true")

      // 如果域名是生产域名，强制设置为非预览模式
      const isProductionDomain = window.location.hostname === "bookclub.inklinks.io"

      setIsPreviewMode(isPreview && !isProductionDomain)
    }

    checkPreviewMode()
  }, [])

  // 创��� Supabase 客户端
  const createSupabaseClient = () => {
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

      if (!supabaseUrl || !supabaseKey) {
        throw new Error("Supabase环境变量未设置")
      }

      return createClient(supabaseUrl, supabaseKey)
    } catch (error) {
      console.error("Failed to create Supabase client:", error)
      setError("无法连接到数据库，请检查配置")
      return null
    }
  }

  // 加载数据
  const loadData = async () => {
    setIsLoading(true)
    setError(null)
    setMessage(null)

    // 如果是预览模式，使用模拟数据
    if (isPreviewMode) {
      setTimeout(() => {
        setMonthlyStats(MOCK_MONTHLY_STATS)
        setReadingRecords(MOCK_READING_RECORDS)
        setIsLoading(false)
      }, 800) // 添加一点延迟以模拟加载
      return
    }

    try {
      const supabase = createSupabaseClient()

      if (!supabase) {
        setIsLoading(false)
        return
      }

      // 获取月度统计数据
      const { data: monthlyData, error: monthlyError } = await supabase
        .from("monthly_stats")
        .select("*")
        .order("year", { ascending: false })
        .order("month", { ascending: true })

      if (monthlyError) {
        throw new Error(`获取月度统计数据失败: ${monthlyError.message}`)
      }

      setMonthlyStats(monthlyData || [])

      // 获取最近的阅读记录
      const { data: recordsData, error: recordsError } = await supabase
        .from("reading_records")
        .select(`
          id,
          created_at,
          reading_status,
          progress,
          users (
            id,
            user_id
          ),
          books (
            id,
            title,
            main_categories (
              id,
              name
            )
          )
        `)
        .order("created_at", { ascending: false })
        .limit(50)

      if (recordsError) {
        throw new Error(`获取阅读记录失败: ${recordsError.message}`)
      }

      setReadingRecords(recordsData || [])
    } catch (err) {
      console.error("Error loading data:", err)
      setError(err.message || "加载数据时发生错误")
    } finally {
      setIsLoading(false)
    }
  }

  // 删除月度统计
  const deleteMonthlyStats = async (id: number) => {
    // 在预览模式下，模拟删除操作
    if (isPreviewMode) {
      setMonthlyStats(monthlyStats.filter((stat) => stat.id !== id))
      setMessage(`成功删除ID为${id}的月度统计数据（预览模式）`)
      setDeleteConfirm(null)
      return
    }

    try {
      const supabase = createSupabaseClient()

      if (!supabase) {
        return
      }

      const { error } = await supabase.from("monthly_stats").delete().eq("id", id)

      if (error) {
        throw new Error(`删除失败: ${error.message}`)
      }

      setMessage(`成功删除ID为${id}的月度统计数据`)
      loadData() // 重新加载数据
    } catch (err) {
      setError(err.message || "删除数据时发生错误")
    } finally {
      setDeleteConfirm(null)
    }
  }

  // 初始加载
  useEffect(() => {
    loadData()
  }, [isPreviewMode]) // 当预览模式状态变化时重新加载

  // 月份名称
  const monthNames = [
    "一月",
    "二月",
    "三月",
    "四月",
    "五月",
    "六月",
    "七月",
    "八月",
    "九月",
    "十月",
    "十一月",
    "十二月",
  ]

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${date.getMinutes().toString().padStart(2, "0")}`
    } catch (e) {
      return "日期格式错误"
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 container mx-auto py-8">
          <Card>
            <CardHeader>
              <CardTitle>数据检查工具</CardTitle>
              <CardDescription>正在加载数据...</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 container mx-auto py-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>数据检查工具</CardTitle>
              <CardDescription>检查和管理数据库中的记录</CardDescription>
            </div>
            <Button variant="outline" onClick={loadData} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              刷新数据
            </Button>
          </CardHeader>
          <CardContent>
            {isPreviewMode && (
              <Alert className="mb-4 bg-amber-50 border-amber-200">
                <AlertTriangle className="h-4 w-4 text-amber-600 mr-2" />
                <AlertDescription className="text-amber-800">
                  您正在预览模式下查看此页面。显示的是模拟数据，数据操作不会影响实际数据库。
                </AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {message && (
              <Alert className="mb-4 bg-green-50 text-green-700 border-green-200">
                <AlertDescription>{message}</AlertDescription>
              </Alert>
            )}

            <Tabs defaultValue="monthly">
              <TabsList className="mb-4">
                <TabsTrigger value="monthly">月度统计数据</TabsTrigger>
                <TabsTrigger value="records">最近阅读记录</TabsTrigger>
              </TabsList>

              <TabsContent value="monthly">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>年份</TableHead>
                        <TableHead>月份</TableHead>
                        <TableHead>参与人数</TableHead>
                        <TableHead>书籍数量</TableHead>
                        <TableHead>活跃读者</TableHead>
                        <TableHead>完成书籍</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {monthlyStats.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={9} className="text-center py-4 text-muted-foreground">
                            暂无月度统计数据
                          </TableCell>
                        </TableRow>
                      ) : (
                        monthlyStats.map((stat) => (
                          <TableRow key={stat.id}>
                            <TableCell>{stat.id}</TableCell>
                            <TableCell>{stat.year}年</TableCell>
                            <TableCell>{monthNames[stat.month - 1]}</TableCell>
                            <TableCell>{stat.total_participants}</TableCell>
                            <TableCell>{stat.total_books}</TableCell>
                            <TableCell>{stat.active_readers}</TableCell>
                            <TableCell>{stat.completed_books}</TableCell>
                            <TableCell>{stat.created_at ? formatDate(stat.created_at) : "未知"}</TableCell>
                            <TableCell>
                              {deleteConfirm === stat.id ? (
                                <div className="flex items-center gap-2">
                                  <Button variant="destructive" size="sm" onClick={() => deleteMonthlyStats(stat.id)}>
                                    确认
                                  </Button>
                                  <Button variant="outline" size="sm" onClick={() => setDeleteConfirm(null)}>
                                    取消
                                  </Button>
                                </div>
                              ) : (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setDeleteConfirm(stat.id)}
                                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              <TabsContent value="records">
                <div className="rounded-md border overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>用户</TableHead>
                        <TableHead>书籍</TableHead>
                        <TableHead>分类</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>进度</TableHead>
                        <TableHead>创建时间</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {readingRecords.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-4 text-muted-foreground">
                            暂无阅读记录
                          </TableCell>
                        </TableRow>
                      ) : (
                        readingRecords.map((record) => (
                          <TableRow key={record.id}>
                            <TableCell>{record.id}</TableCell>
                            <TableCell>{record.users?.user_id || "未知用户"}</TableCell>
                            <TableCell>{record.books?.title || "未知书籍"}</TableCell>
                            <TableCell>{record.books?.main_categories?.name || "未分类"}</TableCell>
                            <TableCell>
                              <span
                                className={`px-2 py-1 text-xs rounded-full ${
                                  record.reading_status === "读完"
                                    ? "bg-green-100 text-green-800"
                                    : record.reading_status === "在读"
                                      ? "bg-blue-100 text-blue-800"
                                      : record.reading_status === "计划读"
                                        ? "bg-amber-100 text-amber-800"
                                        : "bg-gray-100 text-gray-800"
                                }`}
                              >
                                {record.reading_status || "未知"}
                              </span>
                            </TableCell>
                            <TableCell>{record.progress || "0%"}</TableCell>
                            <TableCell>{record.created_at ? formatDate(record.created_at) : "未知"}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
