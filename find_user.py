#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json

with open('rawdata/202505.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

for i, user in enumerate(data['users']):
    if '放之四海而皆准' in user['user_id'] or user['user_id'] == 'Charity':
        print(f'用户 {i+1}: {user["user_id"]}')
        print(f'书籍数量: {user["total_books"]}')
        print('书籍列表:')
        for book in user['books']:
            print(
                f'  - {book["title"]} ({book["reading_status"]}, {book["progress"]})')
        print()
