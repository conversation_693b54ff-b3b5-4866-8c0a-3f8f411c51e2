#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re

def debug_qingxin_guyu():
    """
    调试清新谷雨用户的处理过程
    """
    # 读取原始文件
    with open('rawdata/202505.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 正则表达式，用于匹配用户ID和书籍信息
    user_pattern = re.compile(r'^\s*(\d+)\.\s+(.+)')
    book_title_pattern = re.compile(r'《(.+?)》')
    
    current_user_id = None
    found_qingxin = False
    
    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        if not line or line.startswith('#') or line == "接龙" or "读书分享" in line:
            continue
        
        # 尝试匹配用户ID
        user_match = user_pattern.match(line)
        if user_match:
            user_number = user_match.group(1)
            user_info = user_match.group(2).strip()
            
            # 提取用户ID（第一个空格前的内容）
            parts = user_info.split()
            if parts:
                old_user_id = current_user_id
                current_user_id = parts[0]
                
                if current_user_id == "清新谷雨" or old_user_id == "清新谷雨":
                    print(f"第{line_num}行: {line}")
                    print(f"  匹配到用户ID: {user_number}. {user_info}")
                    print(f"  提取的用户ID: {current_user_id}")
                    print(f"  之前的用户ID: {old_user_id}")
                    found_qingxin = True
                    print()
        else:
            # 非用户ID行
            if current_user_id == "清新谷雨" or found_qingxin:
                print(f"第{line_num}行: {line}")
                print(f"  当前用户ID: {current_user_id}")
                
                # 检查是否包含书籍信息
                if '《' in line:
                    book_titles = book_title_pattern.findall(line)
                    print(f"  找到书籍: {book_titles}")
                
                print()
                
                # 如果已经处理完清新谷雨的相关行，停止调试
                if current_user_id != "清新谷雨" and found_qingxin and line_num > 300:
                    break

if __name__ == "__main__":
    debug_qingxin_guyu()
