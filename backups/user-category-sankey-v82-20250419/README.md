# 用户与类别关系流图 (v82)

**备份日期:** 2025-04-19  
**组件版本:** v82  
**状态:** 稳定，功能完整  

## 组件描述

这个组件展示用户阅读不同类别书籍的流量分布，使用桑基图(Sankey)可视化用户与书籍类别之间的关系。

## 功能特性

- 展示用户与类别之间的阅读流量关系
- 支持用户筛选功能
- 支持类别筛选功能
- 支持搜索特定用户
- 响应式设计，适应不同屏幕尺寸
- 可全屏查看

## 依赖项

- React
- Next.js
- recharts 或 d3.js (用于桑基图)
- shadcn/ui 组件库 (Card, Input 等)

## 使用方法

将此目录中的文件复制到项目的相应位置:
- `user-category-sankey.tsx` → `components/charts/user-category-sankey.tsx`
- `user-category-sankey-card.tsx` → `components/charts/user-category-sankey-card.tsx`

## 恢复说明

如需恢复此版本，请将文件复制回原始位置，并确保所有依赖项正确安装。
\`\`\`

现在，让我复制当前的用户与类别关系流图组件到备份目录：
