"use client"

// 复制自 components/charts/user-category-sankey-card.tsx
// 版本: v82
// 日期: 2025-04-19

import type React from "react"
import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { UserCategorySankey } from "./user-category-sankey"
import { Maximize2 } from "lucide-react"
import Link from "next/link"

// 定义数据类型
interface User {
  id: string
  name: string
  bookCount: number
}

interface Category {
  id: string
  name: string
  bookCount: number
}

interface UserCategoryRelation {
  userId: string
  categoryId: string
  bookCount: number
}

interface UserCategorySankeyCardProps {
  users: User[]
  categories: Category[]
  relations: UserCategoryRelation[]
  year?: number
  month?: number
  showFullscreenLink?: boolean
}

export const UserCategorySankeyCard: React.FC<UserCategorySankeyCardProps> = ({
  users,
  categories,
  relations,
  year,
  month,
  showFullscreenLink = true,
}) => {
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredUsers, setFilteredUsers] = useState<User[]>(users)
  const [filteredCategories, setFilteredCategories] = useState<Category[]>(categories)
  const [filteredRelations, setFilteredRelations] = useState<UserCategoryRelation[]>(relations)

  // 准备桑基图数据
  const prepareSankeyData = () => {
    // 创建节点
    const nodes = [
      ...filteredUsers.map((user, index) => ({
        name: user.name,
        value: user.bookCount,
        category: "user" as const,
      })),
      ...filteredCategories.map((category) => ({
        name: category.name,
        value: category.bookCount,
        itemStyle: {
          color: getCategoryColor(category.name),
        },
      })),
    ]

    // 创建连接
    const links = filteredRelations.map((relation) => {
      const sourceIndex = filteredUsers.findIndex((user) => user.id === relation.userId)
      const targetIndex =
        filteredUsers.length + filteredCategories.findIndex((category) => category.id === relation.categoryId)

      return {
        source: sourceIndex,
        target: targetIndex,
        value: relation.bookCount,
      }
    })

    return { nodes, links }
  }

  // 获取类别颜色
  const getCategoryColor = (categoryName: string) => {
    const colors: Record<string, string> = {
      文学类: "#FF9999",
      哲学类: "#66B2FF",
      社会科学类: "#FFCC66",
      历史类: "#66CCCC",
      科学技术类: "#9966CC",
      其他: "#CCCCCC",
    }
    return colors[categoryName] || "#CCCCCC"
  }

  // 处理搜索
  useEffect(() => {
    if (searchTerm) {
      const filtered = users.filter((user) => user.name.toLowerCase().includes(searchTerm.toLowerCase()))
      setFilteredUsers(filtered)

      // 更新关系数据
      const userIds = filtered.map((u) => u.id)
      const filteredRels = relations.filter((rel) => userIds.includes(rel.userId))
      setFilteredRelations(filteredRels)

      // 更新类别数据
      const categoryIds = [...new Set(filteredRels.map((rel) => rel.categoryId))]
      setFilteredCategories(categories.filter((cat) => categoryIds.includes(cat.id)))
    } else {
      setFilteredUsers(users)
      setFilteredCategories(categories)
      setFilteredRelations(relations)
    }
  }, [searchTerm, users, categories, relations])

  // 用户筛选
  const handleUserFilter = () => {
    // 实现用户筛选逻辑
    console.log("User filter clicked")
  }

  // 类别筛选
  const handleCategoryFilter = () => {
    // 实现类别筛选逻辑
    console.log("Category filter clicked")
  }

  // 构建全屏链接
  const fullscreenLink = year && month ? `/fullscreen/sankey/${year}/${month}` : "/fullscreen/sankey"

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>用户与类别关系流图</CardTitle>
          <CardDescription>展示用户阅读不同类别书籍的流量分布</CardDescription>
        </div>
        {showFullscreenLink && (
          <Link href={fullscreenLink} passHref>
            <Button variant="ghost" size="icon" title="全屏查看">
              <Maximize2 className="h-4 w-4" />
            </Button>
          </Link>
        )}
      </CardHeader>
      <CardContent>
        <div className="flex gap-2 mb-4">
          <Input
            placeholder="搜索用户..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-xs"
          />
          <Button variant="outline" onClick={handleUserFilter}>
            用户筛选
          </Button>
          <Button variant="outline" onClick={handleCategoryFilter}>
            类别筛选
          </Button>
        </div>
        <UserCategorySankey data={prepareSankeyData()} height={400} />
      </CardContent>
    </Card>
  )
}

export default UserCategorySankeyCard
