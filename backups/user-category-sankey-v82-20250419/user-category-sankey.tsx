"use client"

// 复制自 components/charts/user-category-sankey.tsx
// 版本: v82
// 日期: 2025-04-19

import type React from "react"
import { useCallback } from "react"
import { ResponsiveContainer, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Layer, Rectangle, Text } from "recharts"

// 定义数据类型
interface SankeyNode {
  name: string
  value?: number
  category?: "user" | "category"
  itemStyle?: {
    color?: string
  }
}

interface SankeyLink {
  source: number
  target: number
  value: number
}

interface SankeyData {
  nodes: SankeyNode[]
  links: SankeyLink[]
}

interface UserCategorySankeyProps {
  data: SankeyData
  height?: number | string
  onNodeClick?: (nodeData: SankeyNode) => void
}

// 为不同类别定义颜色
const categoryColors = {
  文学类: "#FF9999",
  哲学类: "#66B2FF",
  社会科学类: "#FFCC66",
  历史类: "#66CCCC",
  科学技术类: "#9966CC",
  其他: "#CCCCCC",
  // 用户使用浅蓝色背景
  user: "#E6F0FF",
}

// 自定义节点组件
const CustomNode = ({ x, y, width, height, index, payload }: any) => {
  const isUser = payload.category === "user"
  const color = isUser ? categoryColors.user : payload.itemStyle?.color || categoryColors[payload.name] || "#CCCCCC"

  return (
    <Layer key={`CustomNode${index}`}>
      <Rectangle x={x} y={y} width={width} height={height} fill={color} fillOpacity="1" rx={4} ry={4} />
      <Text
        x={isUser ? x + 10 : x + width - 10}
        y={y + height / 2}
        textAnchor={isUser ? "start" : "end"}
        verticalAnchor="middle"
        fontSize={12}
        fontWeight="bold"
        fill="#333333"
      >
        {payload.name}
      </Text>
      {payload.value && (
        <Text
          x={isUser ? x + 10 : x + width - 10}
          y={y + height / 2 + 15}
          textAnchor={isUser ? "start" : "end"}
          verticalAnchor="middle"
          fontSize={10}
          fill="#666666"
        >
          {`${payload.value} 本书`}
        </Text>
      )}
    </Layer>
  )
}

// 自定义提示组件
const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-2 border rounded shadow-md text-xs">
        <p>{`${payload[0].source.name} → ${payload[0].target.name}`}</p>
        <p className="font-bold">{`${payload[0].value} 本书`}</p>
      </div>
    )
  }
  return null
}

export const UserCategorySankey: React.FC<UserCategorySankeyProps> = ({ data, height = 400, onNodeClick }) => {
  // 处理节点点击
  const handleNodeClick = useCallback(
    (nodeData: any) => {
      if (onNodeClick) {
        onNodeClick(nodeData)
      }
    },
    [onNodeClick],
  )

  return (
    <ResponsiveContainer width="100%" height={height}>
      <Sankey
        data={data}
        nodeWidth={120}
        nodePadding={10}
        margin={{ top: 10, right: 10, bottom: 10, left: 10 }}
        link={{ stroke: "#d9d9d9" }}
        node={<CustomNode />}
        onClick={handleNodeClick}
      >
        <Tooltip content={<CustomTooltip />} />
      </Sankey>
    </ResponsiveContainer>
  )
}

export default UserCategorySankey
