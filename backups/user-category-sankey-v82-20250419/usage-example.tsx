// 示例：如何在页面中使用用户与类别关系流图卡片组件

import { UserCategorySankeyCard } from "@/components/charts/user-category-sankey-card"

// 示例数据
const exampleUsers = [
  { id: "user1", name: "小菜鸡", bookCount: 45 },
  { id: "user2", name: "<PERSON><PERSON><PERSON>", bookCount: 19 },
  { id: "user3", name: "娃��", bookCount: 9 },
  { id: "user4", name: "梦田", bookCount: 8 },
  { id: "user5", name: "社住森", bookCount: 8 },
]

const exampleCategories = [
  { id: "cat1", name: "文学类", bookCount: 62 },
  { id: "cat2", name: "哲学类", bookCount: 23 },
  { id: "cat3", name: "社会科学类", bookCount: 12 },
  { id: "cat4", name: "历史类", bookCount: 4 },
  { id: "cat5", name: "科学技术类", bookCount: 4 },
  { id: "cat6", name: "其他", bookCount: 22 },
]

const exampleRelations = [
  { userId: "user1", categoryId: "cat1", bookCount: 20 },
  { userId: "user1", categoryId: "cat2", bookCount: 10 },
  { userId: "user1", categoryId: "cat3", bookCount: 5 },
  { userId: "user1", categoryId: "cat5", bookCount: 2 },
  { userId: "user1", categoryId: "cat6", bookCount: 8 },
  { userId: "user2", categoryId: "cat1", bookCount: 5 },
  { userId: "user2", categoryId: "cat2", bookCount: 8 },
  { userId: "user2", categoryId: "cat4", bookCount: 2 },
  { userId: "user2", categoryId: "cat6", bookCount: 4 },
  { userId: "user3", categoryId: "cat1", bookCount: 3 },
  { userId: "user3", categoryId: "cat3", bookCount: 4 },
  { userId: "user3", categoryId: "cat6", bookCount: 2 },
  { userId: "user4", categoryId: "cat1", bookCount: 4 },
  { userId: "user4", categoryId: "cat2", bookCount: 2 },
  { userId: "user4", categoryId: "cat6", bookCount: 2 },
  { userId: "user5", categoryId: "cat1", bookCount: 3 },
  { userId: "user5", categoryId: "cat3", bookCount: 3 },
  { userId: "user5", categoryId: "cat5", bookCount: 2 },
]

export default function SankeyChartPage() {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">阅读数据分析</h1>

      <UserCategorySankeyCard
        users={exampleUsers}
        categories={exampleCategories}
        relations={exampleRelations}
        year={2025}
        month={4}
      />
    </div>
  )
}
