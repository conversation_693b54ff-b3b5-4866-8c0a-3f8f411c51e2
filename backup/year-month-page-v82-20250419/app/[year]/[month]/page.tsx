// 备份自: app/[year]/[month]/page.tsx
// 版本: v82
// 日期: 2025-04-19

import { Suspense } from "react"
import { notFound } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { UserCategorySankeyCard } from "@/components/charts/user-category-sankey-card"
import { MonthlyReportViewer } from "@/components/monthly-report-viewer"
import { ArrowLeft, BarChart3, FileText, Users } from "lucide-react"
import { fetchMonthlyData } from "@/lib/data-fetchers"

// 获取桑基图数据
async function getSankeyData(year: number, month: number) {
  try {
    const response = await fetch(`/api/sankey-data/${year}/${month}`, {
      next: { revalidate: 3600 }, // 缓存1小时
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch sankey data: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching sankey data:", error)
    return {
      users: [],
      categories: [],
      relations: [],
    }
  }
}

export default async function MonthPage({
  params,
  searchParams,
}: {
  params: { year: string; month: string }
  searchParams: { returnYear?: string }
}) {
  const year = Number.parseInt(params.year, 10)
  const month = Number.parseInt(params.month, 10)
  const returnYear = searchParams.returnYear ? Number.parseInt(searchParams.returnYear, 10) : year

  // 验证年月参数
  if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
    notFound()
  }

  // 获取月度数据
  const monthlyData = await fetchMonthlyData(year, month)

  // 如果没有数据，返回404
  if (!monthlyData) {
    notFound()
  }

  // 获取桑基图数据
  const sankeyData = await getSankeyData(year, month)

  // 月份名称
  const monthNames = [
    "一月",
    "二月",
    "三月",
    "四月",
    "五月",
    "六月",
    "七月",
    "八月",
    "九月",
    "十月",
    "十一月",
    "十二月",
  ]

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Link href={`/data-analysis?year=${returnYear}`} passHref>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回{returnYear}年概览
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">
            {year}年{monthNames[month - 1]}阅读数据
          </h1>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">
            <BarChart3 className="h-4 w-4 mr-2" />
            数据概览
          </TabsTrigger>
          <TabsTrigger value="reports">
            <FileText className="h-4 w-4 mr-2" />
            月度报告
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="h-4 w-4 mr-2" />
            用户分析
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <Card>
              <CardHeader>
                <CardTitle>月度阅读总览</CardTitle>
                <CardDescription>本月阅读数据统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">总阅读量</p>
                    <p className="text-3xl font-bold">{monthlyData.totalBooks} 本</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">活跃用户</p>
                    <p className="text-3xl font-bold">{monthlyData.activeUsers} 人</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">人均阅读</p>
                    <p className="text-3xl font-bold">{monthlyData.booksPerUser.toFixed(1)} 本</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">新增用户</p>
                    <p className="text-3xl font-bold">{monthlyData.newUsers} 人</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>类别分布</CardTitle>
                <CardDescription>本月阅读类别分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {monthlyData.categoryDistribution.map((category) => (
                    <div key={category.name} className="flex items-center">
                      <div className="w-36 truncate">{category.name}</div>
                      <div className="flex-1">
                        <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                          <div className="h-full bg-primary" style={{ width: `${category.percentage}%` }} />
                        </div>
                      </div>
                      <div className="w-16 text-right">{category.percentage}%</div>
                      <div className="w-16 text-right">{category.count} 本</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Suspense fallback={<div>加载用户与类别关系流图...</div>}>
            <UserCategorySankeyCard
              users={sankeyData.users}
              categories={sankeyData.categories}
              relations={sankeyData.relations}
              year={year}
              month={month}
            />
          </Suspense>
        </TabsContent>

        <TabsContent value="reports">
          <Card>
            <CardHeader>
              <CardTitle>月度报告</CardTitle>
              <CardDescription>
                {year}年{monthNames[month - 1]}阅读报告
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<div>加载月度报告...</div>}>
                <MonthlyReportViewer year={year} month={month} />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>用户阅读分析</CardTitle>
              <CardDescription>
                {year}年{monthNames[month - 1]}用户阅读情况
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>用户阅读分析内容将在此显示...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
