// 备份自: app/api/sankey-data/[year]/[month]/route.ts
// 版本: v82
// 日期: 2025-04-19

import { type NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { cache } from "@/lib/cache"

// 缓存键生成函数
const getCacheKey = (year: number, month: number) => `sankey-data:${year}:${month}`

export async function GET(request: NextRequest, { params }: { params: { year: string; month: string } }) {
  const year = Number.parseInt(params.year, 10)
  const month = Number.parseInt(params.month, 10)

  // 验证参数
  if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
    return NextResponse.json({ error: "Invalid year or month parameter" }, { status: 400 })
  }

  try {
    // 尝试从缓存获取数据
    const cacheKey = getCacheKey(year, month)
    const cachedData = await cache.get(cacheKey)

    if (cachedData) {
      return NextResponse.json(JSON.parse(cachedData))
    }

    // 如果缓存中没有，从数据库获取
    const supabase = createClient()

    // 获取指定年月的阅读记录
    const startDate = new Date(year, month - 1, 1)
    const endDate = new Date(year, month, 0) // 月份的最后一天

    // 获取用户数据
    const { data: userData, error: userError } = await supabase
      .from("reading_records")
      .select("user_id, users(id, name)")
      .gte("read_date", startDate.toISOString())
      .lte("read_date", endDate.toISOString())
      .order("user_id")

    if (userError) {
      console.error("Error fetching user data:", userError)
      return NextResponse.json({ error: "Failed to fetch user data" }, { status: 500 })
    }

    // 获取类别数据
    const { data: categoryData, error: categoryError } = await supabase
      .from("reading_records")
      .select("book_id, books(id, category_id, categories(id, name))")
      .gte("read_date", startDate.toISOString())
      .lte("read_date", endDate.toISOString())
      .order("book_id")

    if (categoryError) {
      console.error("Error fetching category data:", categoryError)
      return NextResponse.json({ error: "Failed to fetch category data" }, { status: 500 })
    }

    // 获取用户-类别关系数据
    const { data: relationData, error: relationError } = await supabase
      .from("reading_records")
      .select(`
        user_id,
        books(category_id)
      `)
      .gte("read_date", startDate.toISOString())
      .lte("read_date", endDate.toISOString())

    if (relationError) {
      console.error("Error fetching relation data:", relationError)
      return NextResponse.json({ error: "Failed to fetch relation data" }, { status: 500 })
    }

    // 处理数据，构建桑基图所需的格式
    const uniqueUsers = new Map()
    const userBookCounts = new Map()

    userData.forEach((record) => {
      const userId = record.user_id
      const user = record.users

      if (!uniqueUsers.has(userId)) {
        uniqueUsers.set(userId, {
          id: userId,
          name: user.name,
          bookCount: 0,
        })
      }

      // 增加用户的书籍计数
      const currentCount = userBookCounts.get(userId) || 0
      userBookCounts.set(userId, currentCount + 1)
    })

    // 更新用户的书籍计数
    userBookCounts.forEach((count, userId) => {
      const user = uniqueUsers.get(userId)
      if (user) {
        user.bookCount = count
      }
    })

    // 处理类别数据
    const uniqueCategories = new Map()
    const categoryBookCounts = new Map()

    categoryData.forEach((record) => {
      const categoryId = record.books?.categories?.id
      const categoryName = record.books?.categories?.name

      if (categoryId && categoryName && !uniqueCategories.has(categoryId)) {
        uniqueCategories.set(categoryId, {
          id: categoryId,
          name: categoryName,
          bookCount: 0,
        })
      }

      // 增加类别的书籍计数
      if (categoryId) {
        const currentCount = categoryBookCounts.get(categoryId) || 0
        categoryBookCounts.set(categoryId, currentCount + 1)
      }
    })

    // 更新类别的书籍计数
    categoryBookCounts.forEach((count, categoryId) => {
      const category = uniqueCategories.get(categoryId)
      if (category) {
        category.bookCount = count
      }
    })

    // 处理关系数据
    const relationMap = new Map()

    relationData.forEach((record) => {
      const userId = record.user_id
      const categoryId = record.books?.category_id

      if (userId && categoryId) {
        const key = `${userId}-${categoryId}`
        const currentCount = relationMap.get(key)?.bookCount || 0

        relationMap.set(key, {
          userId,
          categoryId,
          bookCount: currentCount + 1,
        })
      }
    })

    // 构建最终数据
    const result = {
      users: Array.from(uniqueUsers.values()),
      categories: Array.from(uniqueCategories.values()),
      relations: Array.from(relationMap.values()),
    }

    // 缓存数据
    await cache.set(cacheKey, JSON.stringify(result), 60 * 60) // 缓存1小时

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error processing sankey data:", error)
    return NextResponse.json({ error: "Failed to process data" }, { status: 500 })
  }
}
