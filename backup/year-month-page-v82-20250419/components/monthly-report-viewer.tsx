// 备份自: components/monthly-report-viewer.tsx
// 版本: v82
// 日期: 2025-04-19

"use client"

import { useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

interface MonthlyReportViewerProps {
  year: number
  month: number
}

export function MonthlyReportViewer({ year, month }: MonthlyReportViewerProps) {
  const [report, setReport] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchReport() {
      try {
        setLoading(true)
        setError(null)

        const supabase = createClient()

        // 获取指定年月的报告
        const { data, error } = await supabase
          .from("monthly_reports")
          .select("content")
          .eq("year", year)
          .eq("month", month)
          .single()

        if (error) {
          throw error
        }

        if (data) {
          setReport(data.content)
        } else {
          setReport(null)
          setError("未找到该月份的报告")
        }
      } catch (err) {
        console.error("Error fetching monthly report:", err)
        setError("获取报告时出错")
        setReport(null)
      } finally {
        setLoading(false)
      }
    }

    fetchReport()
  }, [year, month])

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-3/4" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
        <Skeleton className="h-32 w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!report) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>该月份暂无报告</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="prose max-w-none dark:prose-invert">
      <div dangerouslySetInnerHTML={{ __html: report }} />
    </div>
  )
}
