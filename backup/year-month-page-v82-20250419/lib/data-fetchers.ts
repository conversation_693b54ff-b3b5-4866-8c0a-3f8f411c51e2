// 备份自: lib/data-fetchers.ts (相关部分)
// 版本: v82
// 日期: 2025-04-19

import { createClient } from "@/lib/supabase/server"
import { cache } from "@/lib/cache"

// 获取月度数据
export async function fetchMonthlyData(year: number, month: number) {
  const cacheKey = `monthly-data:${year}:${month}`

  // 尝试从缓存获取
  const cachedData = await cache.get(cacheKey)
  if (cachedData) {
    return JSON.parse(cachedData)
  }

  const supabase = createClient()

  // 计算日期范围
  const startDate = new Date(year, month - 1, 1)
  const endDate = new Date(year, month, 0) // 月份的最后一天

  try {
    // 获取总阅读量
    const { count: totalBooks, error: countError } = await supabase
      .from("reading_records")
      .select("*", { count: "exact" })
      .gte("read_date", startDate.toISOString())
      .lte("read_date", endDate.toISOString())

    if (countError) throw countError

    // 获取活跃用户数
    const { data: activeUsersData, error: activeUsersError } = await supabase
      .from("reading_records")
      .select("user_id")
      .gte("read_date", startDate.toISOString())
      .lte("read_date", endDate.toISOString())

    if (activeUsersError) throw activeUsersError

    const activeUsers = new Set(activeUsersData.map((record) => record.user_id)).size

    // 获取新增用户
    const { count: newUsers, error: newUsersError } = await supabase
      .from("users")
      .select("*", { count: "exact" })
      .gte("created_at", startDate.toISOString())
      .lte("created_at", endDate.toISOString())

    if (newUsersError) throw newUsersError

    // 获取类别分布
    const { data: categoryData, error: categoryError } = await supabase
      .from("reading_records")
      .select(`
        books(
          category_id,
          categories(
            id,
            name
          )
        )
      `)
      .gte("read_date", startDate.toISOString())
      .lte("read_date", endDate.toISOString())

    if (categoryError) throw categoryError

    // 处理类别分布数据
    const categoryCountMap = new Map()

    categoryData.forEach((record) => {
      const categoryId = record.books?.categories?.id
      const categoryName = record.books?.categories?.name

      if (categoryId && categoryName) {
        if (!categoryCountMap.has(categoryId)) {
          categoryCountMap.set(categoryId, {
            id: categoryId,
            name: categoryName,
            count: 0,
          })
        }

        categoryCountMap.get(categoryId).count += 1
      }
    })

    const categoryDistribution = Array.from(categoryCountMap.values())
      .map((category) => ({
        ...category,
        percentage: Math.round((category.count / (totalBooks || 1)) * 100),
      }))
      .sort((a, b) => b.count - a.count)

    // 构建结果
    const result = {
      totalBooks: totalBooks || 0,
      activeUsers,
      booksPerUser: activeUsers ? (totalBooks || 0) / activeUsers : 0,
      newUsers: newUsers || 0,
      categoryDistribution,
    }

    // 缓存结果
    await cache.set(cacheKey, JSON.stringify(result), 60 * 60) // 缓存1小时

    return result
  } catch (error) {
    console.error("Error fetching monthly data:", error)
    return null
  }
}
