// 模拟数据，用于开发和测试环境
export const mockReadingRecords = [
  {
    id: "mock-1",
    reading_status: "completed",
    progress: 100,
    created_at: "2025-01-15T08:30:00Z",
    books: {
      title: "活着",
      main_categories: {
        name: "文学类",
      },
      sub_categories: {
        name: "小说",
      },
    },
    users: {
      user_id: "小菜鸡",
    },
  },
  {
    id: "mock-2",
    reading_status: "in_progress",
    progress: 65,
    created_at: "2025-02-20T14:45:00Z",
    books: {
      title: "人类简史",
      main_categories: {
        name: "社会科学类",
      },
      sub_categories: {
        name: "历史",
      },
    },
    users: {
      user_id: "Chris.W",
    },
  },
  {
    id: "mock-3",
    reading_status: "plan_to_read",
    progress: 0,
    created_at: "2025-03-10T11:15:00Z",
    books: {
      title: "三体",
      main_categories: {
        name: "文学类",
      },
      sub_categories: {
        name: "科幻",
      },
    },
    users: {
      user_id: "娃娃",
    },
  },
  {
    id: "mock-4",
    reading_status: "completed",
    progress: 100,
    created_at: "2025-04-05T09:20:00Z",
    books: {
      title: "思考，快与慢",
      main_categories: {
        name: "哲学类",
      },
      sub_categories: {
        name: "心理学",
      },
    },
    users: {
      user_id: "梦田",
    },
  },
  {
    id: "mock-5",
    reading_status: "in_progress",
    progress: 30,
    created_at: "2025-04-18T16:40:00Z",
    books: {
      title: "百年孤独",
      main_categories: {
        name: "文学类",
      },
      sub_categories: {
        name: "小说",
      },
    },
    users: {
      user_id: "杜佳霖",
    },
  },
]
