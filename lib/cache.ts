// 缓存项类型定义
// 修改CacheItem类型定义，确保泛型参数正确
type CacheItem<T = any> = {
  data: T
  timestamp: number
  expiresAt: number
}

// 内存缓存存储
const memoryCache: Map<string, CacheItem<any>> = new Map()

/**
 * 从缓存中获取数据
 * @param key 缓存键
 * @returns 缓存的数据，如果不存在或已过期则返回null
 */
export async function getFromCache<T>(key: string): Promise<T | null> {
  const item = memoryCache.get(key)

  if (!item) {
    return null
  }

  // 检查是否过期
  if (Date.now() > item.expiresAt) {
    memoryCache.delete(key)
    return null
  }

  return item.data
}

/**
 * 将数据存储到缓存中
 * @param key 缓存键
 * @param data 要缓存的数据
 * @param ttlSeconds 缓存生存时间（秒），默认1小时
 */
export async function setCache<T>(key: string, data: T, ttlSeconds = 3600): Promise<void> {
  const timestamp = Date.now()
  const expiresAt = timestamp + ttlSeconds * 1000

  memoryCache.set(key, {
    data,
    timestamp,
    expiresAt,
  })
}

/**
 * 从缓存中删除指定键的数据
 * @param key 缓存键
 */
export async function removeFromCache(key: string): Promise<void> {
  memoryCache.delete(key)
}

/**
 * 清除所有缓存
 */
export async function clearCache(): Promise<void> {
  memoryCache.clear()
}

/**
 * 获取缓存统计信息
 * @returns 缓存统计信息，包括缓存大小和所有缓存键
 */
export function getCacheStats(): { size: number; keys: string[] } {
  return {
    size: memoryCache.size,
    keys: Array.from(memoryCache.keys()),
  }
}

/**
 * 获取指定键的缓存项信息
 * @param key 缓存键
 * @returns 缓存项信息，包括数据、时间戳和过期时间
 */
export function getCacheItemInfo(key: string): { exists: boolean; expired: boolean; ttl: number } | null {
  const item = memoryCache.get(key)

  if (!item) {
    return null
  }

  const now = Date.now()
  const expired = now > item.expiresAt
  const ttl = Math.max(0, Math.floor((item.expiresAt - now) / 1000))

  return {
    exists: true,
    expired,
    ttl,
  }
}

// 确保缓存对象导出正确
export const cache = {
  get: getFromCache,
  set: setCache,
  delete: removeFromCache,
  clear: clearCache,
}
