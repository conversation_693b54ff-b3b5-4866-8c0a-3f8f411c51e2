import { createClient } from "./supabase"

// Mock config values for preview mode
const MOCK_CONFIG = {
  total_group_members: "182",
  site_title: "读书群数据分析",
  enable_reports: "true",
  join_us_text:
    "欢迎加入我们的读书群！\n\n我们是一个热爱阅读的社区，每月共读精选书籍，分享阅读心得。\n\n加入方式：\n1. 扫描下方二维码\n2. 添加群主微信：reading123\n3. 备注“读书群”",
}

// 获取系统配置
export async function getSystemConfig(key: string): Promise<string | null> {
  // Check if we're in a browser environment
  const isBrowser = typeof window !== "undefined"

  // Check if we're in preview mode
  const isPreviewMode =
    isBrowser &&
    (window.location.hostname.includes("vercel.app") ||
      window.location.hostname.includes("localhost") ||
      window.location.search.includes("preview=true"))

  // If in preview mode, return mock data
  if (isPreviewMode) {
    console.log(`Using mock config for ${key} (preview mode)`)
    return MOCK_CONFIG[key] || null
  }

  try {
    const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseKey) {
      console.warn("Supabase environment variables not set, using mock config")
      return MOCK_CONFIG[key] || null
    }

    const supabase = createClient()

    // 添加日志以便调试
    console.log(`Fetching config for key: ${key}`)

    const { data, error } = await supabase.from("system_config").select("value").eq("key", key).single()

    if (error) {
      console.error(`Error fetching config ${key}:`, error)
      return null
    }

    if (!data) {
      console.warn(`No data found for config ${key}`)
      return null
    }

    console.log(`Successfully fetched config for ${key}:`, data.value)
    return data.value
  } catch (error) {
    console.error(`Error in getSystemConfig for ${key}:`, error)
    return MOCK_CONFIG[key] || null
  }
}

// 更新系统配置
export async function updateSystemConfig(key: string, value: string): Promise<boolean> {
  try {
    const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseKey) {
      console.warn("Supabase environment variables not set, cannot update config")
      return false
    }

    const supabase = createClient()

    // 检查配置项是否存在
    const { data: existingData, error: checkError } = await supabase
      .from("system_config")
      .select("key")
      .eq("key", key)
      .single()

    if (checkError && checkError.code !== "PGRST116") {
      console.error(`Error checking if config ${key} exists:`, checkError)
      return false
    }

    let result

    if (!existingData) {
      // 如果配置项不存在，创建新的
      result = await supabase.from("system_config").insert({
        key,
        value,
        description: `${key} configuration`,
      })
    } else {
      // 如果配置项存在，更新它
      result = await supabase
        .from("system_config")
        .update({
          value,
          updated_at: new Date().toISOString(),
        })
        .eq("key", key)
    }

    if (result.error) {
      console.error(`Error updating config ${key}:`, result.error)
      return false
    }

    return true
  } catch (error) {
    console.error(`Error in updateSystemConfig for ${key}:`, error)
    return false
  }
}

// 获取所有系统配置
export async function getAllSystemConfig(): Promise<any[]> {
  try {
    const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseKey) {
      console.warn("Supabase environment variables not set, using mock config")
      return Object.entries(MOCK_CONFIG).map(([key, value]) => ({
        key,
        value,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }))
    }

    const supabase = createClient()

    const { data, error } = await supabase.from("system_config").select("*").order("key")

    if (error) {
      console.error("Error fetching all configs:", error)
      return []
    }

    return data || []
  } catch (error) {
    console.error("Error in getAllSystemConfig:", error)
    return []
  }
}
