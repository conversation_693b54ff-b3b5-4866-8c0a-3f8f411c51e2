import { getFromCache, setCache } from "./cache"

// 预热配置
type WarmupConfig = {
  enabled: boolean // 是否启用预热
  months: number // 预热最近几个月的数据
  years: number[] // 预热指定年份的数据
  retryDelay: number // 预热失败后重���延迟（毫秒）
  maxRetries: number // 最大重试次数
  debug: boolean // 是否启用调试模式
}

// 预热状态
type WarmupStatus = {
  isWarming: boolean
  lastWarmedAt: Date | null
  completedItems: string[]
  failedItems: string[]
  inProgress: string[]
}

// 默认配置
const config: WarmupConfig = {
  enabled: true,
  months: 3, // 预热最近3个月的数据
  years: [new Date().getFullYear(), new Date().getFullYear() - 1], // 当前年份和上一年
  retryDelay: 5000, // 5秒
  maxRetries: 3,
  debug: process.env.NODE_ENV === "development",
}

// 预热状态
const status: WarmupStatus = {
  isWarming: false,
  lastWarmedAt: null,
  completedItems: [],
  failedItems: [],
  inProgress: [],
}

/**
 * 获取预热状态
 */
export function getWarmupStatus(): WarmupStatus {
  return { ...status }
}

/**
 * 预热单个数据项
 */
async function warmupItem(url: string, cacheKey: string, retries = 0): Promise<{ success: boolean; error?: string }> {
  // 检查是否已经在缓存中
  const cached = getFromCache(cacheKey)
  if (cached) {
    if (config.debug) console.log(`[Warmup] Cache already exists for ${cacheKey}, skipping`)
    return { success: true }
  }

  try {
    // 添加到进行中列表
    status.inProgress.push(cacheKey)

    // 获取数据
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Failed to fetch data: ${response.statusText}`)
    }

    const data = await response.json()

    // 缓存数据
    setCache(cacheKey, data)

    // 从进行中列表移除
    status.inProgress = status.inProgress.filter((item) => item !== cacheKey)

    // 添加到完成列表
    status.completedItems.push(cacheKey)

    if (config.debug) console.log(`[Warmup] Successfully warmed up ${cacheKey}`)
    return { success: true }
  } catch (error) {
    // 从进行中列表移除
    status.inProgress = status.inProgress.filter((item) => item !== cacheKey)

    if (retries < config.maxRetries) {
      if (config.debug) console.log(`[Warmup] Failed to warm up ${cacheKey}, retrying in ${config.retryDelay}ms`)

      // 重试
      return new Promise((resolve) => {
        setTimeout(async () => {
          const result = await warmupItem(url, cacheKey, retries + 1)
          resolve(result)
        }, config.retryDelay)
      })
    }

    // 添加到失败列表
    status.failedItems.push(cacheKey)

    if (config.debug) console.error(`[Warmup] Failed to warm up ${cacheKey} after ${retries} retries:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

/**
 * 预热桑基图数据
 */
async function warmupSankeyData(): Promise<void> {
  const currentYear = new Date().getFullYear()
  const currentMonth = new Date().getMonth() + 1

  // 清除状态
  status.completedItems = []
  status.failedItems = []
  status.inProgress = []

  // 预热指定年份的数据
  for (const year of config.years) {
    // 确定月份范围
    let startMonth = 1
    let endMonth = 12

    // 如果是当前年份，只预热到当前月份
    if (year === currentYear) {
      endMonth = currentMonth
    }

    // 如果配置了只预热最近几个月
    if (config.months > 0 && year === currentYear) {
      startMonth = Math.max(1, currentMonth - config.months + 1)
    }

    // 预热每个月的数据
    for (let month = startMonth; month <= endMonth; month++) {
      const url = `/api/sankey-data/${year}/${month}`
      const cacheKey = `sankey-data-${year}-${month}`

      await warmupItem(url, cacheKey)
    }
  }
}

/**
 * 启动缓存预热
 */
export async function startWarmup(force = false): Promise<boolean> {
  // 如果已经在预热中，或者禁用了预热，则跳过
  if ((status.isWarming && !force) || !config.enabled) {
    return false
  }

  if (config.debug) console.log("[Warmup] Starting cache warmup")

  status.isWarming = true

  try {
    await warmupSankeyData()

    // 更新状态
    status.lastWarmedAt = new Date()
    status.isWarming = false

    if (config.debug) {
      console.log("[Warmup] Cache warmup completed")
      console.log(`[Warmup] Completed: ${status.completedItems.length}, Failed: ${status.failedItems.length}`)
    }

    return true
  } catch (error) {
    console.error("[Warmup] Cache warmup failed:", error)
    status.isWarming = false
    return false
  }
}

/**
 * 更新预热配置
 */
export function updateWarmupConfig(newConfig: Partial<WarmupConfig>): WarmupConfig {
  Object.assign(config, newConfig)
  return { ...config }
}

/**
 * 获取预热配置
 */
export function getWarmupConfig(): WarmupConfig {
  return { ...config }
}
