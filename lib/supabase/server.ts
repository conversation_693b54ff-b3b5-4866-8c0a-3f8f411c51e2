import { createClient as supabase<PERSON>reateClient } from "@supabase/supabase-js"

// Create a server-side Supabase client
export function createClient() {
  // Check if we're in preview mode
  const isPreviewMode =
    typeof process !== "undefined" &&
    (process.env.VERCEL_ENV === "preview" ||
      process.env.NODE_ENV === "development" ||
      process.env.NEXT_PUBLIC_VERCEL_ENV === "preview")

  if (isPreviewMode) {
    console.log("Using mock Supabase client (preview mode)")
    return createMockClient()
  }

  const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Missing Supabase credentials")
  }

  return supabaseCreateClient(supabaseUrl, supabaseKey)
}

// Create a mock Supabase client for preview mode
function createMockClient() {
  return {
    from: (table: string) => ({
      select: (columns: string) => ({
        eq: (column: string, value: any) => ({
          single: async () => ({ data: null, error: null }),
          maybeSingle: async () => ({ data: null, error: null }),
        }),
        in: (column: string, values: any[]) => ({
          data: [],
          error: null,
        }),
        order: (column: string, { ascending }: { ascending: boolean }) => ({
          limit: (limit: number) => ({ data: [], error: null }),
        }),
        gte: (column: string, value: any) => ({
          lt: (column: string, value: any) => ({
            data: [],
            error: null,
          }),
        }),
      }),
      insert: (data: any) => ({ error: null }),
      update: (data: any) => ({ error: null }),
      delete: () => ({ error: null }),
    }),
    auth: {
      getUser: async () => ({ data: { user: null }, error: null }),
      getSession: async () => ({ data: { session: null }, error: null }),
    },
  }
}
