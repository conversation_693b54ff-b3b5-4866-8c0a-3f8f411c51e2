import { createClient as createClientBase } from "@supabase/supabase-js"

// 创建一个单例客户端实例
let supabaseClient: ReturnType<typeof createClientBase> | null = null

export function createClient() {
  if (supabaseClient) return supabaseClient

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Missing Supabase credentials")
  }

  supabaseClient = createClientBase(supabaseUrl, supabaseKey)
  return supabaseClient
}
