import { createClient as supabaseCreateClient } from "@supabase/supabase-js"

// 创建客户端Supabase实例
export function createClient() {
  // 检查是否在预览模式
  const isPreviewMode =
    typeof window !== "undefined" &&
    (window.location.hostname.includes("vercel.app") ||
      window.location.hostname.includes("localhost") ||
      window.location.hostname.includes("vusercontent.net") ||
      window.location.search.includes("preview=true"))

  // 在预览模式下，返回一个模拟的Supabase客户端
  if (isPreviewMode) {
    return createMockClient()
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Missing Supabase credentials")
  }

  return supabaseCreateClient(supabaseUrl, supabaseKey)
}

// 修改createMockClient函数，确保它正确处理身份验证

function createMockClient() {
  return {
    auth: {
      getUser: async () => {
        try {
          // 从localStorage获取模拟的用户数据
          if (typeof window !== "undefined") {
            const auth = localStorage.getItem("preview_auth")
            if (auth) {
              const { user } = JSON.parse(auth)
              return { data: { user }, error: null }
            }
          }
          return { data: { user: null }, error: null }
        } catch (error) {
          console.error("Error in mock getUser:", error)
          return { data: { user: null }, error: null }
        }
      },
      getSession: async () => {
        try {
          // 从localStorage获取模拟的会话数据
          if (typeof window !== "undefined") {
            const auth = localStorage.getItem("preview_auth")
            if (auth) {
              const { session } = JSON.parse(auth)
              return { data: { session }, error: null }
            }
          }
          return { data: { session: null }, error: null }
        } catch (error) {
          console.error("Error in mock getSession:", error)
          return { data: { session: null }, error: null }
        }
      },
      signInWithPassword: async ({ email, password }) => {
        // 模拟登录逻辑
        if (email === "<EMAIL>" && password === "password123") {
          const user = { id: "mock-user-id", email, role: "admin" }
          const session = {
            access_token: "mock-token",
            expires_at: Date.now() + 24 * 60 * 60 * 1000,
            user,
          }

          if (typeof window !== "undefined") {
            localStorage.setItem("preview_auth", JSON.stringify({ user, session }))
          }

          return { data: { user, session }, error: null }
        }

        return {
          data: { user: null, session: null },
          error: { message: "Invalid login credentials" },
        }
      },
      signOut: async () => {
        // 清除模拟的身份验证数据
        if (typeof window !== "undefined") {
          localStorage.removeItem("preview_auth")
        }
        return { error: null }
      },
    },
    // 添加其他需要模拟的方法...
    from: () => ({
      select: () => ({
        eq: () => ({
          single: async () => ({ data: null, error: null }),
          maybeSingle: async () => ({ data: null, error: null }),
        }),
        order: () => ({
          limit: () => ({ data: [], error: null }),
        }),
      }),
      insert: () => ({ error: null }),
      update: () => ({ error: null }),
      delete: () => ({ error: null }),
    }),
  }
}
