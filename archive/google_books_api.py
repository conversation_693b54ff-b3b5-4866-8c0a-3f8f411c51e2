import requests


def get_book_info(book_title):
    """
    使用 Google Books API 查询书籍信息
    :param book_title: 书籍名称
    :return: 包含作者和书籍类型的字典
    """
    api_url = "https://www.googleapis.com/books/v1/volumes"
    params = {
        "q": book_title,
        "maxResults": 1,  # 只获取一个结果
        "printType": "books",
        "langRestrict": "zh"  # 限制返回中文信息
    }

    try:
        response = requests.get(api_url, params=params)
        response.raise_for_status()
        data = response.json()

        if "items" in data and len(data["items"]) > 0:
            book = data["items"][0]["volumeInfo"]
            authors = book.get("authors", ["未知作者"])
            categories = book.get("categories", ["未分类"])

            # 确保作者和分类信息为中文
            authors = [author for author in authors if all(
                '\u4e00' <= char <= '\u9fff' for char in author)]
            categories = [category for category in categories if all(
                '\u4e00' <= char <= '\u9fff' for char in category)]

            return {
                "title": book_title,
                "authors": authors if authors else ["未知作者"],
                "categories": categories if categories else ["未分类"]
            }
        else:
            return {
                "title": book_title,
                "authors": ["未知作者"],
                "categories": ["未分类"]
            }
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return {
            "title": book_title,
            "authors": ["未知作者"],
            "categories": ["未分类"]
        }


# 示例用法
if __name__ == "__main__":
    book_title = input("请输入书籍名称: ")
    book_info = get_book_info(book_title)
    print(f"书名: {book_info['title']}")
    print(f"作者: {', '.join(book_info['authors'])}")
    print(f"分类: {', '.join(book_info['categories'])}")
