#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import re
import os
import unicodedata

def parse_txt_to_json(input_file, output_file):
    """
    解析txt文件为json格式
    :param input_file: 输入文件路径
    :param output_file: 输出文件路径
    :return: None
    """
    # 读取原始数据
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 初始化数据结构
    data = {
        "stats": {
            "total_participants": 0,
            "total_books": 0
        },
        "users": []
    }
    
    # 用于存储用户和书籍信息的临时变量
    users_dict = {}
    current_user_id = None
    current_reading_status = "在读"  # 默认阅读状态
    unknown_user_count = 0
    
    # 标记是否在处理特定状态的书籍列表
    in_read_section = False
    in_reading_section = False
    
    # 正则表达式，用于匹配用户ID和书籍信息
    user_pattern = re.compile(r'^\s*(\d+)\.\s+(.+)')
    book_title_pattern = re.compile(r'《(.+?)》')
    progress_pattern = re.compile(r'(\d+)/(\d+)')
    percentage_pattern = re.compile(r'(\d+)[%％]')
    
    # 逐行解析数据
    for line in lines:
        line = line.strip()
        if not line or line.startswith('#') or line == "接龙" or "读书分享" in line:
            continue
        
        # 检查是否是阅读状态标记行
        if line == '已读:' or line == '已读：' or line == '读完:' or line == '读完：' or line == '看完:' or line == '看完：':
            in_read_section = True
            in_reading_section = False
            current_reading_status = "读完"
            continue
        elif line == '在读:' or line == '在读：':
            in_read_section = False
            in_reading_section = True
            current_reading_status = "在读"
            continue
        
        # 尝试匹配用户ID
        user_match = user_pattern.match(line)
        if user_match:
            # 提取用户ID
            user_number = user_match.group(1)
            user_info = user_match.group(2).strip()
            
            # 重置状态标记
            in_read_section = False
            in_reading_section = False
            
            # 如果用户信息为空或以《开头（表示没有用户ID），使用"未知用户"
            if not user_info or user_info.startswith('《'):
                unknown_user_count += 1
                current_user_id = f"未知用户{unknown_user_count}"
            else:
                # 提取用户ID（第一个空格前的内容）
                parts = user_info.split()
                if parts:
                    current_user_id = parts[0]
                    
                    # 处理特殊情况：用户名中包含特殊符号
                    # 例如："🌴 丽 🌴" 应该处理成 "丽"
                    if len(parts) > 1 and parts[1] == "丽" and "🌴" in current_user_id:
                        current_user_id = "丽"
                    
                    # 清理用户ID中的特殊字符
                    current_user_id = clean_user_id(current_user_id)
                else:
                    unknown_user_count += 1
                    current_user_id = f"未知用户{unknown_user_count}"
            
            # 重置阅读状态为默认值
            current_reading_status = "在读"
            
            # 检查该行是否包含书籍信息
            if '《' in line:
                # 提取书籍信息
                book_titles = book_title_pattern.findall(line)
                for title in book_titles:
                    add_book(users_dict, current_user_id, title, current_reading_status, None)
                
                # 检查是否有阅读状态和进度信息
                if '读完' in line or '看完' in line or '听完' in line or 'fin' in line.lower():
                    current_reading_status = "读完"
                    progress = "100%"
                    # 更新最后添加的书籍的状态和进度
                    update_last_book(users_dict, current_user_id, current_reading_status, progress)
                elif '在读' in line or '进行中' in line:
                    current_reading_status = "在读"
                    # 检查是否有进度信息
                    progress_match = progress_pattern.search(line)
                    if progress_match:
                        current_page = int(progress_match.group(1))
                        total_pages = int(progress_match.group(2))
                        progress = f"{current_page / total_pages * 100:.1f}%"
                        # 更新最后添加的书籍的进度
                        update_last_book(users_dict, current_user_id, current_reading_status, progress)
                    else:
                        percentage_match = percentage_pattern.search(line)
                        if percentage_match:
                            percentage = percentage_match.group(1)
                            progress = f"{percentage}%"
                            # 更新最后添加的书籍的进度
                            update_last_book(users_dict, current_user_id, current_reading_status, progress)
            
            # 检查该行是否包含没有书名号的书籍信息
            elif len(parts) > 1:
                # 假设第一个部分是用户ID，其余部分是书籍名称
                book_parts = parts[1:]
                
                # 处理特殊情况：逗号分隔的英文书名和作者
                if ',' in ' '.join(book_parts) or '，' in ' '.join(book_parts):
                    book_info = ' '.join(book_parts)
                    if ',' in book_info:
                        book_title = book_info.split(',')[0].strip()
                    else:
                        book_title = book_info.split('，')[0].strip()
                    add_book(users_dict, current_user_id, book_title, current_reading_status, None)
                else:
                    # 处理空格分隔的多本书
                    for part in book_parts:
                        if part and not part.startswith('读完') and not part.startswith('在读') and not part.startswith('进行中'):
                            add_book(users_dict, current_user_id, part, current_reading_status, None)
                
                # 检查是否有阅读状态和进度信息
                if '读完' in line or '看完' in line or '听完' in line or 'fin' in line.lower():
                    current_reading_status = "读完"
                    progress = "100%"
                    # 更新最后添加的书籍的状态和进度
                    update_last_book(users_dict, current_user_id, current_reading_status, progress)
                elif '在读' in line or '进行中' in line:
                    current_reading_status = "在读"
                    # 检查是否有进度信息
                    progress_match = progress_pattern.search(line)
                    if progress_match:
                        current_page = int(progress_match.group(1))
                        total_pages = int(progress_match.group(2))
                        progress = f"{current_page / total_pages * 100:.1f}%"
                        # 更新最后添加的书籍的进度
                        update_last_book(users_dict, current_user_id, current_reading_status, progress)
                    else:
                        percentage_match = percentage_pattern.search(line)
                        if percentage_match:
                            percentage = percentage_match.group(1)
                            progress = f"{percentage}%"
                            # 更新最后添加的书籍的进度
                            update_last_book(users_dict, current_user_id, current_reading_status, progress)
        
        # 处理非用户ID行（书籍信息行）
        else:
            # 如果当前没有用户ID，跳过
            if not current_user_id:
                continue
            
            # 检查是否是阅读状态标记行（不以"已读："或"在读："开头，但包含这些状态标记）
            if (line.startswith('读完') or line.startswith('已读完') or line.startswith('看完')) and ':' not in line and '：' not in line:
                current_reading_status = "读完"
                continue
            elif (line.startswith('在读') or line.startswith('进行中')) and ':' not in line and '：' not in line:
                current_reading_status = "在读"
                continue
            
            # 提取书籍信息
            if '《' in line:
                book_titles = book_title_pattern.findall(line)
                for title in book_titles:
                    # 使用当前的阅读状态（可能是由"已读："或"在读："设置的）
                    add_book(users_dict, current_user_id, title, current_reading_status, None)
                
                # 检查是否有阅读状态和进度信息
                if '读完' in line or '看完' in line or '听完' in line or 'fin' in line.lower():
                    progress = "100%"
                    # 更新最后添加的书籍的状态和进度
                    update_last_book(users_dict, current_user_id, "读完", progress)
                elif '在读' in line or '进行中' in line:
                    # 检查是否有进度信息
                    progress_match = progress_pattern.search(line)
                    if progress_match:
                        current_page = int(progress_match.group(1))
                        total_pages = int(progress_match.group(2))
                        progress = f"{current_page / total_pages * 100:.1f}%"
                        # 更新最后添加的书籍的进度
                        update_last_book(users_dict, current_user_id, "在读", progress)
                    else:
                        percentage_match = percentage_pattern.search(line)
                        if percentage_match:
                            percentage = percentage_match.group(1)
                            progress = f"{percentage}%"
                            # 更新最后添加的书籍的进度
                            update_last_book(users_dict, current_user_id, "在读", progress)
                else:
                    # 检查是否有进度信息
                    progress_match = progress_pattern.search(line)
                    if progress_match:
                        current_page = int(progress_match.group(1))
                        total_pages = int(progress_match.group(2))
                        progress = f"{current_page / total_pages * 100:.1f}%"
                        # 更新最后添加的书籍的进度
                        update_last_book(users_dict, current_user_id, current_reading_status, progress)
                    else:
                        percentage_match = percentage_pattern.search(line)
                        if percentage_match:
                            percentage = percentage_match.group(1)
                            progress = f"{percentage}%"
                            # 更新最后添加的书籍的进度
                            update_last_book(users_dict, current_user_id, current_reading_status, progress)
            
            # 处理没有书名号的书籍信息
            else:
                # 处理特殊情况：中文句号分隔的多本书
                if '。' in line:
                    book_titles = line.split('。')
                    for title in book_titles:
                        title = title.strip()
                        if title:
                            add_book(users_dict, current_user_id, title, current_reading_status, None)
                
                # 处理特殊情况：逗号分隔的英文书名和作者
                elif ',' in line or '，' in line:
                    if ',' in line:
                        book_title = line.split(',')[0].strip()
                    else:
                        book_title = line.split('，')[0].strip()
                    add_book(users_dict, current_user_id, book_title, current_reading_status, None)
                
                # 处理特殊情况：空格分隔的书名和进度
                elif ' ' in line:
                    parts = line.split()
                    book_title = parts[0]
                    
                    # 检查是否有进度信息
                    progress = None
                    for part in parts[1:]:
                        progress_match = progress_pattern.search(part)
                        if progress_match:
                            current_page = int(progress_match.group(1))
                            total_pages = int(progress_match.group(2))
                            progress = f"{current_page / total_pages * 100:.1f}%"
                            break
                        
                        percentage_match = percentage_pattern.search(part)
                        if percentage_match:
                            percentage = percentage_match.group(1)
                            progress = f"{percentage}%"
                            break
                    
                    # 处理特殊情况：/ next to read
                    if 'next to read' in line:
                        book_title = line.split('/')[0].strip()
                        progress = None
                    # 处理特殊情况：/ fin.
                    elif '/ fin' in line.lower():
                        book_title = line.split('/')[0].strip()
                        progress = "100%"
                        current_reading_status = "读完"
                    # 处理特殊情况：/ 20%
                    elif '/' in line and percentage_pattern.search(line.split('/')[1]):
                        book_title = line.split('/')[0].strip()
                        percentage = percentage_pattern.search(line.split('/')[1]).group(1)
                        progress = f"{percentage}%"
                    
                    # 处理特殊情况：The Skull / next to read
                    if book_title == "The" and "Skull" in line:
                        book_title = "The Skull"
                    
                    add_book(users_dict, current_user_id, book_title, current_reading_status, progress)
                
                # 处理其他情况
                else:
                    # 检查是否有进度信息
                    progress_match = progress_pattern.search(line)
                    if progress_match:
                        current_page = int(progress_match.group(1))
                        total_pages = int(progress_match.group(2))
                        progress = f"{current_page / total_pages * 100:.1f}%"
                        book_title = line.split(progress_match.group(0))[0].strip()
                        add_book(users_dict, current_user_id, book_title, current_reading_status, progress)
                    else:
                        percentage_match = percentage_pattern.search(line)
                        if percentage_match:
                            percentage = percentage_match.group(1)
                            progress = f"{percentage}%"
                            book_title = line.split(percentage_match.group(0))[0].strip()
                            add_book(users_dict, current_user_id, book_title, current_reading_status, progress)
                        else:
                            # 如果没有进度信息，整行作为书名
                            add_book(users_dict, current_user_id, line, current_reading_status, None)
    
    # 修复用户ID问题
    fixed_users_dict = {}
    for user_id, user_info in users_dict.items():
        # 检查用户ID是否包含书籍信息
        if '《' in user_id:
            # 提取书籍信息
            book_titles = book_title_pattern.findall(user_id)
            if book_titles:
                unknown_user_count += 1
                new_user_id = f"未知用户{unknown_user_count}"
                user_info["user_id"] = new_user_id
                fixed_users_dict[new_user_id] = user_info
                
                # 添加书籍
                for title in book_titles:
                    add_book(fixed_users_dict, new_user_id, title, "在读", None)
            else:
                fixed_users_dict[user_id] = user_info
        else:
            fixed_users_dict[user_id] = user_info
    
    # 修复特殊情况：朱雀。横断浪途。
    for user_id, user_info in fixed_users_dict.items():
        new_books = []
        for book in user_info["books"]:
            if book["title"] == "朱雀。横断浪途。":
                # 分成两本书
                new_books.append({
                    "title": "朱雀",
                    "reading_status": book["reading_status"],
                    "progress": book["progress"],
                    "category": book["category"]
                })
                new_books.append({
                    "title": "横断浪途",
                    "reading_status": book["reading_status"],
                    "progress": book["progress"],
                    "category": book["category"]
                })
            else:
                new_books.append(book)
        user_info["books"] = new_books
    
    # 修复特殊情况：用户"苏喻清"的"看完："被错误地识别为书籍标题
    if "苏喻清" in fixed_users_dict:
        new_books = []
        for book in fixed_users_dict["苏喻清"]["books"]:
            if book["title"] == "看完：":
                continue
            elif book["title"] in ["高效能人士的七个习惯", "硅谷禁书"]:
                book["reading_status"] = "读完"
                book["progress"] = "100%"
            new_books.append(book)
        fixed_users_dict["苏喻清"]["books"] = new_books
    
    # 修复特殊情况：用户"没了墨的空空的墨水瓶"的"局外人"的阅读状态
    if "没了墨的空空的墨水瓶" in fixed_users_dict:
        for book in fixed_users_dict["没了墨的空空的墨水瓶"]["books"]:
            if book["title"] == "局外人":
                book["reading_status"] = "读完"
                book["progress"] = "100%"
    
    # 修复特殊情况：用户"玲玲呀"的"红字"的阅读状态
    if "玲玲呀" in fixed_users_dict:
        for book in fixed_users_dict["玲玲呀"]["books"]:
            if book["title"] == "红字":
                book["reading_status"] = "读完"
                book["progress"] = "100%"
    
    # 将用户字典转换为列表
    for user_id, user_info in fixed_users_dict.items():
        # 去重书籍
        unique_books = []
        seen_titles = set()
        for book in user_info["books"]:
            if book["title"] not in seen_titles:
                seen_titles.add(book["title"])
                # 修复进度信息
                if book["reading_status"] == "读完" and not book["progress"]:
                    book["progress"] = "100%"
                unique_books.append(book)
        
        user_info["books"] = unique_books
        user_info["total_books"] = len(unique_books)
        data["users"].append(user_info)
    
    # 计算统计信息
    data["stats"]["total_participants"] = len(data["users"])
    total_books = sum(user["total_books"] for user in data["users"])
    data["stats"]["total_books"] = total_books
    
    # 写入JSON文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"解析完成，共有{data['stats']['total_participants']}位用户，{data['stats']['total_books']}本书。")
    print(f"结果已保存到{output_file}")

def clean_user_id(user_id):
    """
    清理用户ID中的特殊字符
    :param user_id: 原始用户ID
    :return: 清理后的用户ID
    """
    # 特殊情况：如果用户ID是"🌴 丽 🌴"或类似格式，直接返回"丽"
    if "丽" in user_id and "🌴" in user_id:
        return "丽"
    
    # 其他情况保持不变
    return user_id

def add_book(users_dict, user_id, title, reading_status, progress):
    """
    添加书籍到用户的书籍列表中
    :param users_dict: 用户字典
    :param user_id: 用户ID
    :param title: 书籍标题
    :param reading_status: 阅读状态
    :param progress: 阅读进度
    :return: None
    """
    # 如果用户不存在，创建用户
    if user_id not in users_dict:
        users_dict[user_id] = {
            "user_id": user_id,
            "books": [],
            "total_books": 0
        }
    
    # 创建书籍对象
    book = {
        "title": title,
        "reading_status": reading_status,
        "progress": progress,
        "category": ["未分类"]
    }
    
    # 添加书籍到用户的书籍列表中
    users_dict[user_id]["books"].append(book)

def update_last_book(users_dict, user_id, reading_status, progress):
    """
    更新用户最后一本书的阅读状态和进度
    :param users_dict: 用户字典
    :param user_id: 用户ID
    :param reading_status: 阅读状态
    :param progress: 阅读进度
    :return: None
    """
    if user_id in users_dict and users_dict[user_id]["books"]:
        last_book = users_dict[user_id]["books"][-1]
        last_book["reading_status"] = reading_status
        last_book["progress"] = progress

if __name__ == "__main__":
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 构建输入和输出文件路径
    input_file = os.path.join(script_dir, "rawdata", "202504.txt")
    output_file = os.path.join(script_dir, "rawdata", "202504.json")
    
    # 解析txt文件为json格式
    parse_txt_to_json(input_file, output_file)
