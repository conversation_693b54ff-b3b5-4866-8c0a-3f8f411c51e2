#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import re

def check_suspicious_books():
    """
    检查可能被错误识别为书籍的用户名
    """
    # 读取处理后的JSON文件
    with open('rawdata/202505.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 读取原始文件
    with open('rawdata/202505.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 从原始文件中提取所有用户名
    user_pattern = re.compile(r'^\s*(\d+)\.\s*(.+)')
    original_user_names = set()
    
    for line in lines:
        line = line.strip()
        user_match = user_pattern.match(line)
        if user_match:
            user_info = user_match.group(2).strip()
            # 提取用户名的各个部分
            parts = user_info.split()
            for part in parts:
                # 过滤掉明显的书籍信息（包含《》的）
                if not ('《' in part or '》' in part):
                    original_user_names.add(part)
    
    print("=== 检查可疑的书籍名称 ===")
    print("以下书籍名称可能是用户名的一部分：\n")
    
    suspicious_books = []
    
    for user in data['users']:
        user_id = user['user_id']
        for book in user['books']:
            book_title = book['title']
            
            # 检查书籍名称是否可能是用户名的一部分
            is_suspicious = False
            reasons = []
            
            # 1. 检查是否在原始用户名列表中
            if book_title in original_user_names:
                is_suspicious = True
                reasons.append("在原始用户名列表中")
            
            # 2. 检查是否包含特殊符号（可能是用户名的装饰）
            special_chars = ['🌴', '🌵', '🍃', '－', '＿', '🔥', '💫', '⭐', '🌸', '🌺', '🌻', '🌹']
            if any(char in book_title for char in special_chars):
                is_suspicious = True
                reasons.append("包含装饰性特殊符号")
            
            # 3. 检查是否是单个字符或很短的非中文书名
            if len(book_title) <= 2 and not re.search(r'[a-zA-Z]', book_title):
                is_suspicious = True
                reasons.append("过短的非英文名称")
            
            # 4. 检查是否包含明显的用户名模式
            user_patterns = ['小楷', '进行时', '谷雨', '椿芽', '王木木', '亚平', '大脸', '丽']
            if any(pattern in book_title for pattern in user_patterns):
                is_suspicious = True
                reasons.append("包含常见用户名模式")
            
            # 5. 检查是否是纯符号
            if re.match(r'^[^\w\u4e00-\u9fff]+$', book_title):
                is_suspicious = True
                reasons.append("纯符号")
            
            if is_suspicious:
                suspicious_books.append({
                    'user': user_id,
                    'book': book_title,
                    'reasons': reasons,
                    'reading_status': book['reading_status'],
                    'progress': book['progress']
                })
    
    # 按用户分组显示
    if suspicious_books:
        current_user = None
        for item in sorted(suspicious_books, key=lambda x: x['user']):
            if item['user'] != current_user:
                current_user = item['user']
                print(f"\n用户: {current_user}")
            
            print(f"  可疑书籍: '{item['book']}'")
            print(f"    原因: {', '.join(item['reasons'])}")
            print(f"    状态: {item['reading_status']}, 进度: {item['progress']}")
    else:
        print("✅ 没有发现可疑的书籍名称")
    
    print(f"\n总共发现 {len(suspicious_books)} 个可疑的书籍名称")
    
    # 额外检查：查找可能被遗漏的用户
    print("\n=== 检查可能被遗漏的用户名 ===")
    json_user_ids = {user['user_id'] for user in data['users']}
    
    # 从原始文件中提取完整的用户名
    complete_user_names = []
    for line in lines:
        line = line.strip()
        user_match = user_pattern.match(line)
        if user_match:
            user_number = user_match.group(1)
            user_info = user_match.group(2).strip()
            complete_user_names.append((user_number, user_info))
    
    print("可能被遗漏或处理错误的用户：")
    for number, full_name in complete_user_names:
        # 检查是否有对应的用户在JSON中
        found = False
        for user_id in json_user_ids:
            if user_id in full_name or full_name.startswith(user_id):
                found = True
                break
        
        if not found:
            print(f"  {number}. {full_name}")

if __name__ == "__main__":
    check_suspicious_books()
