#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re

def debug_line_366():
    """
    调试第366行的处理
    """
    line = "76.放之四海而皆准《野性的呼唤》读完"
    
    # 当前使用的正则表达式
    user_pattern = re.compile(r'^\s*(\d+)\.\s*(.+)')
    
    print(f"原始行: {line}")
    
    user_match = user_pattern.match(line)
    if user_match:
        user_number = user_match.group(1)
        user_info = user_match.group(2).strip()
        print(f"匹配成功:")
        print(f"  编号: {user_number}")
        print(f"  用户信息: '{user_info}'")
        
        # 分析用户信息
        parts = user_info.split()
        print(f"  分割后的部分: {parts}")
        
        # 检查是否包含书籍信息
        if '《' in user_info:
            print("  包含书籍信息")
            # 尝试分离用户名和书籍
            book_start = user_info.find('《')
            if book_start > 0:
                potential_user_name = user_info[:book_start]
                book_part = user_info[book_start:]
                print(f"  可能的用户名: '{potential_user_name}'")
                print(f"  书籍部分: '{book_part}'")
        
        # 当前脚本的处理逻辑
        if parts:
            current_user_id = parts[0]  # 这会是 "放之四海而皆准《野性的呼唤》读完"
            print(f"  当前脚本会提取的用户ID: '{current_user_id}'")
    else:
        print("匹配失败")

if __name__ == "__main__":
    debug_line_366()
