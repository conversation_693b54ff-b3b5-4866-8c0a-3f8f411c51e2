#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re


def debug_user_pattern():
    """
    调试用户匹配模式
    """
    # 读取原始文件
    with open('rawdata/202505.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # 正则表达式，用于匹配用户ID
    user_pattern = re.compile(r'^\s*(\d+)\.\s*(.+)')

    # 检查第338行之后的所有行
    for line_num in range(338, len(lines)):
        line = lines[line_num].strip()
        if not line:
            continue

        user_match = user_pattern.match(line)
        if user_match:
            user_number = user_match.group(1)
            user_info = user_match.group(2).strip()
            print(f"第{line_num+1}行: {line}")
            print(f"  匹配成功: 编号={user_number}, 用户信息='{user_info}'")

            # 提取用户ID
            parts = user_info.split()
            if parts:
                user_id = parts[0]
                print(f"  提取的用户ID: '{user_id}'")
            print()
        else:
            # 检查是否看起来像用户行但没有匹配
            if re.match(r'^\s*\d+\.', line):
                print(f"第{line_num+1}行: {line}")
                print(f"  看起来像用户行但没有匹配!")
                print()


if __name__ == "__main__":
    debug_user_pattern()
